package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCppPositionHierarchy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员群体层级关系表DAO接口
 * 提供船员群体层级数据的数据库访问方法
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCppPositionHierarchyDao extends BaseMapper<DwdzCppPositionHierarchy> {

    /**
     * 获取船员总数统计
     * 用于首页船员总数显示
     * 
     * @return 船员总数
     */
    Long getSeafarerTotalCount();

    /**
     * 获取完整的船员群体树形结构数据
     * 用于船员群体分类组件
     * 
     * @return 船员群体树形结构列表
     */
    List<DwdzCppPositionHierarchy> getSeafarerHierarchyTree();

    /**
     * 根据层级ID获取子节点列表
     * 
     * @param parentId 父级层级ID，null表示获取根节点
     * @return 子节点列表
     */
    List<DwdzCppPositionHierarchy> getChildrenByParentId(@Param("parentId") String parentId);

    /**
     * 根据船员类别名称查询层级信息
     * 
     * @param name 船员类别名称
     * @return 层级信息
     */
    DwdzCppPositionHierarchy getHierarchyByName(@Param("name") String name);
} 