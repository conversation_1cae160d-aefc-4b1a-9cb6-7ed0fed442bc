package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 船员履职预警信息主表
 * <AUTHOR> Generation
 */
@Data
@TableName("dwdz_crew_sv_warning_info")
@ApiModel(value = "船员履职预警信息主表")
public class DwdzCrewSvWarningInfo {

    private static final long serialVersionUID = 1L;

    /**
     * 预警信息唯一标识
     */
    @TableId(value = "warning_id")
    @ApiModelProperty(value = "预警信息唯一标识")
    private String warningId;

    /**
     * 关联船员基本信息表的船员ID
     */
    @ApiModelProperty(value = "关联船员基本信息表的船员ID")
    private String crewId;

    /**
     * 预警标题
     */
    @ApiModelProperty(value = "预警标题")
    private String warningTitle;

    /**
     * 预警描述
     */
    @ApiModelProperty(value = "预警描述")
    private String warningDescription;

    /**
     * 预警产生时间
     */
    @ApiModelProperty(value = "预警产生时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date warningDate;

    /**
     * 预警级别(red:红色,yellow:黄色)
     */
    @ApiModelProperty(value = "预警级别(red:红色,yellow:黄色)")
    private String warningLevel;

    /**
     * 是否已读(true:已读,false:未读)
     */
    @ApiModelProperty(value = "是否已读")
    private Boolean isRead;

    /**
     * 触发预警的规则ID（关联cpp_fi_feature_rule表的rule_id）
     */
    @ApiModelProperty(value = "触发预警的规则ID")
    private String triggerRuleId;

    /**
     * 触发预警的规则名称（如多船任职交叉、超期任职等）
     */
    @ApiModelProperty(value = "触发预警的规则名称")
    private String triggerRuleName;

    /**
     * 处理措施说明，例："1. 联系船员本人确认任职情况 2. 核查相关公司的用工合同 3. 检查海员服务簿记录"
     */
    @ApiModelProperty(value = "处理措施说明")
    private String handleInstruction;

    /**
     * 规则计算说明，展示具体业务参数，例："2021-07-01至2024-05-15期间在上海港务集团船舶公司和青岛海洋船舶管理有限公司交叉任职达35个月，超过12个月的安全阈值"
     */
    @ApiModelProperty(value = "规则计算说明")
    private String ruleCalculationDesc;

    /**
     * 触发预警的特征类别ID（关联cpp_fi_rule_category表的category_id），如交叉任职（一人多船）类别的ID
     */
    @ApiModelProperty(value = "触发预警的特征类别ID")
    private String triggerCategoryId;

    /**
     * 触发预警的特征类别名称，如"交叉任职（一人多船）"、"超期任职"等
     */
    @ApiModelProperty(value = "触发预警的特征类别名称")
    private String triggerCategoryName;

    /**
     * 记录创建日期
     */
    @ApiModelProperty(value = "记录创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @ApiModelProperty(value = "记录修改日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recModifyDate;
} 