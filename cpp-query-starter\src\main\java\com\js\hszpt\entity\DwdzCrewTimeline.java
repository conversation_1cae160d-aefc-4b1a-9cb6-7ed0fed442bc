package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员动态履历时间轴宽表：整合所有船员事件的完整生命周期数据
 * 
 * <AUTHOR> Generation
 */
@Data
@TableName("dwdz_crew_timeline")
@ApiModel(value = "船员动态履历时间轴宽表")
public class DwdzCrewTimeline {

    private static final long serialVersionUID = 1L;

    /**
     * 时间轴记录唯一标识
     */
    @TableId(value = "timeline_id")
    @ApiModelProperty(value = "时间轴记录唯一标识")
    private String timelineId;

    /**
     * 关联船员ID
     */
    @ApiModelProperty(value = "关联船员ID")
    private String crewId;

    /**
     * 事件主日期（排序依据）
     */
    @ApiModelProperty(value = "事件主日期（排序依据）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date eventDate;

    /**
     * 事件类型（training/examination/certificate/service/medical/entryexit/supervision/transition）
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    /**
     * 事件子类型（如证书类型、监管类型）
     */
    @ApiModelProperty(value = "事件子类型")
    private String subType;

    /**
     * 关联源表记录ID
     */
    @ApiModelProperty(value = "关联源表记录ID")
    private String refId;

    /**
     * 事件标题（展示用）
     */
    @ApiModelProperty(value = "事件标题（展示用）")
    private String eventTitle;

    /**
     * 事件整体状态
     */
    @ApiModelProperty(value = "事件整体状态")
    private String eventStatus;

    /**
     * 事件发生时船员年龄
     */
    @ApiModelProperty(value = "事件发生时船员年龄")
    private Integer eventAge;

    /**
     * 事件开始日期（持续事件）
     */
    @ApiModelProperty(value = "事件开始日期（持续事件）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 事件结束日期（持续事件）
     */
    @ApiModelProperty(value = "事件结束日期（持续事件）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 记录创建日期
     */
    @ApiModelProperty(value = "记录创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @ApiModelProperty(value = "记录修改日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recModifyDate;

    // 培训相关字段
    @ApiModelProperty(value = "培训结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date trainingEndDate;

    @ApiModelProperty(value = "培训状态")
    private String trainingStatus;

    @ApiModelProperty(value = "培训机构或院校全称")
    private String trainingInstitution;

    @ApiModelProperty(value = "培训班名称")
    private String trainingClassName;

    @ApiModelProperty(value = "培训项目名称")
    private String trainingProject;

    @ApiModelProperty(value = "培训实际开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date trainingStartDate;

    @ApiModelProperty(value = "培训实际完成日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date trainingCompletionDate;

    @ApiModelProperty(value = "培训结果")
    private String trainingResult;

    @ApiModelProperty(value = "培训详情描述")
    private String trainingDetails;

    @ApiModelProperty(value = "培训数据来源")
    private String trainingDataSource;

    @ApiModelProperty(value = "培训海事机构")
    private String trainingMarineAuthority;

    // 考试相关字段
    @ApiModelProperty(value = "考试状态")
    private String examStatus;

    @ApiModelProperty(value = "考试地点")
    private String examLocation;

    @ApiModelProperty(value = "报考/考核职位")
    private String examPosition;

    @ApiModelProperty(value = "考试科目")
    private String examSubjects;

    @ApiModelProperty(value = "实际考试日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date examExamDate;

    @ApiModelProperty(value = "考试成绩结果")
    private String examResult;

    @ApiModelProperty(value = "海事管理机构")
    private String examMarineAuthority;

    @ApiModelProperty(value = "考试场次")
    private String examSession;

    @ApiModelProperty(value = "报名日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date examRegistrationDate;

    @ApiModelProperty(value = "报名结果")
    private String examRegistrationResult;

    @ApiModelProperty(value = "考试详细描述")
    private String examDetails;

    @ApiModelProperty(value = "考试数据来源")
    private String examDataSource;

    @ApiModelProperty(value = "是否补考（0-否 1-是）")
    private String examIsAgain;

    @ApiModelProperty(value = "适任种类代码")
    private String examCompetencyCode;

    @ApiModelProperty(value = "考试期数")
    private String examPeriod;

    // 证书申请相关字段
    @ApiModelProperty(value = "申请状态")
    private String certApplyStatus;

    @ApiModelProperty(value = "申请公司名称")
    private String certApplyCompName;

    @ApiModelProperty(value = "申请形式")
    private String certApplyFormName;

    @ApiModelProperty(value = "证书名称")
    private String certApplyCertificateName;

    @ApiModelProperty(value = "海事管理机构")
    private String certApplyMarineAuthority;

    @ApiModelProperty(value = "证书编号")
    private String certApplyCertificateNumber;

    @ApiModelProperty(value = "申请类型")
    private String certApplyApplicationType;

    @ApiModelProperty(value = "批准日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date certApplyApprovalDate;

    @ApiModelProperty(value = "申请结果")
    private String certApplyApplicationResult;

    @ApiModelProperty(value = "证书申请详细描述")
    private String certApplyDetails;

    @ApiModelProperty(value = "证书申请数据来源")
    private String certApplyDataSource;

    // 服务相关字段
    @ApiModelProperty(value = "服务状态")
    private String serviceStatus;

    @ApiModelProperty(value = "公司名称")
    private String serviceCompanyName;

    @ApiModelProperty(value = "公司标签")
    private String serviceCompanyLabel;

    @ApiModelProperty(value = "服务开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date serviceDate;

    @ApiModelProperty(value = "船舶类型")
    private String serviceVesselType;

    @ApiModelProperty(value = "船舶名称")
    private String serviceVesselName;

    @ApiModelProperty(value = "任职职位")
    private String servicePosition;

    @ApiModelProperty(value = "任命日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date serviceAppointmentDate;

    @ApiModelProperty(value = "任职地点")
    private String serviceLocation;

    @ApiModelProperty(value = "服务详细描述")
    private String serviceDetails;

    @ApiModelProperty(value = "服务数据来源")
    private String serviceDataSource;

    @ApiModelProperty(value = "服务持续天数")
    private Integer serviceDurationDays;

    @ApiModelProperty(value = "服务结束日期（解职时间）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date serviceEndDate;

    @ApiModelProperty(value = "解职地点")
    private String serviceEndLocation;

    @ApiModelProperty(value = "船舶编号")
    private String serviceVesselNumber;

    @ApiModelProperty(value = "兼职标志代码（0：正式职务；1：兼职职务；2：见习职务）")
    private String serviceTraineePosition;

    @ApiModelProperty(value = "船舶总吨，单位：吨")
    private BigDecimal serviceShipTonnage;

    @ApiModelProperty(value = "主机功率，单位：千瓦")
    private BigDecimal serviceShipEnginePower;

    @ApiModelProperty(value = "航行类型（IN_PORT_DOMESTIC/OFF_PORT_DOMESTIC/OVERSEAS）")
    private String serviceNaviCategory;

    @ApiModelProperty(value = "船舶国籍")
    private String serviceVesselNationality;

    @ApiModelProperty(value = "航行方向（境内航行/国际航行等）")
    private String serviceVoyageDirection;

    @ApiModelProperty(value = "资历来源 1-派遣报备（即外轮） 2-国轮")
    private String serviceSource;

    @ApiModelProperty(value = "外派公司名称")
    private String serviceDspCompanyName;

    @ApiModelProperty(value = "外派公司id")
    private String serviceDspCompanyId;

    @ApiModelProperty(value = "船舶初次登记号")
    private String serviceVesselRegno;

    @ApiModelProperty(value = "船舶IMO")
    private String serviceVesselImo;

    @ApiModelProperty(value = "船舶识别号")
    private String serviceVesselId;

    @ApiModelProperty(value = "任职英文船名")
    private String serviceVesselNameEn;

    @ApiModelProperty(value = "船舶等级")
    private String serviceVesselLevel;

    // 体检相关字段
    @ApiModelProperty(value = "体检状态")
    private String medicalStatus;

    @ApiModelProperty(value = "体检医院")
    private String medicalHospital;

    @ApiModelProperty(value = "适用部门")
    private String medicalDepartment;

    @ApiModelProperty(value = "体检日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date medicalExamDate;

    @ApiModelProperty(value = "体检结果")
    private String medicalResult;

    @ApiModelProperty(value = "体检详细描述")
    private String medicalDetails;

    @ApiModelProperty(value = "体检数据来源")
    private String medicalDataSource;

    @ApiModelProperty(value = "体检海事机构")
    private String medicalMarineAuthority;

    // 出入境相关字段
    @ApiModelProperty(value = "出入境状态")
    private String immigrationStatus;

    @ApiModelProperty(value = "目的地国家")
    private String immigrationDestinationCountry;

    @ApiModelProperty(value = "边检单位")
    private String immigrationBorderControl;

    @ApiModelProperty(value = "港口名称")
    private String immigrationPort;

    @ApiModelProperty(value = "出入境时间（含具体时间）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date immigrationEntryExitTime;

    @ApiModelProperty(value = "出入境方向")
    private String immigrationDirection;

    @ApiModelProperty(value = "出入境详细描述")
    private String immigrationDetails;

    @ApiModelProperty(value = "出入境数据来源")
    private String immigrationDataSource;

    // 监管相关字段
    @ApiModelProperty(value = "处理机构")
    private String supervisionAuthority;

    @ApiModelProperty(value = "公司名称")
    private String supervisionCompanyName;

    @ApiModelProperty(value = "船舶名称")
    private String supervisionVesselName;

    @ApiModelProperty(value = "处理日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date supervisionProcessingDate;

    @ApiModelProperty(value = "奖励原因")
    private String supervisionAwardReason;

    @ApiModelProperty(value = "奖励内容")
    private String supervisionAwardContent;

    @ApiModelProperty(value = "记分分值")
    private String supervisionScorePoints;

    @ApiModelProperty(value = "缺陷详情")
    private String supervisionDefectDetails;

    @ApiModelProperty(value = "案号")
    private String supervisionCaseNumber;

    @ApiModelProperty(value = "事件发生日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date supervisionIncidentDate;

    @ApiModelProperty(value = "事件发生地点")
    private String supervisionIncidentLocation;

    @ApiModelProperty(value = "违章地点")
    private String supervisionViolationLocation;

    @ApiModelProperty(value = "处罚决定")
    private String supervisionPenaltyDecision;

    @ApiModelProperty(value = "证书是否被扣留")
    private String supervisionCertificateDetain;

    @ApiModelProperty(value = "监管详细描述")
    private String supervisionDetails;

    @ApiModelProperty(value = "监管数据来源")
    private String supervisionDataSource;

    @ApiModelProperty(value = "监管子状态")
    private String supervisionSubStatus;

    @ApiModelProperty(value = "船舶编号（监管相关）")
    private String supervisionVesselNumber;

    @ApiModelProperty(value = "监督检查编号")
    private String supervisionInspectNo;

    @ApiModelProperty(value = "初查复查标志代码，0：初查；1：复查")
    private String supervisionInitCode;

    // 职业转型相关字段
    @ApiModelProperty(value = "转型状态")
    private String careerChangeStatus;

    @ApiModelProperty(value = "任职公司")
    private String careerChangeCompany;

    @ApiModelProperty(value = "转型身份")
    private String careerChangeNewIdentity;

    @ApiModelProperty(value = "转型生效日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date careerChangeEffectiveDate;

    @ApiModelProperty(value = "转型详细描述")
    private String careerChangeDetails;

    @ApiModelProperty(value = "转型数据来源")
    private String careerChangeDataSource;
}