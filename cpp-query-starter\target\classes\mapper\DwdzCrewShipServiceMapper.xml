<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewShipServiceMapper">

    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewShipService">
        <id column="service_id" property="serviceId"/>
        <result column="crew_id" property="crewId"/>
        <result column="ship_type" property="shipType"/>
        <result column="total_months" property="totalMonths"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
    </resultMap>

    <select id="selectByCrewId" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_ship_service WHERE crew_id = #{crewId} and stat_type = #{statType}
    </select>
</mapper> 