package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员职业生涯成长轨迹表
 * 对应表：dwdz_crew_career_track
 */
@Data
@TableName("dwdz_crew_career_track")
@ApiModel(value = "船员职业生涯成长轨迹表")
public class DwdzCrewCareerTrack {
    /** 成长轨迹记录唯一标识 */
    @TableId
    @ApiModelProperty(value = "成长轨迹记录唯一标识")
    private String trackId;

    /** 关联船员基本信息表的船员ID */
    @ApiModelProperty(value = "关联船员基本信息表的船员ID")
    private String crewId;

    /** 职务名称（如：船长、大副、轮机长等） */
    @ApiModelProperty(value = "职务名称（如：船长、大副、轮机长等）")
    private String positionName;

    /** 取得该职务证书的日期 */
    @ApiModelProperty(value = "取得该职务证书的日期")
    private Date certObtainDate;

    /** 首次履职该职务的日期 */
    @ApiModelProperty(value = "首次履职该职务的日期")
    private Date firstServiceDate;

    /** 晋升至该职务的时长（单位：年，保留一位小数） */
    @ApiModelProperty(value = "晋升至该职务的时长（单位：年，保留一位小数）")
    private BigDecimal promotionDuration;

    /** 同年龄段（±2岁）船员的平均晋升时长（单位：年，保留一位小数） */
    @ApiModelProperty(value = "同年龄段（±2岁）船员的平均晋升时长（单位：年，保留一位小数）")
    private BigDecimal avgPromotionDuration;

    /** 成长轨迹顺序（数值越小表示越早） */
    @ApiModelProperty(value = "成长轨迹顺序（数值越小表示越早）")
    private Integer trackOrder;

    /** 取证时年龄(岁) */
    @ApiModelProperty(value = "取证时年龄(岁)")
    private Integer ageAtObtain;

    /** 晋升到下一职务用时(年)，即展示中的"晋升用时" */
    @ApiModelProperty(value = "晋升到下一职务用时(年)，即展示中的\"晋升用时\"")
    private BigDecimal nextPromotionDuration;

    /** 同龄人样本数量 */
    @ApiModelProperty(value = "同龄人样本数量")
    private Integer peerSampleSize;

    /** 晋升百分位(小数形式，如0.1表示前10%) */
    @ApiModelProperty(value = "晋升百分位(小数形式，如0.1表示前10%)")
    private BigDecimal promotionPercentile;

    /** 记录创建日期 */
    @ApiModelProperty(value = "记录创建日期")
    private Date recCreateDate;

    /** 记录修改日期 */
    @ApiModelProperty(value = "记录修改日期")
    private Date recModifyDate;
} 