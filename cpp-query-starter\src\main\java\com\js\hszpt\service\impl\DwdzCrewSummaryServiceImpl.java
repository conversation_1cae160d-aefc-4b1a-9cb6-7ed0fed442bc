package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewSummary;
import com.js.hszpt.mapper.DwdzCrewSummaryMapper;
import com.js.hszpt.service.DwdzCrewSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 船员职业生涯汇总信息表Service实现类
 * <AUTHOR> Generation
 */
@Slf4j
@Service
public class DwdzCrewSummaryServiceImpl extends ServiceImpl<DwdzCrewSummaryMapper, DwdzCrewSummary> implements DwdzCrewSummaryService {

    @Override
    public DwdzCrewSummary getByCrewId(String crewId) {
        log.info("根据船员ID查询职业生涯汇总信息，船员ID：{}", crewId);
        return this.baseMapper.selectByCrewId(crewId);
    }

    @Override
    public List<DwdzCrewSummary> getByCrewIds(List<String> crewIds) {
        log.info("批量查询船员职业生涯汇总信息，船员ID数量：{}", crewIds.size());
        return this.baseMapper.selectByCrewIds(crewIds);
    }

    @Override
    public Object getCareerAnalysisData(String crewId) {
        log.info("获取船员职业发展统计数据，船员ID：{}", crewId);
        
        // 获取船员职业生涯汇总信息
        DwdzCrewSummary summary = getByCrewId(crewId);
        if (summary == null) {
            log.warn("未找到船员职业生涯汇总信息，船员ID：{}", crewId);
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        
        // 构建职业发展数据
        Map<String, Object> careerData = new HashMap<>();
        careerData.put("totalExperience", summary.getCareerYears() + "年");
        careerData.put("dataNote", "数据来源于船员服务簿和证书记录");
        
        // 构建职位数据（这里需要根据实际业务逻辑补充）
        Map<String, Object> peerComparison = new HashMap<>();
        // 数据库中peer_percentile存储的是百分比值（如85.5表示85.5%），直接转换为整数
        int percentage = summary.getPeerPercentile() != null ? 
            summary.getPeerPercentile().intValue() : 0;
        peerComparison.put("percentage", percentage);
        peerComparison.put("rank", "前" + (100 - percentage) + "%");
        
        careerData.put("peerComparison", peerComparison);
        result.put("careerData", careerData);
        
        // 构建船舶类型数据（这里需要根据实际业务逻辑补充）
        Map<String, Object> shipTypeData = new HashMap<>();
        result.put("shipTypeData", shipTypeData);
        
        // 构建轨迹数据（这里需要根据实际业务逻辑补充）
        Map<String, Object> trajectoryData = new HashMap<>();
        result.put("trajectoryData", trajectoryData);
        
        return result;
    }
} 