package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewSvWarningInfo;
import com.js.hszpt.mapper.DwdzCrewSvWarningInfoMapper;
import com.js.hszpt.service.DwdzCrewSvWarningInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 船员履职预警信息主表Service实现类
 * <AUTHOR> Generation
 */
@Slf4j
@Service
public class DwdzCrewSvWarningInfoServiceImpl extends ServiceImpl<DwdzCrewSvWarningInfoMapper, DwdzCrewSvWarningInfo> implements DwdzCrewSvWarningInfoService {

    @Override
    public List<DwdzCrewSvWarningInfo> getByCrewId(String crewId) {
        log.info("根据船员ID查询预警信息，船员ID：{}", crewId);
        return this.baseMapper.selectByCrewId(crewId);
    }

    @Override
    public List<DwdzCrewSvWarningInfo> getByCrewIdAndLevel(String crewId, String warningLevel) {
        log.info("根据船员ID和预警级别查询预警信息，船员ID：{}，预警级别：{}", crewId, warningLevel);
        return this.baseMapper.selectByCrewIdAndLevel(crewId, warningLevel);
    }

    @Override
    public boolean updateAsRead(String warningId) {
        log.info("更新预警信息为已读状态，预警ID：{}", warningId);
        int result = this.baseMapper.updateAsRead(warningId);
        return result > 0;
    }

    @Override
    public List<Map<String, Object>> getWarnings(String crewId) {
        log.info("获取预警信息，船员ID：{}", crewId);
        
        List<DwdzCrewSvWarningInfo> warningList = getByCrewId(crewId);
        
        return warningList.stream()
                .map(this::convertToWarningMap)
                .collect(Collectors.toList());
    }

    /**
     * 将预警实体转换为前端需要的格式
     */
    private Map<String, Object> convertToWarningMap(DwdzCrewSvWarningInfo warning) {
        Map<String, Object> warningMap = new HashMap<>();
        warningMap.put("id", warning.getWarningId());
        warningMap.put("level", warning.getWarningLevel());
        warningMap.put("type", getWarningType(warning.getTriggerCategoryName()));
        warningMap.put("title", warning.getWarningTitle());
        warningMap.put("description", warning.getWarningDescription());
        warningMap.put("date", new SimpleDateFormat("yyyy-MM-dd").format(warning.getWarningDate()));
        
        // 构建详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("triggerRuleId", warning.getTriggerRuleId());
        details.put("triggerRuleName", warning.getTriggerRuleName());
        details.put("handleInstruction", warning.getHandleInstruction());
        details.put("ruleCalculationDesc", warning.getRuleCalculationDesc());
        details.put("triggerCategoryId", warning.getTriggerCategoryId());
        details.put("triggerCategoryName", warning.getTriggerCategoryName());
        
        // 根据预警类别构建特定的详细信息
        if ("conflict_training".equals(getWarningType(warning.getTriggerCategoryName()))) {
            details.put("conflictType", "training");
            List<Map<String, Object>> conflictItems = new ArrayList<>();
            
            Map<String, Object> item1 = new HashMap<>();
            item1.put("type", "高级船员安全培训");
            item1.put("date", "2024-01-15");
            item1.put("duration", "5天");
            conflictItems.add(item1);
            
            Map<String, Object> item2 = new HashMap<>();
            item2.put("type", "船舶安全管理培训");
            item2.put("date", "2024-01-15");
            item2.put("duration", "3天");
            conflictItems.add(item2);
            
            details.put("conflictItems", conflictItems);
        } else if ("expired_certificate".equals(getWarningType(warning.getTriggerCategoryName()))) {
            details.put("certificateType", "适任证书");
            details.put("expiryDate", "2024-03-15");
            details.put("daysRemaining", 28);
        }
        warningMap.put("details", details);
        
        return warningMap;
    }

    /**
     * 根据预警类别名称获取预警类型
     */
    private String getWarningType(String categoryName) {
        if (categoryName == null) {
            return "unknown";
        }
        
        if (categoryName.contains("交叉任职")) {
            return "conflict_training";
        } else if (categoryName.contains("证书过期")) {
            return "expired_certificate";
        } else if (categoryName.contains("体检冲突")) {
            return "conflict_medical";
        } else if (categoryName.contains("健康证失效")) {
            return "invalid_health";
        } else if (categoryName.contains("安全事故")) {
            return "major_accident";
        } else if (categoryName.contains("证书暂扣")) {
            return "certificate_suspension";
        } else if (categoryName.contains("短时冲突")) {
            return "short_conflict_training";
        } else if (categoryName.contains("出入境延迟")) {
            return "entry_exit_delay";
        } else if (categoryName.contains("配员缺陷")) {
            return "manning_minor_defect";
        } else if (categoryName.contains("重复缺陷")) {
            return "repeated_defect";
        }
        
        return "unknown";
    }
} 