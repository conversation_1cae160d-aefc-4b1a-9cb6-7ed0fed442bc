package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCppPositionPromotionRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 晋升关系表DAO接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCppPositionPromotionRelDao extends BaseMapper<DwdzCppPositionPromotionRel> {

    /**
     * 根据子分类名称获取晋升关系（包含来源和目标）
     */
    List<DwdzCppPositionPromotionRel> getPromotionRelationsBySubcategoryName(@Param("subcategoryName") String subcategoryName);

    /**
     * 根据子分类名称获取晋升关系详情（包含子分类名称）
     */
    List<Map<String, Object>> getPromotionRelationsWithNames(@Param("subcategoryName") String subcategoryName);

    /**
     * 根据子分类ID获取晋升关系详情（新版本，基于ID查询）
     * @param subcategoryId 子分类ID
     * @return 晋升关系详情列表
     */
    List<Map<String, Object>> getPromotionRelationsBySubcategoryId(@Param("subcategoryId") String subcategoryId);
} 