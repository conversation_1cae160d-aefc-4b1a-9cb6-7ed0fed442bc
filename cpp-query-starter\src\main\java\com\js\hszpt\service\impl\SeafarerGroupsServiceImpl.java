package com.js.hszpt.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.js.hszpt.entity.DwdzCppPositionHierarchy;
import com.js.hszpt.mapper.DwdzCppPositionHierarchyDao;
import com.js.hszpt.service.SeafarerGroupsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 船员群体分类服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@DS("dzzzdwdz")
public class SeafarerGroupsServiceImpl implements SeafarerGroupsService {

    @Autowired
    private DwdzCppPositionHierarchyDao positionHierarchyDao;

    @Override
    public Map<String, Object> getSeafarerData() {
        log.info("从数据库获取船员群体分类数据");
        
        try {
            // 获取所有层级数据
            List<DwdzCppPositionHierarchy> allHierarchies = positionHierarchyDao.selectList(null);
            
            if (allHierarchies == null || allHierarchies.isEmpty()) {
                log.warn("数据库中没有船员群体层级数据，返回空数据结构");
                return getEmptySeafarerData();
            }
            
            // 构建树形结构
            Map<String, Object> rootData = buildHierarchyTree(allHierarchies);
            
            log.info("成功从数据库获取船员群体分类数据，根节点数据：{}", rootData.get("name"));
            return rootData;
            
        } catch (Exception e) {
            log.error("从数据库获取船员群体分类数据失败：{}", e.getMessage(), e);
            return getEmptySeafarerData();
        }
    }

    @Override
    public Map<String, Object> getSeafarerColors() {
        log.info("获取颜色配置数据");
        
        Map<String, Object> result = new HashMap<>();
        
        // 群体颜色配置
        Map<String, String> groupColors = new HashMap<>();
        groupColors.put("预备船员", "#f39c12");  // 橙色 - 代表希望和未来
        groupColors.put("注册海员", "#1e3a8a");  // 海事蓝 - 代表现在和专业
        groupColors.put("参照船员管理", "#8e44ad");  // 紫色 - 代表经验和传承
        result.put("groupColors", groupColors);
        
        // 特定区块颜色配置
//        Map<String, String> blockColors = new HashMap<>();
//        blockColors.put("持海船高级船员适任证书", "#2E86AB");      // 深蓝色
//        blockColors.put("持海船普通船员适任证书", "#A23B72");      // 深紫红色
//        blockColors.put("持不参加航行和轮机值班海船船员适任证书", "#F18F01");  // 深橙色
//        result.put("blockColors", blockColors);
        
        log.info("获取颜色配置数据成功");
        return result;
    }

    @Override
    public Map<String, Object> getChartConfig() {
        log.info("获取图表配置数据");
        
        Map<String, Object> result = new HashMap<>();
        
        // Canvas绘制参数
        Map<String, Object> canvas = new HashMap<>();
        canvas.put("defaultRadius", 140);
        canvas.put("defaultInnerRadius", 60);
        canvas.put("minRadiusRatio", 0.35); // 相对于画布最小边的比例
        canvas.put("innerRadiusRatio", 0.15); // 相对于画布最小边的比例
        result.put("canvas", canvas);
        
        // 动画配置
        Map<String, Object> animation = new HashMap<>();
        animation.put("rotationDuration", 800); // 轮盘旋转动画时长
        animation.put("minimumDragDistance", Math.PI / 6); // 最小拖拽距离
        animation.put("particleCount", 30); // 背景粒子数量
        result.put("animation", animation);
        
        // 默认选中的群体索引
        result.put("defaultGroupIndex", 1); // 默认显示现役船员
        
        // 响应式断点
        Map<String, Integer> responsive = new HashMap<>();
        responsive.put("tablet", 1024);
        responsive.put("mobile", 768);
        result.put("responsive", responsive);
        
        // 轮盘显示尺寸配置
        Map<String, Map<String, Integer>> wheelSizes = new HashMap<>();
        
        Map<String, Integer> desktop = new HashMap<>();
        desktop.put("width", 280);
        desktop.put("height", 280);
        wheelSizes.put("desktop", desktop);
        
        Map<String, Integer> tablet = new HashMap<>();
        tablet.put("width", 200);
        tablet.put("height", 200);
        wheelSizes.put("tablet", tablet);
        
        Map<String, Integer> mobile = new HashMap<>();
        mobile.put("width", 180);
        mobile.put("height", 180);
        wheelSizes.put("mobile", mobile);
        
        result.put("wheelSizes", wheelSizes);
        
        log.info("获取图表配置数据成功");
        return result;
    }

    /**
     * 构建层级树形结构
     * 
     * @param hierarchies 层级数据列表
     * @return 树形结构数据
     */
    private Map<String, Object> buildHierarchyTree(List<DwdzCppPositionHierarchy> hierarchies) {
        // 按父级ID分组
        Map<String, List<DwdzCppPositionHierarchy>> groupedByParent = hierarchies.stream()
            .collect(Collectors.groupingBy(h -> h.getParentId() == null ? "" : h.getParentId()));
        
        // 对每个组内的数据按sort_order和name进行排序
        groupedByParent.forEach((key, list) -> {
            list.sort((a, b) -> {
                // 首先按sort_order排序
                Integer sortOrderA = a.getSortOrder() != null ? a.getSortOrder() : Integer.MAX_VALUE;
                Integer sortOrderB = b.getSortOrder() != null ? b.getSortOrder() : Integer.MAX_VALUE;
                int sortOrderComparison = sortOrderA.compareTo(sortOrderB);
                
                // 如果sort_order相同，按name排序
                if (sortOrderComparison == 0) {
                    String nameA = a.getName() != null ? a.getName() : "";
                    String nameB = b.getName() != null ? b.getName() : "";
                    return nameA.compareTo(nameB);
                }
                
                return sortOrderComparison;
            });
        });
        
        // 找到根节点（没有父级的）
        List<DwdzCppPositionHierarchy> rootNodes = groupedByParent.get("");
        
        if (rootNodes == null || rootNodes.isEmpty()) {
            log.warn("没有找到根节点，返回空数据结构");
            return getEmptySeafarerData();
        }
        
        // 选择第一个根节点作为主要节点
        DwdzCppPositionHierarchy rootNode = rootNodes.get(0);
        
        // 构建树形结构
        Map<String, Object> rootData = convertToTreeData(rootNode, groupedByParent);
        
        return rootData;
    }

    /**
     * 递归构建树形数据
     * 
     * @param node 当前节点
     * @param groupedByParent 按父级分组的数据
     * @return 树形数据
     */
    private Map<String, Object> convertToTreeData(DwdzCppPositionHierarchy node, 
                                                 Map<String, List<DwdzCppPositionHierarchy>> groupedByParent) {
        Map<String, Object> treeData = new HashMap<>();
        
        treeData.put("name", node.getName());
        treeData.put("value", node.getValue() != null ? node.getValue() : 0);
        
        // 添加排序序号到返回数据中（可选，用于前端调试）
        if (node.getSortOrder() != null) {
            treeData.put("sortOrder", node.getSortOrder());
        }
        
        // 添加节点展示方向字段
        if (node.getDisplayDirection() != null) {
            treeData.put("displayDirection", node.getDisplayDirection());
        } else {
            // 如果数据库没有设置方向，设置默认值为右侧
            treeData.put("displayDirection", "right");
        }
        
        // 添加可选字段
        if (node.getCertifiedCount() != null) {
            treeData.put("certifiedCount", node.getCertifiedCount());
        }
        if (node.getDemandCount() != null) {
            treeData.put("demandCount", node.getDemandCount());
        }
        if (node.getOnboardCount() != null) {
            treeData.put("onboardCount", node.getOnboardCount());
        }
        if (node.getDescription() != null) {
            treeData.put("description", node.getDescription());
        }

        // 添加新增的群体介绍和统计说明字段
        if (node.getGroupIntroduction() != null) {
            treeData.put("groupIntroduction", node.getGroupIntroduction());
        }
        if (node.getStaticIntroduction() != null) {
            treeData.put("staticIntroduction", node.getStaticIntroduction());
        }
        
        // 查找子节点（子节点已经在buildHierarchyTree方法中排序过了）
        List<DwdzCppPositionHierarchy> children = groupedByParent.get(node.getHierarchyId());
        if (children != null && !children.isEmpty()) {
            List<Map<String, Object>> childrenData = new ArrayList<>();
            for (DwdzCppPositionHierarchy child : children) {
                childrenData.add(convertToTreeData(child, groupedByParent));
            }
            treeData.put("children", childrenData);
        }
        
        return treeData;
    }

    /**
     * 获取空的船员数据结构（当数据库无数据或异常时使用）
     * 
     * @return 空的船员数据结构
     */
    private Map<String, Object> getEmptySeafarerData() {
        log.info("返回空的船员数据结构");
        
        Map<String, Object> rootData = new HashMap<>();
        rootData.put("name", "船员");
        rootData.put("value", 0);
        rootData.put("description", "暂无船员数据");
        rootData.put("children", new ArrayList<>());
        
        return rootData;
    }


} 