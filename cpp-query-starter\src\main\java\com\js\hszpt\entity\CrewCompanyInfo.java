package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 船员关联公司基本信息表
 */
@Data
@TableName("dwdz_crew_company_info")
public class CrewCompanyInfo {
    /**
     * 公司唯一识别ID
     */
    @TableId("company_id")
    private String companyId;

    /**
     * 公司全称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 公司简称
     */
    @TableField("company_short_name")
    private String companyShortName;

    /**
     * 公司注册地址
     */
    @TableField("address")
    private String address;

    /**
     * 公司联系电话
     */
    @TableField("contact")
    private String contact;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
} 