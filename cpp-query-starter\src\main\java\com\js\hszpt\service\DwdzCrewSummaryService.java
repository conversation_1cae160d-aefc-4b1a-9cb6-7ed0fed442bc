package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.hszpt.entity.DwdzCrewSummary;

import java.util.List;

/**
 * 船员职业生涯汇总信息表Service接口
 * <AUTHOR> Generation
 */
public interface DwdzCrewSummaryService extends IService<DwdzCrewSummary> {

    /**
     * 根据船员ID查询职业生涯汇总信息
     * @param crewId 船员ID
     * @return 职业生涯汇总信息
     */
    DwdzCrewSummary getByCrewId(String crewId);

    /**
     * 批量查询船员职业生涯汇总信息
     * @param crewIds 船员ID列表
     * @return 职业生涯汇总信息列表
     */
    List<DwdzCrewSummary> getByCrewIds(List<String> crewIds);

    /**
     * 获取船员职业发展统计数据
     * @param crewId 船员ID
     * @return 职业发展统计数据
     */
    Object getCareerAnalysisData(String crewId);
} 