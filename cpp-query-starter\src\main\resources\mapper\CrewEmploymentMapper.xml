<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CrewEmploymentMapper">

    <!-- 根据船员ID查询任职关系 -->
    <select id="selectByCrewId" resultType="com.js.hszpt.entity.CrewEmployment">
        SELECT 
            employment_id,
            crew_id,
            company_id,
            company_type,
            start_date,
            end_date,
            employment_status,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_employment
        WHERE crew_id = #{crewId}
        ORDER BY start_date DESC
    </select>

    <!-- 获取船员公司关系统计信息 -->
    <select id="getCompanyRelationsStatistics" resultType="java.util.Map">
        SELECT
            COALESCE(COUNT(DISTINCT company_id), 0) as "totalCompanies",
            COALESCE(CAST(SUM(
                EXTRACT(YEAR FROM AGE(COALESCE(end_date, CURRENT_DATE), start_date)) +
                EXTRACT(MONTH FROM AGE(COALESCE(end_date, CURRENT_DATE), start_date)) / 12.0
            ) AS NUMERIC(10,1)), 0) as "totalYears",
            COALESCE(COUNT(DISTINCT CASE WHEN end_date IS NULL THEN company_id END), 0) as "currentCompanies"
        FROM dwdz_crew_employment
        WHERE crew_id = #{crewId}
    </select>

    <!-- 根据船员ID和公司类型查询任职关系 -->
    <select id="selectByCrewIdAndCompanyTypes" resultType="com.js.hszpt.entity.CrewEmployment">
        SELECT 
            e.employment_id,
            e.crew_id,
            e.company_id,
            e.company_type,
            e.start_date,
            e.end_date,
            e.employment_status,
            e.rec_create_date,
            e.rec_modify_date
        FROM dwdz_crew_employment e
        WHERE e.crew_id = #{crewId}
        <if test="companyTypes != null and companyTypes.size() > 0">
            AND e.company_type IN
            <foreach collection="companyTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        ORDER BY e.start_date DESC
    </select>

</mapper> 