<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewDefectRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewDefectRecord">
        <id column="defect_id" property="defectId" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
        <result column="inspect_no" property="inspectNo" jdbcType="VARCHAR"/>
        <result column="original_id" property="originalId" jdbcType="VARCHAR"/>
        <result column="seq_no" property="seqNo" jdbcType="DECIMAL"/>
        <result column="defect_code" property="defectCode" jdbcType="VARCHAR"/>
        <result column="defect_desc" property="defectDesc" jdbcType="VARCHAR"/>
        <result column="defect_desc_en" property="defectDescEn" jdbcType="VARCHAR"/>
        <result column="process_result_code" property="processResultCode" jdbcType="VARCHAR"/>
        <result column="process_result_desc" property="processResultDesc" jdbcType="VARCHAR"/>
        <result column="correct_flag_code" property="correctFlagCode" jdbcType="VARCHAR"/>
        <result column="correct_flag_name" property="correctFlagName" jdbcType="VARCHAR"/>
        <result column="defect_status_code" property="defectStatusCode" jdbcType="VARCHAR"/>
        <result column="defect_status_name" property="defectStatusName" jdbcType="VARCHAR"/>
        <result column="defect_conv_code" property="defectConvCode" jdbcType="VARCHAR"/>
        <result column="defect_conv_desc" property="defectConvDesc" jdbcType="VARCHAR"/>
        <result column="action_other" property="actionOther" jdbcType="VARCHAR"/>
        <result column="defect_ro_code" property="defectRoCode" jdbcType="VARCHAR"/>
        <result column="defect_ro_name" property="defectRoName" jdbcType="VARCHAR"/>
        <result column="if_relate_inspect" property="ifRelateInspect" jdbcType="VARCHAR"/>
        <result column="enforce_basis" property="enforceBasis" jdbcType="VARCHAR"/>
        <result column="if_find_problem" property="ifFindProblem" jdbcType="VARCHAR"/>
        <result column="inspect_content" property="inspectContent" jdbcType="VARCHAR"/>
        <result column="inspect_result" property="inspectResult" jdbcType="VARCHAR"/>
        <result column="init_inspect_no" property="initInspectNo" jdbcType="VARCHAR"/>
        <result column="apcis_no" property="apcisNo" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_date" property="createDate" jdbcType="DATE"/>
        <result column="operate_date" property="operateDate" jdbcType="DATE"/>
        <result column="creator_id" property="creatorId" jdbcType="VARCHAR"/>
        <result column="rec_create_date" property="recCreateDate" jdbcType="TIMESTAMP"/>
        <result column="rec_modify_date" property="recModifyDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        defect_id, source_type, inspect_no, original_id, seq_no, defect_code, defect_desc, defect_desc_en,
        process_result_code, process_result_desc, correct_flag_code, correct_flag_name, defect_status_code, 
        defect_status_name, defect_conv_code, defect_conv_desc, action_other, defect_ro_code, defect_ro_name,
        if_relate_inspect, enforce_basis, if_find_problem, inspect_content, inspect_result, init_inspect_no,
        apcis_no, remark, create_date, operate_date, creator_id, rec_create_date, rec_modify_date
    </sql>

    <!-- 根据检查编号查询缺陷记录 -->
    <select id="selectByInspectNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_defect_record
        WHERE inspect_no = #{inspectNo,jdbcType=VARCHAR}
        ORDER BY seq_no ASC, rec_create_date ASC
    </select>

    <!-- 根据检查编号列表批量查询缺陷记录 -->
    <select id="selectByInspectNoList" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_defect_record
        WHERE inspect_no IN
        <foreach collection="inspectNoList" item="inspectNo" open="(" separator="," close=")">
            #{inspectNo,jdbcType=VARCHAR}
        </foreach>
        ORDER BY inspect_no ASC, seq_no ASC, rec_create_date ASC
    </select>

    <!-- 根据来源类型和检查编号查询缺陷记录 -->
    <select id="selectBySourceTypeAndInspectNo" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_defect_record
        WHERE source_type = #{sourceType,jdbcType=VARCHAR}
        AND inspect_no = #{inspectNo,jdbcType=VARCHAR}
        ORDER BY seq_no ASC, rec_create_date ASC
    </select>

</mapper> 