package com.js.hszpt.job;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.js.hszpt.entity.CrewAnnualReport;
import com.js.hszpt.service.AnnualReportService;
import com.js.hszpt.utils.MessageUtil;
import com.js.hszpt.vo.MessageRequest;
import com.js.hszpt.vo.UserPushResult;
import com.js.redis.starter.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class MessageJob {

    @Value("${job.message.enable:false}")
    private boolean enable;

    @Value("${job.message.tokenUrl}")
    private String tokenUrl;

    @Value("${job.message.pushUrl}")
    private String pushUrl;

    @Value("${job.message.encryptionKey}")
    private String encryptionKey;

    private final static String MESSAGE_TOKEN_KEY = "MESSAGE_TOKEN";

    @Autowired
    private AnnualReportService annualReportService;

    @Autowired
    private RedisUtil redisUtil;

    @Scheduled(cron = "${job.message.cron}")
    public void execute() {
        if (!enable) {
            return;
        }
        long start = System.currentTimeMillis();
        log.info("【消息推送】开始执行推送消息任务:{}",start);
        // 1. 获取需要推送的消息
        List<CrewAnnualReport> pushMessageList = annualReportService.getPushMessageList();
        if (CollUtil.isEmpty(pushMessageList)) {
            log.info("没有需要推送的消息");
            return;
        }
        log.info("需要推送的消息数：{}",pushMessageList.size());
        // 2.获取推送票据
        String accessTicket = (String) redisUtil.get(MESSAGE_TOKEN_KEY);
        if (StrUtil.isBlank(accessTicket)) {
            accessTicket = MessageUtil.getSysAccessTicket(tokenUrl, "加密后的票据获取参数");
            redisUtil.set(MESSAGE_TOKEN_KEY, accessTicket, 3540);
        }
        List<CrewAnnualReport> resultList = new ArrayList<>();
        // 3. 推送消息
        for (CrewAnnualReport crewAnnualReport : pushMessageList) {
            MessageRequest messageRequest = buildMessageRequest(crewAnnualReport);
            List<UserPushResult> pushResults = MessageUtil.pushMessage(pushUrl, accessTicket, messageRequest, encryptionKey);
            CrewAnnualReport result = new CrewAnnualReport();
            result.setRecModifyDateStr(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            // 处理推送结果
            handleUserPushResult(pushResults, result);
            resultList.add(result);
        }
        // 4. 更新已推送的消息
        if (CollUtil.isNotEmpty(resultList)) {
            annualReportService.updateBatchById(resultList);
        }
        log.info("【消息推送】推送消息任务执行完毕，耗时：{}ms",System.currentTimeMillis() - start);
    }

    public MessageRequest buildMessageRequest(CrewAnnualReport crewAnnualReport) {
        MessageRequest messageRequest = new MessageRequest();
        return messageRequest;
    }

    public void handleUserPushResult(List<UserPushResult> pushResults, CrewAnnualReport result) {
        if (CollUtil.isEmpty(pushResults)) {
            result.setMobilePushStatus("2");
            return;
        }
        UserPushResult userPushResult = pushResults.get(0);
        if (StrUtil.equals("1", userPushResult.getSuccess())) {
            result.setMobilePushStatus("1");
        } else {
            log.warn("【消息推送】推送失败:{}", userPushResult.getUserFlag());
            result.setMobilePushStatus("2");
        }
    }
}
