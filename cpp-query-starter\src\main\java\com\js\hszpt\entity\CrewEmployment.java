package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 船员任职关系信息表
 */
@Data
@TableName("dwdz_crew_employment")
public class CrewEmployment {
    /**
     * 任职关系唯一ID
     */
    @TableId("employment_id")
    private String employmentId;

    /**
     * 船员ID（关联dwdz_crew_basic_info）
     */
    @TableField("crew_id")
    private String crewId;

    /**
     * 公司ID（关联dwdz_crew_company_info）
     */
    @TableField("company_id")
    private String companyId;

    /**
     * 公司关系类型代码（management/operation/owner/dispatch）
     */
    @TableField("company_type")
    private String companyType;

    /**
     * 任职开始日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 任职结束日期（null表示至今在职）
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 任职状态（active/inactive）
     */
    @TableField("employment_status")
    private String employmentStatus;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
} 