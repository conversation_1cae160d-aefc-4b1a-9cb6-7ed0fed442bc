<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCppPositionPromotionReqDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCppPositionPromotionReq">
        <id column="requirement_id" property="requirementId" />
        <result column="dq_subcategory_id" property="dqSubcategoryId" />
        <result column="mb_subcategory_id" property="mbSubcategoryId" />
        <result column="cert_type" property="certType" />
        <result column="kskmlb" property="kskmlb" />
        <result column="pxhgzxmxz" property="pxhgzxmxz" />
        <result column="sqtjjbnr" property="sqtjjbnr" />
        <result column="sqtjbcnr" property="sqtjbcnr" />
        <result column="sqtjfj" property="sqtjfj" />
        <result column="bz" property="bz" />
        <result column="relation_type" property="relationType" />
        <result column="zssqxxzsmc" property="zssqxxzsmc" />
        <result column="zssqtjlx" property="zssqtjlx" />
        <result column="zssqtjlxmczs" property="zssqtjlxmczs" />
        <result column="zwjsgxbzxx" property="zwjsgxbzxx" />
        <result column="sort_order" property="sortOrder" />
        <result column="rec_create_date" property="recCreateDate" />
        <result column="rec_modify_date" property="recModifyDate" />
        <result column="dq_subcategory_name" property="dqSubcategoryName" />
        <result column="mb_subcategory_name" property="mbSubcategoryName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        requirement_id, dq_subcategory_id, mb_subcategory_id, cert_type, kskmlb, pxhgzxmxz,
        sqtjjbnr, sqtjbcnr, sqtjfj, bz, relation_type, zssqxxzsmc, zssqtjlx, zssqtjlxmczs,
        zwjsgxbzxx, sort_order, rec_create_date, rec_modify_date
    </sql>

    <!-- ==================== 兼容旧版本的查询方法（已废弃） ==================== -->

    <!-- 根据子分类ID查询晋升要求（废弃方法，保留向后兼容） -->
    <select id="getRequirementBySubcategoryId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dwdz_crew_position_promo_req 
        WHERE dq_subcategory_id = #{subcategoryId} OR mb_subcategory_id = #{subcategoryId}
        LIMIT 1
    </select>

    <!-- 根据目标职位查询晋升要求（废弃方法，保留向后兼容） -->
    <select id="getRequirementByTargetPosition" resultMap="BaseResultMap">
        SELECT req.*, sub.flxxmc as "dq_subcategory_name"
        FROM dwdz_crew_position_promo_req req
        LEFT JOIN dwdz_crew_position_subcategory sub ON req.dq_subcategory_id = sub.subcategory_id
        WHERE req.mb_subcategory_id IN (
            SELECT subcategory_id FROM dwdz_crew_position_subcategory
            WHERE flxxmc = #{targetPosition}
        )
        LIMIT 1
    </select>

    <!-- 根据子分类名称查询晋升要求（废弃方法，保留向后兼容） -->
    <select id="getRequirementBySubcategoryName" resultMap="BaseResultMap">
        SELECT req.*, dq_sub.flxxmc as "dq_subcategory_name", mb_sub.flxxmc as "mb_subcategory_name"
        FROM dwdz_crew_position_promo_req req
        LEFT JOIN dwdz_crew_position_subcategory dq_sub ON req.dq_subcategory_id = dq_sub.subcategory_id
        LEFT JOIN dwdz_crew_position_subcategory mb_sub ON req.mb_subcategory_id = mb_sub.subcategory_id
        WHERE dq_sub.flxxmc = #{subcategoryName} OR mb_sub.flxxmc = #{subcategoryName}
        LIMIT 1
    </select>

    <!-- ==================== 新版本的查询方法 ==================== -->

    <!-- 根据当前职务ID和目标职务ID查询晋升要求列表 -->
    <select id="getRequirementsByIds" resultMap="BaseResultMap">
        SELECT
            req.*,
            dq_sub.flxxmc as "dq_subcategory_name",
            mb_sub.flxxmc as "mb_subcategory_name"
        FROM dwdz_crew_position_promo_req req
        LEFT JOIN dwdz_crew_position_subcategory dq_sub ON req.dq_subcategory_id = dq_sub.subcategory_id
        LEFT JOIN dwdz_crew_position_subcategory mb_sub ON req.mb_subcategory_id = mb_sub.subcategory_id
        WHERE req.dq_subcategory_id = #{dqSubcategoryId}
          AND req.mb_subcategory_id = #{mbSubcategoryId}
        ORDER BY req.zssqtjlx ASC, req.sort_order ASC, req.rec_create_date DESC
    </select>

    <!-- 根据当前职务ID查询所有相关的晋升要求 -->
    <select id="getRequirementsBySubcategoryId" resultMap="BaseResultMap">
        SELECT
            req.*,
            dq_sub.flxxmc as "dq_subcategory_name",
            mb_sub.flxxmc as "mb_subcategory_name"
        FROM dwdz_crew_position_promo_req req
        LEFT JOIN dwdz_crew_position_subcategory dq_sub ON req.dq_subcategory_id = dq_sub.subcategory_id
        LEFT JOIN dwdz_crew_position_subcategory mb_sub ON req.mb_subcategory_id = mb_sub.subcategory_id
        WHERE req.dq_subcategory_id = #{subcategoryId}
           OR req.mb_subcategory_id = #{subcategoryId}
        ORDER BY req.zssqtjlx ASC, req.sort_order ASC, req.rec_create_date DESC
    </select>

</mapper> 