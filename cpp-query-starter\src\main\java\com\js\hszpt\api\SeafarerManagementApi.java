package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.service.SeafarerIndexService;
import com.js.hszpt.service.CareerStructureService;
import com.js.hszpt.service.CertificatePathService;
import com.js.hszpt.service.SeafarerGroupsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 船员管理系统统一API入口
 * 包含首页、职业结构、证书路径、船员群体分类等15个核心接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/seafarer")
public class SeafarerManagementApi {

    @Autowired
    private SeafarerIndexService seafarerIndexService;

    @Autowired
    private CareerStructureService careerStructureService;

    @Autowired
    private CertificatePathService certificatePathService;

    @Autowired
    private SeafarerGroupsService seafarerGroupsService;

    // ==================== 首页相关接口 ====================

    /**
     * 1. 获取船员总数
     * GET /api/index/seafarer-count
     */
    @GetMapping("/index/seafarer-count")
    public Result getSeafarerCount() {
        try {
            log.info("开始获取船员总数统计");
            Map<String, Object> result = seafarerIndexService.getSeafarerCount();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取船员总数统计失败", e);
            return ResultUtil.error("获取船员总数统计失败");
        }
    }

    /**
     * 1.1. 根据职务级别获取职务名称列表
     * GET /api/positions/by-level?positionLevel={positionLevel}
     */
    @GetMapping("/positions/by-level")
    public Result getPositionsByLevel(@RequestParam String positionLevel) {
        try {
            log.info("开始获取{}级别的职务名称列表", positionLevel);
            Map<String, Object> result = careerStructureService.getPositionsByLevel(positionLevel);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取{}级别职务名称列表失败", positionLevel, e);
            return ResultUtil.error("获取职务名称列表失败");
        }
    }

    // ==================== 船员职业结构相关接口 ====================

    /**
     * 2. 获取职业路径数据
     * GET /api/career/paths
     */
    @GetMapping("/career/paths")
    public Result getCareerPaths() {
        try {
            log.info("开始获取职业路径数据");
            Map<String, Object> result = careerStructureService.getCareerPaths();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取职业路径数据失败", e);
            return ResultUtil.error("获取职业路径数据失败");
        }
    }

    /**
     * 3. 获取管理关系数据
     * GET /api/career/management-relations
     */
    @GetMapping("/career/management-relations")
    public Result getManagementRelations() {
        try {
            log.info("开始获取管理关系数据");
            Map<String, Object> result = careerStructureService.getManagementRelations();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取管理关系数据失败", e);
            return ResultUtil.error("获取管理关系数据失败");
        }
    }

    /**
     * 4. 获取职位详情
     * GET /api/career/position/{positionId}
     */
    @GetMapping("/career/position/{positionId}")
    public Result getPositionDetails(@PathVariable String positionId) {
        try {
            log.info("开始获取职位详情，职位ID：{}", positionId);
            Map<String, Object> result = careerStructureService.getPositionDetails(positionId);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取职位详情失败，职位ID：{}，错误：{}", positionId, e.getMessage());
            return ResultUtil.error("获取职位详情失败");
        }
    }

    /**
     * 5. 获取晋升关系数据
     * GET /api/career/promotion-relations/{subcategoryId}
     */
    @GetMapping("/career/promotion-relations/{subcategoryId}")
    public Result getPromotionRelations(@PathVariable String subcategoryId) {
        try {
            // 手动解码URL编码的中文字符
            String decodedSubcategoryId = java.net.URLDecoder.decode(subcategoryId, "UTF-8");
            log.info("开始获取晋升关系数据，子分类ID：{}", decodedSubcategoryId);
            Map<String, Object> result = careerStructureService.getPromotionRelationsBySubcategoryId(decodedSubcategoryId);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取晋升关系数据失败，子分类ID：{}，错误：{}", subcategoryId, e.getMessage());
            return ResultUtil.error("获取晋升关系数据失败");
        }
    }

    /**
     * 6. 获取晋升要求详情
     * GET /api/career/promotion-requirements/{targetPosition}
     */
    @GetMapping("/career/promotion-requirements/{targetPosition}")
    public Result getPromotionRequirements(@PathVariable String targetPosition) {
        try {
            // 手动解码URL编码的中文字符
            String decodedTargetPosition = java.net.URLDecoder.decode(targetPosition, "UTF-8");
            log.info("开始获取晋升要求详情，目标职位：{}", decodedTargetPosition);
            Map<String, Object> result = careerStructureService.getPromotionRequirements(decodedTargetPosition);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取晋升要求详情失败，目标职位：{}，错误：{}", targetPosition, e.getMessage());
            return ResultUtil.error("获取晋升要求详情失败");
        }
    }

    /**
     * 6.1. 根据当前职务ID和目标职务ID获取晋升要求详情（新版本）
     * GET /api/career/promotion-requirements-by-ids?dqSubcategoryId={dqSubcategoryId}&mbSubcategoryId={mbSubcategoryId}
     */
    @GetMapping("/career/promotion-requirements-by-ids")
    public Result getPromotionRequirementsByIds(
            @RequestParam String dqSubcategoryId, 
            @RequestParam String mbSubcategoryId,
            @RequestParam(required = false, defaultValue = "intranet") String source) {
        try {
            log.info("开始获取晋升要求详情，当前职务ID：{}，目标职务ID：{}，请求来源：{}", dqSubcategoryId, mbSubcategoryId, source);
            Map<String, Object> result = careerStructureService.getPromotionRequirementsByIds(dqSubcategoryId, mbSubcategoryId, source);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取晋升要求详情失败，当前职务ID：{}，目标职务ID：{}，请求来源：{}，错误：{}", 
                     dqSubcategoryId, mbSubcategoryId, source, e.getMessage());
            return ResultUtil.error("获取晋升要求详情失败");
        }
    }

    /**
     * 7. 获取职务人数统计
     * GET /api/career/position-counts
     */
    @GetMapping("/career/position-counts")
    public Result getPositionCounts() {
        try {
            log.info("开始获取职务人数统计");
            Map<String, Integer> result = careerStructureService.getPositionCounts();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取职务人数统计失败，错误：{}", e.getMessage());
            return ResultUtil.error("获取职务人数统计失败");
        }
    }

    /**
     * 8. 获取单个职务人数
     * GET /api/career/position-count/{positionId}
     */
    @GetMapping("/career/position-count/{positionId}")
    public Result getPositionCount(@PathVariable String positionId) {
        try {
            log.info("开始获取单个职务人数，职位ID：{}", positionId);
            Map<String, Object> result = careerStructureService.getPositionCount(positionId);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取单个职务人数失败，职位ID：{}，错误：{}", positionId, e.getMessage());
            return ResultUtil.error("获取单个职务人数失败");
        }
    }

    /**
     * 9. 获取路径选择器选项
     * GET /api/career/path-selector-options
     */
    @GetMapping("/career/path-selector-options")
    public Result getPathSelectorOptions() {
        try {
            log.info("开始获取路径选择器选项");
            Object result = careerStructureService.getPathSelectorOptions();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取路径选择器选项失败，错误：{}", e.getMessage());
            return ResultUtil.error("获取路径选择器选项失败");
        }
    }

    // ==================== 证书路径相关接口 ====================

    /**
     * 10. 获取证书路径数据
     * GET /api/certificate/path/{certificateType}
     */
    @GetMapping("/certificate/path/{certificateType}")
    public Result getCertificatePath(@PathVariable String certificateType) {
        try {
            // 手动解码URL编码的中文字符
            String decodedCertificateType = java.net.URLDecoder.decode(certificateType, "UTF-8");
            log.info("开始获取证书路径数据，证书类型：{}", decodedCertificateType);
            Map<String, Object> result = certificatePathService.getCertificatePath(decodedCertificateType);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取证书路径数据失败，证书类型：{}，错误：{}", certificateType, e.getMessage());
            return ResultUtil.error("获取证书路径数据失败");
        }
    }

    /**
     * 10.1. 获取证书路径数据（平铺查询+Java构建树）
     * GET /api/certificate/path-flat/{certificateType}
     */
    @GetMapping("/certificate/path-flat/{certificateType}")
    public Result getCertificatePathFlat(@PathVariable String certificateType) {
        try {
            // 手动解码URL编码的中文字符
            String decodedCertificateType = java.net.URLDecoder.decode(certificateType, "UTF-8");
            log.info("开始获取证书路径数据（平铺查询），证书类型：{}", decodedCertificateType);
            Map<String, Object> result = certificatePathService.getCertificatePathFlat(decodedCertificateType);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取证书路径数据（平铺查询）失败，证书类型：{}，错误：{}", certificateType, e.getMessage());
            return ResultUtil.error("获取证书路径数据失败");
        }
    }

    /**
     * 11. 搜索证书节点
     * GET /api/certificate/search?keyword={keyword}&limit={limit}
     */
    @GetMapping("/certificate/search")
    public Result searchCertificateNodes(@RequestParam String keyword,
                                       @RequestParam(defaultValue = "20") Integer limit) {
        try {
            log.info("开始搜索证书节点，关键词：{}，限制数量：{}", keyword, limit);
            Map<String, Object> result = certificatePathService.searchCertificateNodes(keyword, limit);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("搜索证书节点失败，关键词：{}，错误：{}", keyword, e.getMessage());
            return ResultUtil.error("搜索证书节点失败");
        }
    }

    /**
     * 12. 获取证书晋升要求详情
     * POST /api/certificate/promotion-requirements
     */
    @PostMapping("/certificate/promotion-requirements")
    public Result getCertificatePromotionRequirements(@RequestBody Map<String, Object> requestData) {
        try {
            log.info("开始获取证书晋升要求详情，请求数据：{}", requestData);
            Map<String, Object> result = certificatePathService.getCertificatePromotionRequirements(requestData);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取证书晋升要求详情失败，错误：{}", e.getMessage());
            return ResultUtil.error("获取证书晋升要求详情失败");
        }
    }

    /**
     * 13. 获取证书类型列表
     * GET /api/certificate/types
     */
    @GetMapping("/certificate/types")
    public Result getCertificateTypes() {
        try {
            log.info("开始获取证书类型列表");
            Map<String, Object> result = certificatePathService.getCertificateTypes();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取证书类型列表失败，错误：{}", e.getMessage());
            return ResultUtil.error("获取证书类型列表失败");
        }
    }

    // ==================== 船员群体分类相关接口 ====================

    /**
     * 13. 获取船员数据
     * GET /api/seafarer/data
     */
    @GetMapping("/seafarer/data")
    public Result getSeafarerData() {
        try {
            log.info("开始获取船员数据");
            Map<String, Object> result = seafarerGroupsService.getSeafarerData();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取船员数据失败，错误：{}", e.getMessage());
            return ResultUtil.error("获取船员数据失败");
        }
    }

    /**
     * 14. 获取颜色配置
     * GET /api/seafarer/colors
     */
    @GetMapping("/seafarer/colors")
    public Result getSeafarerColors() {
        try {
            log.info("开始获取颜色配置");
            Map<String, Object> result = seafarerGroupsService.getSeafarerColors();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取颜色配置失败，错误：{}", e.getMessage());
            return ResultUtil.error("获取颜色配置失败");
        }
    }

    /**
     * 15. 获取图表配置
     * GET /api/seafarer/chart-config
     */
    @GetMapping("/seafarer/chart-config")
    public Result getChartConfig() {
        try {
            log.info("开始获取图表配置");
            Map<String, Object> result = seafarerGroupsService.getChartConfig();
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取图表配置失败，错误：{}", e.getMessage());
            return ResultUtil.error("获取图表配置失败");
        }
    }
} 