package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 船员任职期内船舶服务信息表
 */
@Data
@TableName("dwdz_crew_emp_ship_service")
public class CrewEmpShipService {
    /**
     * 服务记录唯一ID
     */
    @TableId("service_id")
    private String serviceId;

    /**
     * 任职关系ID（关联dwdz_crew_employment）
     */
    @TableField("employment_id")
    private String employmentId;

    /**
     * 船舶名称
     */
    @TableField("ship_name")
    private String shipName;

    /**
     * 任职岗位
     */
    @TableField("position")
    private String position;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
} 