package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 证书路径表实体类 - 新版本
 * 对应数据库表 dwdz_crew_certificate_path
 * 
 * <AUTHOR>
 * @since 2024-12-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_certificate_path")
public class DwdzCrewCertificatePath {

    /**
     * 节点id
     */
    @TableId(value = "node_id", type = IdType.INPUT)
    private String nodeId;

    /**
     * 证书类别
     * 0(海船船员适任证书) 2(海上游艇操作人员) 5(培训合格证)
     */
    @TableField("cert_type")
    private String certType;

    /**
     * 节点名称
     */
    @TableField("node_name")
    private String nodeName;

    /**
     * 节点类型
     * 0:证书职务 1:普通分类
     */
    @TableField("node_type")
    private String nodeType;

    /**
     * 申请关系形式
     * 0：职务晋升 1：学生考试发证 2：航区扩大 3:功率提高 4：军转海 5:内转海 6:渔转海 
     * 7:再有效 8:职务签证 10:吨位提高 11:取消限制 9:初次申请(游艇) 12:变更范围(游艇)
     */
    @TableField("relation_type")
    private Integer relationType;

    /**
     * 申请关系形式名称
     */
    @TableField("sqxszsmc")
    private String sqxszsmc;

    /**
     * 父节点ID（外键）
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 排序序号（用于同层级节点展示顺序控制）
     */
    @TableField("sort_order")
    private BigDecimal sortOrder;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    /**
     * 子节点列表（非数据库字段，用于树形结构）
     */
    @TableField(exist = false)
    private List<DwdzCrewCertificatePath> children;

    /**
     * 节点完整路径（非数据库字段，用于搜索结果）
     */
    @TableField(exist = false)
    private String path;

    /**
     * 是否为证书职务节点（便于前端判断）
     */
    public boolean isCertificatePosition() {
        return "0".equals(this.nodeType);
    }

    /**
     * 是否为普通分类节点（便于前端判断）
     */
    public boolean isCategoryNode() {
        return "1".equals(this.nodeType);
    }
} 