package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.CrewCompanyInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员关联公司基本信息Mapper接口
 */
@Mapper
public interface CrewCompanyInfoMapper extends BaseMapper<CrewCompanyInfo> {
    
    /**
     * 根据船员ID查询关联的公司信息
     * @param crewId 船员ID
     * @return 公司信息列表
     */
    List<CrewCompanyInfo> selectCompaniesByCrewId(@Param("crewId") String crewId);
    
    /**
     * 根据船员ID和公司类型查询关联的公司信息
     * @param crewId 船员ID
     * @param companyTypes 公司类型数组
     * @return 公司信息列表
     */
    List<CrewCompanyInfo> selectCompaniesByCrewIdAndTypes(@Param("crewId") String crewId, 
                                                          @Param("companyTypes") List<String> companyTypes);
    
    /**
     * 根据公司ID列表查询公司信息
     * @param companyIds 公司ID列表
     * @return 公司信息列表
     */
    List<CrewCompanyInfo> selectByCompanyIds(@Param("companyIds") List<String> companyIds);
} 