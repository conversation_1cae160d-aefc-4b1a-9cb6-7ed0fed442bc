package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 船员职务名称分类表
 */
@Data
@TableName("dwdz_crew_position_category")
public class CrewPositionCategory {
    /**
     * 分类唯一标识ID（主键）
     */
    @TableId("category_id")
    private String categoryId;

    /**
     * 职务编码
     */
    @TableField("position_code")
    private String positionCode;

    /**
     * 职务名称
     */
    @TableField("position_name")
    private String positionName;

    /**
     * 职务级别（management/operational/support）
     */
    @TableField("position_level")
    private String positionLevel;

    /**
     * 该职务总人数
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 职务描述
     */
    @TableField("position_description")
    private String positionDescription;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    /**
     * 排序
     */
    @TableField("sort")
    private Double sort;
}