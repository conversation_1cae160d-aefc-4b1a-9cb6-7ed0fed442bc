package com.js.hszpt.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewShipService;
import com.js.hszpt.mapper.DwdzCrewShipServiceMapper;
import com.js.hszpt.service.DwdzCrewShipServiceService;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 船员船舶类型履职统计 Service实现
 */
@Service
public class DwdzCrewShipServiceServiceImpl extends ServiceImpl<DwdzCrewShipServiceMapper, DwdzCrewShipService>
        implements DwdzCrewShipServiceService {

    @Override
    public List<DwdzCrewShipService> getByCrewId(String crewId,String statType) {
        if (StrUtil.isBlank(statType)) {
            statType = "1";
        }
        return this.baseMapper.selectByCrewId(crewId,statType);
    }
} 