package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.CrewCurrentCert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员年度报告当前持证情况数据处理层
 * 
 * @ClassName: CrewCurrentCertDao
 * @Description: 船员年度报告当前持证情况数据处理层
 * @author: System Generation
 */
public interface CrewCurrentCertDao extends BaseMapper<CrewCurrentCert> {

    /**
     * 根据报告ID查询证书列表
     * 
     * @param reportId 报告ID
     * @return 证书列表
     */
    List<CrewCurrentCert> getByReportId(@Param("reportId") String reportId);

    /**
     * 根据报告ID和证书类别查询证书列表
     * 
     * @param reportId     报告ID
     * @param certCategory 证书类别
     * @return 证书列表
     */
    List<CrewCurrentCert> getByReportIdAndCategory(@Param("reportId") String reportId,
            @Param("certCategory") String certCategory);
}