package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 
 * @ClassName:  Demo   
 * @Description:TODO(示例表)   
 * @author:   System Generation 
 */
@Data
@TableName("dp_demo")
@ApiModel(value = "示例表")
public class Demo extends BaseEntity {

    private static final long serialVersionUID = 1L;

	
	/**
	* 名称
	*/
	@ApiModelProperty(value = "名称")
	private String name;
	
	
	/**
	* 类型,0-默认角色 1-管理员角色 2-其他角色
	*/
	@ApiModelProperty(value = "类型,0-默认角色 1-管理员角色 2-其他角色")
	private String type;
	
	
	/**
	* 编码
	*/
	@ApiModelProperty(value = "编码")
	private String code;
	
	
	/**
	* 是否默认
	*/
	@ApiModelProperty(value = "是否默认")
	private Boolean defaultRole;
	
	
	/**
	* 是否内置,0-否 1-是
	*/
	@ApiModelProperty(value = "是否内置,0-否 1-是")
	private Integer isSys;
	
	
	/**
	* 描述
	*/
	@ApiModelProperty(value = "描述")
	private String description;
	
	
	/**
	* 是否模板,0-否 1-是
	*/
	@ApiModelProperty(value = "是否模板,0-否 1-是")
	private Integer templateRole;
	
	
	/**
	* 逻辑删除标记
	*/
	@ApiModelProperty(value = "逻辑删除标记")
	private Integer delFlag;
	
	
	/**
	* 商家ID，用于多租户
	*/
	@ApiModelProperty(value = "商家ID，用于多租户")
	private String merchantId;
	
}