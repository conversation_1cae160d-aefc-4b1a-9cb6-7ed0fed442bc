package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * </p>
 * <AUTHOR>
 * @since 2020-04-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDeptEn extends Model<SysDeptEn> {

    private static final long serialVersionUID = 1L;

    private String orgCode;
    private String orgName;
    private String orgNameEn;
    private String orgAddr;
    private String orgAddrEn;
    private String remark;
    /**
     * 记录唯一标识是该表的记录唯一标识，本记录的唯一标识，取值为 UUID，符合 GB/T17969.8-2010 的规定
     */
    @TableId
    private String sysDeptEnId;


}
