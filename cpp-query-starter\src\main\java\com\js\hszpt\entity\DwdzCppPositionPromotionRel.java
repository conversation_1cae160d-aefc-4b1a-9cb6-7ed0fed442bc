package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 晋升关系表实体类
 * 用于管理职务间的晋升关系数据
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_position_promo_rel")
public class DwdzCppPositionPromotionRel {

    /**
     * 晋升关系唯一标识ID（主键）
     */
    @TableId(value = "promotion_relation_id", type = IdType.ASSIGN_UUID)
    private String promotionRelationId;

    /**
     * 当前证书职务分类id（源职务）
     */
    @TableField("dq_subcategory_id")
    private String dqSubcategoryId;

    /**
     * 当前证书职务分类编码
     */
    @TableField("dq_subcategory_bm")
    private String dqSubcategoryBm;

    /**
     * 晋升目标证书职务分类id（目标职务）
     */
    @TableField("mb_subcategory_id")
    private String mbSubcategoryId;

    /**
     * 目标证书职务分类编码
     */
    @TableField("mb_subcategory_bm")
    private String mbSubcategoryBm;

    /**
     * 证书类别 0(海船船员适任证书) 2(海上游艇操作人员) 5(培训合格证)
     */
    @TableField("cert_type")
    private Integer certType;

    /**
     * 申请关系形式枚举值
     * 0：职务晋升　1：学生考试发证 2：航区扩大 3:功率提高　4：军转海 
     * 5:内转海 6:渔转海 7:再有效　8:职务签证　10:吨位提高　11:取消限制　
     * 9:初次申请(游艇) 12:变更范围(游艇)
     */
    @TableField("relation_type")
    private Integer relationType;

    /**
     * 申请关系形式名称（中文翻译）
     */
    @TableField("sqxszsmc")
    private String sqxszsmc;

    /**
     * 证书申请要求总览条件id
     */
    @TableField("cert_overview_id")
    private Integer certOverviewId;

    /**
     * 晋升人数统计
     */
    @TableField("person_count")
    private Integer personCount;

    /**
     * 排序序号（用于同一晋升来源下的多个晋升目标排序）
     */
    @TableField("sort_order")
    private BigDecimal sortOrder;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    /**
     * 源职务子类名称（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private String dqSubcategoryName;

    /**
     * 目标职务子类名称（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private String mbSubcategoryName;

    /**
     * 晋升关系描述（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private String promotionDescription;
} 