package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 船员年度报告当前持证情况表
 * 
 * @ClassName: CrewCurrentCert
 * @Description: 船员年度报告当前持证情况表实体类
 * @author: System Generation
 */
@Data
@TableName("dwdz_crew_ar_current_cert")
@ApiModel(value = "船员年度报告当前持证情况表")
public class CrewCurrentCert {

    private static final long serialVersionUID = 1L;

    /**
     * 证书ID
     */
    @ApiModelProperty(value = "证书ID")
    private String certId;

    /**
     * 报告ID
     */
    @ApiModelProperty(value = "报告ID")
    private String reportId;

    /**
     * 证书类别(COMPETENCE:适任证书/QUALIFICATION:培训合格证/HEALTH:健康证)
     */
    @ApiModelProperty(value = "证书类别(COMPETENCE:适任证书/QUALIFICATION:培训合格证/HEALTH:健康证)")
    private String certCategory;

    /**
     * 证书名称
     */
    @ApiModelProperty(value = "证书名称")
    private String certName;

    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书编号")
    private String certNumber;

    /**
     * 发证日期
     */
    @ApiModelProperty(value = "发证日期")
    private Date issueDate;

    /**
     * 有效期至
     */
    @ApiModelProperty(value = "有效期至")
    private Date expiryDate;

    /**
     * 证书状态(VALID:有效/INVALID:无效)
     */
    @ApiModelProperty(value = "证书状态(VALID:有效/INVALID:无效)")
    private String certStatus;

    /**
     * 是否即将失效(三个月内)
     */
    @ApiModelProperty(value = "是否即将失效(三个月内)")
    private Boolean expiringFlag;

    /**
     * 记录创建日期
     */
    @ApiModelProperty(value = "记录创建日期")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @ApiModelProperty(value = "记录修改日期")
    private Date recModifyDate;
}