package com.js.hszpt.api;

import com.js.annotation.SystemLog;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.enums.LogType;
import com.js.hszpt.service.CrewCompanyRelationsService;
import com.js.hszpt.vo.CompanyRelationsStatisticsVO;
import com.js.hszpt.vo.CompanyRelationsDetailVO;
import com.js.hszpt.vo.CompanyTypeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 船员公司关系管理API接口
 * <AUTHOR> Generation
 */
@Slf4j
@RestController
@Api(description = "船员公司关系管理API接口")
@RequestMapping("/api/seafarer/company-relations")
public class CrewCompanyRelationsApi {

    @Autowired
    private CrewCompanyRelationsService crewCompanyRelationsService;

    /**
     * 获取船员公司关系统计信息
     */
    @SystemLog(description = "获取船员公司关系统计信息", type = LogType.OPERATION)
    @RequestMapping(value = "/statistics", method = RequestMethod.GET)
    @ApiOperation(value = "获取船员公司关系统计信息")
    public Result<CompanyRelationsStatisticsVO> getCompanyRelationsStatistics(
            @ApiParam(value = "船员ID", required = true) @RequestParam String seafarerId) {
        log.info("获取船员公司关系统计信息，船员ID：{}", seafarerId);
        
        try {
            CompanyRelationsStatisticsVO statistics = crewCompanyRelationsService.getCompanyRelationsStatistics(seafarerId);
            return ResultUtil.data(statistics);
        } catch (Exception e) {
            log.error("获取船员公司关系统计信息失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取船员公司关系统计信息失败");
        }
    }

    /**
     * 获取船员公司履历详细列表
     */
    @SystemLog(description = "获取船员公司履历详细列表", type = LogType.OPERATION)
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取船员公司履历详细列表")
    public Result<List<CompanyRelationsDetailVO>> getCompanyRelationsList(
            @ApiParam(value = "船员ID", required = true) @RequestParam String seafarerId,
            @ApiParam(value = "公司类型数组（可多选）") @RequestParam(required = false) List<String> companyTypes) {
        log.info("获取船员公司履历详细列表，船员ID：{}，公司类型：{}", seafarerId, companyTypes);
        
        try {
            List<CompanyRelationsDetailVO> list = crewCompanyRelationsService.getCompanyRelationsList(seafarerId, companyTypes);
            return ResultUtil.data(list);
        } catch (Exception e) {
            log.error("获取船员公司履历详细列表失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取船员公司履历详细列表失败");
        }
    }

    /**
     * 获取公司类型字典
     */
    @SystemLog(description = "获取公司类型字典", type = LogType.OPERATION)
    @RequestMapping(value = "/types", method = RequestMethod.GET)
    @ApiOperation(value = "获取公司类型字典")
    public Result<List<CompanyTypeVO>> getCompanyTypes() {
        log.info("获取公司类型字典");
        
        try {
            List<CompanyTypeVO> types = crewCompanyRelationsService.getCompanyTypes();
            return ResultUtil.data(types);
        } catch (Exception e) {
            log.error("获取公司类型字典失败", e);
            return ResultUtil.error("获取公司类型字典失败");
        }
    }
} 