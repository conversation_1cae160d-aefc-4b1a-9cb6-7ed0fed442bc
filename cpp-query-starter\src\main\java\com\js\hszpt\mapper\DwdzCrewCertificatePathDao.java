package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewCertificatePath;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 证书路径表DAO接口 - 新版本
 * 对应新的证书路径表结构
 * 
 * <AUTHOR>
 * @since 2024-12-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCrewCertificatePathDao extends BaseMapper<DwdzCrewCertificatePath> {

    /**
     * 根据证书类型获取所有节点
     * @param certType 证书类型
     * @return 节点列表
     */
    @Select("SELECT * FROM dwdz_crew_certificate_path WHERE cert_type = #{certType} ORDER BY sort_order ASC, rec_create_date ASC")
    List<DwdzCrewCertificatePath> getNodesByCertType(@Param("certType") String certType);

    /**
     * 根据根节点名称获取该节点及其所有子节点
     * @param rootNodeName 根节点名称
     * @return 节点列表
     */
    @Select("WITH RECURSIVE node_tree AS (" +
            "  SELECT * FROM dwdz_crew_certificate_path WHERE node_name = #{rootNodeName} AND parent_id IS NULL" +
            "  UNION ALL" +
            "  SELECT c.* FROM dwdz_crew_certificate_path c" +
            "    INNER JOIN node_tree nt ON c.parent_id = nt.node_id" +
            ") SELECT * FROM node_tree ORDER BY sort_order ASC")
    List<DwdzCrewCertificatePath> getNodeTreeByRootName(@Param("rootNodeName") String rootNodeName);

    /**
     * 根据节点名称查找节点（支持模糊搜索）
     * @param keyword 搜索关键词
     * @param limit 限制数量
     * @return 节点列表
     */
    @Select("SELECT * FROM dwdz_crew_certificate_path WHERE node_name LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY sort_order ASC LIMIT #{limit}")
    List<DwdzCrewCertificatePath> searchNodesByKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);

    /**
     * 根据父节点ID获取子节点
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    @Select("SELECT * FROM dwdz_crew_certificate_path WHERE parent_id = #{parentId} ORDER BY sort_order ASC")
    List<DwdzCrewCertificatePath> getChildNodes(@Param("parentId") String parentId);

    /**
     * 获取所有根节点
     * @return 根节点列表
     */
    @Select("SELECT * FROM dwdz_crew_certificate_path WHERE parent_id IS NULL ORDER BY sort_order ASC")
    List<DwdzCrewCertificatePath> getRootNodes();

    /**
     * 根据节点类型获取节点
     * @param nodeType 节点类型 (0:证书职务 1:普通分类)
     * @return 节点列表
     */
    @Select("SELECT * FROM dwdz_crew_certificate_path WHERE node_type = #{nodeType} ORDER BY sort_order ASC")
    List<DwdzCrewCertificatePath> getNodesByType(@Param("nodeType") String nodeType);

    /**
     * 根据证书类型和节点类型获取节点
     * @param certType 证书类型
     * @param nodeType 节点类型
     * @return 节点列表
     */
    @Select("SELECT * FROM dwdz_crew_certificate_path WHERE cert_type = #{certType} AND node_type = #{nodeType} " +
            "ORDER BY sort_order ASC")
    List<DwdzCrewCertificatePath> getNodesByCertTypeAndNodeType(@Param("certType") String certType, @Param("nodeType") String nodeType);

    /**
     * 获取证书职务节点（用于晋升要求查询）
     * @param certType 证书类型
     * @return 证书职务节点列表
     */
    @Select("SELECT * FROM dwdz_crew_certificate_path WHERE cert_type = #{certType} AND node_type = '0' " +
            "ORDER BY sort_order ASC")
    List<DwdzCrewCertificatePath> getCertificatePositionNodes(@Param("certType") String certType);
} 