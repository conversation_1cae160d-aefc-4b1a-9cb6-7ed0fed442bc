package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 年度报告数据响应VO
 * @ClassName: AnnualReportDataVO
 * @Description: 年度报告数据响应VO类
 * @author: System Generation
 */
@Data
@ApiModel(value = "年度报告数据响应VO")
public class AnnualReportDataVO {

    /**
     * 基本信息
     */
    @ApiModelProperty(value = "基本信息")
    private BasicInfoVO basicInfo;

    /**
     * 统计数据
     */
    @ApiModelProperty(value = "统计数据")
    private StatisticsVO statistics;

    /**
     * 在船状态
     */
    @ApiModelProperty(value = "在船状态")
    private ShipStatusVO shipStatus;

    /**
     * 证书信息
     */
    @ApiModelProperty(value = "证书信息")
    private CertificatesVO certificates;

    /**
     * 基本信息VO
     */
    @Data
    @ApiModel(value = "基本信息VO")
    public static class BasicInfoVO {
        @ApiModelProperty(value = "船员姓名")
        private String name;

        @ApiModelProperty(value = "身份证号")
        private String idNumber;

        @ApiModelProperty(value = "证件照URL")
        private String avatar;
    }

    /**
     * 统计数据VO
     */
    @Data
    @ApiModel(value = "统计数据VO")
    public static class StatisticsVO {
        @ApiModelProperty(value = "年度违法记分总计")
        private Integer violationScoreTotal;

        @ApiModelProperty(value = "当前销分情况描述")
        private String scoreReduction;

        @ApiModelProperty(value = "违法记分清分时间(YYYY-MM-DD)")
        private String clearanceDate;

        @ApiModelProperty(value = "年度履职天数总计")
        private Integer serviceDaysTotal;

        @ApiModelProperty(value = "无限航区履职天数")
        private Integer unlimitedAreaDays;

        @ApiModelProperty(value = "沿海航区履职天数")
        private Integer coastalAreaDays;
    }

    /**
     * 在船状态VO
     */
    @Data
    @ApiModel(value = "在船状态VO")
    public static class ShipStatusVO {
        @ApiModelProperty(value = "当前状态(在船/休假)")
        private String currentStatus;

        @ApiModelProperty(value = "船舶名称")
        private String shipName;

        @ApiModelProperty(value = "船舶类型")
        private String shipType;

        @ApiModelProperty(value = "总吨位")
        private String grossTonnage;

        @ApiModelProperty(value = "担任职务")
        private String position;

        @ApiModelProperty(value = "上船日期")
        private String boardingDate;

        @ApiModelProperty(value = "始发港")
        private String departurePort;

        @ApiModelProperty(value = "目的港")
        private String destinationPort;
    }

    /**
     * 证书信息VO
     */
    @Data
    @ApiModel(value = "证书信息VO")
    public static class CertificatesVO {
        @ApiModelProperty(value = "适任证书")
        private List<CertificateVO> competency;

        @ApiModelProperty(value = "培训证书")
        private List<CertificateVO> training;

        @ApiModelProperty(value = "健康证书")
        private List<CertificateVO> health;
    }

    /**
     * 单个证书VO
     */
    @Data
    @ApiModel(value = "单个证书VO")
    public static class CertificateVO {
        @ApiModelProperty(value = "证书名称")
        private String name;

        @ApiModelProperty(value = "状态描述(有效/即将过期/已过期)")
        private String status;

        @ApiModelProperty(value = "状态样式类名(status-valid/status-expiring/status-expired)")
        private String statusClass;

        @ApiModelProperty(value = "到期日期(YYYY-MM-DD)")
        private String expiryDate;
    }
} 