package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 年份选项VO
 * @ClassName: YearOptionVO
 * @Description: 年份选项VO类
 * @author: System Generation
 */
@Data
@ApiModel(value = "年份选项VO")
public class YearOptionVO {

    /**
     * 年份数值
     */
    @ApiModelProperty(value = "年份数值")
    private Integer value;

    /**
     * 年份显示标签
     */
    @ApiModelProperty(value = "年份显示标签")
    private String label;

    public YearOptionVO() {}

    public YearOptionVO(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
} 