<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewFeatureMapper">

    <!-- 根据船员ID列表和触发类别代码前缀查询特征数据 -->
    <select id="selectByCrewIdsAndCategoryCodePrefix" resultType="com.js.hszpt.entity.DwdzCrewFeature">
        SELECT 
            feature_id,
            crew_id,
            trigger_category_id,
            trigger_category_name,
            feature_type,
            feature_value,
            rule_calculation_desc,
            calc_time,
            warning_level,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_feature
        WHERE 1=1
        <if test="crewIds != null and crewIds.size() > 0">
            AND crew_id IN
            <foreach collection="crewIds" item="crewId" open="(" close=")" separator=",">
                #{crewId}
            </foreach>
        </if>
        <if test="categoryCodePrefix != null and categoryCodePrefix != ''">
            AND trigger_category_code LIKE #{categoryCodePrefix} || '%'
        </if>
    </select>

</mapper> 