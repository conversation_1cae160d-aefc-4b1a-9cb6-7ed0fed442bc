<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewCurrentCertDao">

    <!-- 证书结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewCurrentCert">
        <id column="cert_id" property="certId" jdbcType="VARCHAR"/>
        <result column="crew_id" property="crewId" jdbcType="VARCHAR"/>
        <result column="cert_category" property="certCategory" jdbcType="VARCHAR"/>
        <result column="cert_name" property="certName" jdbcType="VARCHAR"/>
        <result column="cert_number" property="certNumber" jdbcType="VARCHAR"/>
        <result column="issue_date" property="issueDate"/>
        <result column="expiry_date" property="expiryDate"/>
        <result column="cert_status" property="certStatus" jdbcType="VARCHAR"/>
        <result column="navi_area" property="naviArea" jdbcType="VARCHAR"/>
        <result column="expiring_flag" property="expiringFlag" jdbcType="BOOLEAN"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
        <result column="position_level" property="positionLevel" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        cert_id, crew_id, cert_category, cert_name, cert_number, issue_date, 
        expiry_date, cert_status, navi_area, expiring_flag, rec_create_date, rec_modify_date, position_level
    </sql>

    <!-- 根据船员ID查询证书列表 -->
    <select id="getByCrewId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_current_cert
        WHERE crew_id = #{crewId}
        ORDER BY cert_category, expiry_date DESC
    </select>

    <!-- 根据船员ID和证书类别查询证书列表 -->
    <select id="getByCrewIdAndCategory" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_current_cert
        WHERE crew_id = #{crewId}
        AND cert_category = #{certCategory}
        ORDER BY expiry_date DESC
    </select>

    <!-- 批量查询船员是否持有适任证书 -->
    <select id="getCrewIdsWithCompetencyCert" resultType="java.lang.String">
        SELECT DISTINCT crew_id
        FROM dwdz_crew_current_cert
        WHERE crew_id IN
        <foreach collection="crewIds" item="crewId" open="(" close=")" separator=",">
            #{crewId}
        </foreach>
        AND cert_category = 'COMPETENCE'
        AND cert_status = 'VALID'
    </select>

    <!-- 批量查询船员证书信息 -->
    <select id="getByCrewIds" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_current_cert
        WHERE crew_id IN
        <foreach collection="crewIds" item="crewId" open="(" close=")" separator=",">
            #{crewId}
        </foreach>
        ORDER BY crew_id, cert_category, expiry_date DESC
    </select>

</mapper> 