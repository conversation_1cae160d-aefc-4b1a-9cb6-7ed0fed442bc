<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewFeatureDictDao">

    <!-- 根据标签编码和查询文本获取标签值建议列表 -->
    <select id="getTagValueSuggestions" resultType="com.js.hszpt.vo.TagValueSuggestionVo">
        SELECT DISTINCT
            t.feature_value AS featureValue,
            t.feature_value AS description,
            t.trigger_category_code AS triggerCategoryCode
        FROM dwdz_crew_feature_dict t
        WHERE 1=1
            AND t.trigger_category_code = #{tagCode}
            AND t.feature_value IS NOT NULL
            AND t.feature_value LIKE ('%' || #{queryText} || '%')
        ORDER BY
            CASE
                WHEN t.feature_value LIKE (#{queryText} || '%') THEN 1
                WHEN t.feature_value LIKE ('%' || #{queryText} || '%') THEN 2
                ELSE 3
            END
        LIMIT #{limit}
    </select>

    <!-- 备用查询：如果主查询没有结果，可以使用模糊匹配 -->
    <select id="getTagValueSuggestionsWithFuzzy" resultType="com.js.hszpt.vo.TagValueSuggestionVo">
        SELECT DISTINCT
            t.feature_value AS featureValue,
            t.feature_value AS description,
            t.trigger_category_code AS triggerCategoryCode
        FROM dwdz_crew_feature_dict t
        WHERE 1=1
            AND t.trigger_category_code = #{tagCode}
            AND t.feature_value IS NOT NULL
            AND t.feature_value LIKE ('%' || #{queryText} || '%')
        ORDER BY 
            CASE 
                WHEN t.feature_value LIKE (#{queryText} || '%') THEN 1
                WHEN t.feature_value LIKE ('%' || #{queryText} || '%') THEN 2
                ELSE 3
            END
        LIMIT #{limit}
    </select>

    <!-- 查询条件构造（预留） -->
    <sql id="where">
        <where>
            <if test="triggerCategoryCode != null and triggerCategoryCode != ''">
                AND trigger_category_code = #{triggerCategoryCode}
            </if>
            <if test="featureValue != null and featureValue != ''">
                AND feature_value LIKE ('%' || #{featureValue} || '%')
            </if>
        </where>
    </sql>

    <!-- 统计某个标签编码下的特征值数量 -->
    <select id="countByTagCode" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT feature_value)
        FROM dwdz_crew_feature_dict
        WHERE trigger_category_code = #{tagCode}
            AND feature_value IS NOT NULL
            AND feature_value != ''
    </select>

</mapper>
