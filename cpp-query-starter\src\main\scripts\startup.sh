#!/bin/sh

APP_NAME=jsdp-fast-starter
APP_HOME=`cd $(dirname $0) && pwd`

echo "APP_HOME=${APP_HOME}"

APP_CONFIG_DIR=${APP_HOME}/config
APP_STATIC_DIR=${APP_HOME}/static
APP_LOG_DIR=${APP_HOME}/logs
#APP_LOGBACK_FILE=${APP_CONFIG_DIR}/logback-spring.xml
APP_PID_FILE=${APP_HOME}/application.pid
BOOT_JAR=`echo "${APP_HOME}"/${APP_NAME}*.jar`

cd "${APP_HOME}"

# 检查必要文件夹是否已创建
if [[ ! -f "${APP_LOG_DIR}" ]] ; then
  mkdir "${APP_LOG_DIR}"
fi

# 检查程序是否已经启动了
if [[ -f "${APP_PID_FILE}" ]] ; then
  ps -p "$(cat "${APP_PID_FILE}")" >/dev/null
  if [[ "$?" = "0" ]] ; then
    echo "Application's pid file has existed, and check process [$(cat "${APP_PID_FILE}")] is running" >&2
    exit 1
  fi
fi

# APP_HOME下存在jre目录, 使用此目录作为JAVA_HOME
JRE_LOCATION="${APP_HOME}/jre"
if [[ -d "${JRE_LOCATION}" ]] ; then
  JAVA_HOME="${JRE_LOCATION}"
fi
# 使用系统 JAVA_HOME
if [[ -z "$JAVA_HOME" ]] ; then
  JAVACMD=`which java`
else
  JAVACMD="$JAVA_HOME/bin/java"
fi
# 找不到java命令
if [[ ! -x "$JAVACMD" ]] ; then
  echo "系统中找不到可用的 java 环境, 请确认已安装JDK" >&2
  exit 1
fi

# Java 及 springboot 相关参数设置
export JAVA_OPTS="-server -Xss512k -Xms256M -Xmx512m"
export SPRING_OPTS="\
    -Xbootclasspath/p:${APP_CONFIG_DIR} \
    -Dspring.config.location=${APP_CONFIG_DIR}/ \
    -Dspring.pid.file=${APP_PID_FILE} \
    -Dspring.profiles.active=prod"

# 启动程序
nohup "$JAVACMD" \
      ${JAVA_OPTS} -DAPP_HOME="${APP_HOME}"-Dapp.name="${APP_NAME}" \
      ${SPRING_OPTS} -jar "${BOOT_JAR}" \
      > ${APP_LOG_DIR}/startup.out 2>&1 &

sleep 2s
tail -f ${APP_LOG_DIR}/startup.out
