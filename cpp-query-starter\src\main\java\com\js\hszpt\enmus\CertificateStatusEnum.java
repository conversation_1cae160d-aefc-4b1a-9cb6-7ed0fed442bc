package com.js.hszpt.enmus;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * 证书状态枚举
 * 
 * @Description: 定义证书的各种状态
 * @author: System Generation
 */
@ToString
@AllArgsConstructor
@Getter
public enum CertificateStatusEnum {

    /**
     * 有效状态
     */
    VALID("valid", "有效", "status-valid"),

    /**
     * 即将过期状态
     */
    EXPIRING("expiring", "即将过期", "status-expiring"),

    /**
     * 已过期状态
     */
    EXPIRED("expired", "已过期", "status-expired");

    /**
     * 状态码（英文）
     */
    private final String code;

    /**
     * 状态描述（中文）
     */
    private final String description;

    /**
     * 状态样式类名
     */
    private final String statusClass;

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举
     */
    public static CertificateStatusEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(value -> value.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}