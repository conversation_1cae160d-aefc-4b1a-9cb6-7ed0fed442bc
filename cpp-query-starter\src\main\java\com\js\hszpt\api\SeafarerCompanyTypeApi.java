package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.DwdzCrewCompanyType;
import com.js.hszpt.service.DwdzCrewCompanyTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 公司类型字典表API接口
 */
@RestController
@RequestMapping("/api/seafarer/company-type")
public class SeafarerCompanyTypeApi {

    @Autowired
    private DwdzCrewCompanyTypeService companyTypeService;

    /** 根据类型代码查详情 */
    @GetMapping("/{companyType}")
    public Result<DwdzCrewCompanyType> getById(@PathVariable String companyType) {
        return ResultUtil.data(companyTypeService.getById(companyType));
    }

    /** 查询所有公司类型 */
    @GetMapping("/list")
    public Result<List<DwdzCrewCompanyType>> listAll() {
        return ResultUtil.data(companyTypeService.list());
    }

    /** 获取启用的公司类型（下拉列表用） */
    @GetMapping("/enabled")
    public Result<List<DwdzCrewCompanyType>> getEnabledTypes() {
        return ResultUtil.data(companyTypeService.getEnabledTypes());
    }

    /** 新增公司类型 */
    @PostMapping
    public Result<Boolean> add(@RequestBody DwdzCrewCompanyType type) {
        return ResultUtil.data(companyTypeService.save(type));
    }

    /** 修改公司类型 */
    @PutMapping
    public Result<Boolean> update(@RequestBody DwdzCrewCompanyType type) {
        return ResultUtil.data(companyTypeService.updateById(type));
    }

    /** 删除公司类型 */
    @DeleteMapping("/{companyType}")
    public Result<Boolean> delete(@PathVariable String companyType) {
        return ResultUtil.data(companyTypeService.removeById(companyType));
    }
} 