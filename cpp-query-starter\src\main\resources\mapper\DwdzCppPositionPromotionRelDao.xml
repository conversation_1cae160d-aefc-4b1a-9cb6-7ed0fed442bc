<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCppPositionPromotionRelDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCppPositionPromotionRel">
        <id column="promotion_relation_id" property="promotionRelationId" />
        <result column="dq_subcategory_id" property="dqSubcategoryId" />
        <result column="dq_subcategory_bm" property="dqSubcategoryBm" />
        <result column="mb_subcategory_id" property="mbSubcategoryId" />
        <result column="mb_subcategory_bm" property="mbSubcategoryBm" />
        <result column="cert_type" property="certType" />
        <result column="relation_type" property="relationType" />
        <result column="sqxszsmc" property="sqxszsmc" />
        <result column="cert_overview_id" property="certOverviewId" />
        <result column="person_count" property="personCount" />
        <result column="sort_order" property="sortOrder" />
        <result column="rec_create_date" property="recCreateDate" />
        <result column="rec_modify_date" property="recModifyDate" />
        <result column="dq_subcategory_name" property="dqSubcategoryName" />
        <result column="mb_subcategory_name" property="mbSubcategoryName" />
        <result column="promotion_description" property="promotionDescription" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        promotion_relation_id, dq_subcategory_id, dq_subcategory_bm, mb_subcategory_id, mb_subcategory_bm,
        cert_type, relation_type, sqxszsmc, cert_overview_id,
        person_count, sort_order, rec_create_date, rec_modify_date
    </sql>

    <!-- 根据子分类名称获取晋升关系（包含来源和目标） -->
    <select id="getPromotionRelationsBySubcategoryName" resultMap="BaseResultMap">
        SELECT 
            rel.promotion_relation_id,
            rel.dq_subcategory_id,
            rel.dq_subcategory_bm,
            rel.mb_subcategory_id,
            rel.mb_subcategory_bm,
            rel.cert_type,
            rel.relation_type,
            rel.sqxszsmc,
            rel.cert_overview_id,
            rel.person_count,
            rel.sort_order,
            rel.rec_create_date,
            rel.rec_modify_date,
            dq_sub.flxxmc as "dq_subcategory_name",
            mb_sub.flxxmc as "mb_subcategory_name"
        FROM dwdz_crew_position_promo_rel rel
        LEFT JOIN dwdz_crew_position_subcategory dq_sub ON rel.dq_subcategory_id = dq_sub.subcategory_id
        LEFT JOIN dwdz_crew_position_subcategory mb_sub ON rel.mb_subcategory_id = mb_sub.subcategory_id
        WHERE dq_sub.flxxmc = #{subcategoryName} 
           OR mb_sub.flxxmc = #{subcategoryName}
        ORDER BY rel.sort_order ASC, rel.relation_type, rel.rec_create_date DESC
    </select>

    <!-- 根据子分类名称获取晋升关系详情（包含子分类名称） -->
    <select id="getPromotionRelationsWithNames" resultType="java.util.Map">
        SELECT 
            rel.promotion_relation_id as "promotionRelationId",
            rel.relation_type as "relationType",
            rel.sqxszsmc as "sqxszsmc",
            rel.person_count as "personCount",
            rel.sort_order as "sortOrder",
            dq_sub.flxxmc as "dqSubcategoryName",
            mb_sub.flxxmc as "mbSubcategoryName",
            CASE 
                WHEN dq_sub.flxxmc = #{subcategoryName} THEN 'target'
                WHEN mb_sub.flxxmc = #{subcategoryName} THEN 'source'
                ELSE 'related'
            END as "direction",
            CASE 
                WHEN dq_sub.flxxmc = #{subcategoryName} THEN mb_sub.flxxmc
                ELSE dq_sub.flxxmc
            END as "relatedSubcategoryName"
        FROM dwdz_crew_position_promo_rel rel
        LEFT JOIN dwdz_crew_position_subcategory dq_sub ON rel.dq_subcategory_id = dq_sub.subcategory_id
        LEFT JOIN dwdz_crew_position_subcategory mb_sub ON rel.mb_subcategory_id = mb_sub.subcategory_id
        WHERE dq_sub.flxxmc = #{subcategoryName} 
           OR mb_sub.flxxmc = #{subcategoryName}
        ORDER BY rel.sort_order ASC, rel.relation_type, rel.person_count DESC
    </select>

    <!-- 根据子分类ID获取晋升关系详情（新版本，基于ID查询） -->
    <select id="getPromotionRelationsBySubcategoryId" resultType="java.util.Map">
        SELECT 
            rel.promotion_relation_id as "promotionRelationId",
            rel.relation_type as "relationType",
            rel.sqxszsmc as "sqxszsmc",
            rel.person_count as "personCount",
            rel.sort_order as "sortOrder",
            rel.dq_subcategory_id as "dqSubcategoryId",
            rel.mb_subcategory_id as "mbSubcategoryId",
            dq_sub.flxxmc as "dqSubcategoryName",
            mb_sub.flxxmc as "mbSubcategoryName",
            CASE 
                WHEN rel.dq_subcategory_id = #{subcategoryId} THEN 'target'
                WHEN rel.mb_subcategory_id = #{subcategoryId} THEN 'source'
                ELSE 'related'
            END as "direction",
            CASE 
                WHEN rel.dq_subcategory_id = #{subcategoryId} THEN mb_sub.subcategory_id
                ELSE dq_sub.subcategory_id
            END as "relatedSubcategoryId",
            CASE 
                WHEN rel.dq_subcategory_id = #{subcategoryId} THEN mb_sub.flxxmc
                ELSE dq_sub.flxxmc
            END as "relatedSubcategoryName",
            CASE 
                WHEN rel.dq_subcategory_id = #{subcategoryId} THEN mb_sub.srzwmc
                ELSE dq_sub.srzwmc
            END as "relatedSrzwmc",
            CASE 
                WHEN rel.dq_subcategory_id = #{subcategoryId} THEN mb_sub.hqmcqc
                ELSE dq_sub.hqmcqc
            END as "relatedHqmcqc",
            CASE 
                WHEN rel.dq_subcategory_id = #{subcategoryId} THEN mb_sub.djmcqc
                ELSE dq_sub.djmcqc
            END as "relatedDjmcqc"
        FROM dwdz_crew_position_promo_rel rel
        LEFT JOIN dwdz_crew_position_subcategory dq_sub ON rel.dq_subcategory_id = dq_sub.subcategory_id and dq_sub.node_type = '0'
        LEFT JOIN dwdz_crew_position_subcategory mb_sub ON rel.mb_subcategory_id = mb_sub.subcategory_id and mb_sub.node_type = '0'
        WHERE (rel.dq_subcategory_id = #{subcategoryId} OR rel.mb_subcategory_id = #{subcategoryId} )
        and exists(select 1 from dwdz_crew_position_subcategory sub where sub.subcategory_id = rel.dq_subcategory_id and sub.node_type = '0' )
        and exists(select 1 from dwdz_crew_position_subcategory sub1 where sub1.subcategory_id = rel.mb_subcategory_id and sub1.node_type = '0' )
        ORDER BY rel.sort_order ASC, rel.relation_type, rel.person_count DESC
    </select>

</mapper> 