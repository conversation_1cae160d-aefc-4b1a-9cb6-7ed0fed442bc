<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCppPositionHierarchyDao">

    <!-- 船员群体层级关系表结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCppPositionHierarchy">
        <id column="hierarchy_id" property="hierarchyId" />
        <result column="parent_id" property="parentId" />
        <result column="name" property="name" />
        <result column="value" property="value" />
        <result column="certified_count" property="certifiedCount" />
        <result column="demand_count" property="demandCount" />
        <result column="onboard_count" property="onboardCount" />
        <result column="sort_order" property="sortOrder" />
        <result column="display_direction" property="displayDirection" />
        <result column="group_introduction" property="groupIntroduction" />
        <result column="static_introduction" property="staticIntroduction" />
        <result column="rec_create_date" property="recCreateDate" />
        <result column="rec_modify_date" property="recModifyDate" />
    </resultMap>

    <!-- 获取船员总数统计 -->
    <select id="getSeafarerTotalCount" resultType="java.lang.Long">
        SELECT
            COALESCE(SUM(value), 0) as "total_count"
        FROM dwdz_crew_position_hierarchy
        WHERE parent_id IS NULL
    </select>

    <!-- 获取完整的船员群体树形结构数据 -->
    <select id="getSeafarerHierarchyTree" resultMap="BaseResultMap">
        SELECT
            hierarchy_id,
            parent_id,
            name,
            value,
            certified_count,
            demand_count,
            onboard_count,
            sort_order,
            display_direction,
            group_introduction,
            static_introduction,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_hierarchy
        ORDER BY parent_id, sort_order ASC, name
    </select>

    <!-- 根据层级ID获取子节点列表 -->
    <select id="getChildrenByParentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
            hierarchy_id,
            parent_id,
            name,
            value,
            certified_count,
            demand_count,
            onboard_count,
            sort_order,
            display_direction,
            group_introduction,
            static_introduction,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_hierarchy
        WHERE
            <choose>
                <when test="parentId != null">
                    parent_id = #{parentId}
                </when>
                <otherwise>
                    parent_id IS NULL
                </otherwise>
            </choose>
        ORDER BY sort_order ASC, name
    </select>

    <!-- 根据船员类别名称查询层级信息 -->
    <select id="getHierarchyByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
            hierarchy_id,
            parent_id,
            name,
            value,
            certified_count,
            demand_count,
            onboard_count,
            sort_order,
            display_direction,
            group_introduction,
            static_introduction,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_hierarchy
        WHERE name = #{name}
        LIMIT 1
    </select>

</mapper> 