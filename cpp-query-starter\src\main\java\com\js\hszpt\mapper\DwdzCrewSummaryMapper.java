package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewSummary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员职业生涯汇总信息表Mapper接口
 * <AUTHOR> Generation
 */
@Mapper
public interface DwdzCrewSummaryMapper extends BaseMapper<DwdzCrewSummary> {

    /**
     * 根据船员ID查询职业生涯汇总信息
     * @param crewId 船员ID
     * @return 职业生涯汇总信息
     */
    DwdzCrewSummary selectByCrewId(@Param("crewId") String crewId);

    /**
     * 批量查询船员职业生涯汇总信息
     * @param crewIds 船员ID列表
     * @return 职业生涯汇总信息列表
     */
    List<DwdzCrewSummary> selectByCrewIds(@Param("crewIds") List<String> crewIds);
} 