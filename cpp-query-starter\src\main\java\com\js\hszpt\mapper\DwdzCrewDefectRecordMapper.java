package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewDefectRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用缺陷记录表Mapper接口
 * 
 * <AUTHOR> Generation
 */
@Mapper
public interface DwdzCrewDefectRecordMapper extends BaseMapper<DwdzCrewDefectRecord> {

    /**
     * 根据检查编号查询缺陷记录
     * 
     * @param inspectNo 检查编号
     * @return 缺陷记录列表
     */
    List<DwdzCrewDefectRecord> selectByInspectNo(@Param("inspectNo") String inspectNo);

    /**
     * 根据检查编号列表批量查询缺陷记录
     * 
     * @param inspectNoList 检查编号列表
     * @return 缺陷记录列表
     */
    List<DwdzCrewDefectRecord> selectByInspectNoList(@Param("inspectNoList") List<String> inspectNoList);

    /**
     * 根据来源类型和检查编号查询缺陷记录
     * 
     * @param sourceType 来源类型
     * @param inspectNo 检查编号
     * @return 缺陷记录列表
     */
    List<DwdzCrewDefectRecord> selectBySourceTypeAndInspectNo(@Param("sourceType") String sourceType, 
                                                              @Param("inspectNo") String inspectNo);
} 