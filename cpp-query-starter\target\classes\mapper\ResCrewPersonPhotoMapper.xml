<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.ResCrewPersonPhotoMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.ResCrewPersonPhoto">
        <id column="serial_no" property="serialNo"/>
        <result column="idcard_no" property="idcardNo"/>
        <result column="photo_info" property="photoInfo"/>
    </resultMap>
    
    <!-- 根据身份证号码查询船员照片 -->
    <select id="selectByIdcardNo" resultMap="BaseResultMap">
        SELECT 
            serial_no,
            idcard_no,
            photo_info
        FROM res_crew_person_photo
        WHERE idcard_no = #{idcardNo} and valid_flag_code = '1'
        ORDER BY CREATOR_DATE DESC
        LIMIT 1
    </select>
    
</mapper> 