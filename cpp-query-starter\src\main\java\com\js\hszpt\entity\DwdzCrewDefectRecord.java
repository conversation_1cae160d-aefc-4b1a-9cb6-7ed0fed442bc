package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 通用缺陷记录表
 * 
 * <AUTHOR> Generation
 */
@Data
@TableName("dwdz_crew_defect_record")
@ApiModel(value = "通用缺陷记录表")
public class DwdzCrewDefectRecord {

    private static final long serialVersionUID = 1L;

    /**
     * 缺陷主键ID（自动生成UUID）
     */
    @TableId(value = "defect_id")
    @ApiModelProperty(value = "缺陷主键ID")
    private String defectId;

    /**
     * 来源类型（SITE:现场检查/FSC:船旗国/PSC:港口国）
     */
    @ApiModelProperty(value = "来源类型（SITE:现场检查/FSC:船旗国/PSC:港口国）")
    private String sourceType;

    /**
     * 检查编号（关联原始检查记录）
     */
    @ApiModelProperty(value = "检查编号（关联原始检查记录）")
    private String inspectNo;

    /**
     * 来源表原始ID（FORM_B_ID/对应无主键表的组合键）
     */
    @ApiModelProperty(value = "来源表原始ID")
    private String originalId;

    /**
     * 缺陷序号
     */
    @ApiModelProperty(value = "缺陷序号")
    private BigDecimal seqNo;

    /**
     * 缺陷代码（三表缺陷代码统一）
     */
    @ApiModelProperty(value = "缺陷代码")
    private String defectCode;

    /**
     * 缺陷描述（中文）
     */
    @ApiModelProperty(value = "缺陷描述（中文）")
    private String defectDesc;

    /**
     * 缺陷英文描述（仅PSC有）
     */
    @ApiModelProperty(value = "缺陷英文描述（仅PSC有）")
    private String defectDescEn;

    /**
     * 处理意见代码
     */
    @ApiModelProperty(value = "处理意见代码")
    private String processResultCode;

    /**
     * 处理意见说明
     */
    @ApiModelProperty(value = "处理意见说明")
    private String processResultDesc;

    /**
     * 纠正标志代码
     */
    @ApiModelProperty(value = "纠正标志代码")
    private String correctFlagCode;

    /**
     * 纠正标志名称
     */
    @ApiModelProperty(value = "纠正标志名称")
    private String correctFlagName;

    /**
     * 缺陷状态代码（PSC的关闭标识）
     */
    @ApiModelProperty(value = "缺陷状态代码（PSC的关闭标识）")
    private String defectStatusCode;

    /**
     * 缺陷状态名称
     */
    @ApiModelProperty(value = "缺陷状态名称")
    private String defectStatusName;

    /**
     * 公约代码（仅PSC有）
     */
    @ApiModelProperty(value = "公约代码（仅PSC有）")
    private String defectConvCode;

    /**
     * 公约描述
     */
    @ApiModelProperty(value = "公约描述")
    private String defectConvDesc;

    /**
     * 其他处理措施
     */
    @ApiModelProperty(value = "其他处理措施")
    private String actionOther;

    /**
     * 责任方代码
     */
    @ApiModelProperty(value = "责任方代码")
    private String defectRoCode;

    /**
     * 责任方名称
     */
    @ApiModelProperty(value = "责任方名称")
    private String defectRoName;

    /**
     * 是否与检验机构有关
     */
    @ApiModelProperty(value = "是否与检验机构有关")
    private String ifRelateInspect;

    /**
     * 执法依据
     */
    @ApiModelProperty(value = "执法依据")
    private String enforceBasis;

    /**
     * 发现问题标识
     */
    @ApiModelProperty(value = "发现问题标识")
    private String ifFindProblem;

    /**
     * 检查内容
     */
    @ApiModelProperty(value = "检查内容")
    private String inspectContent;

    /**
     * 检查结果
     */
    @ApiModelProperty(value = "检查结果")
    private String inspectResult;

    /**
     * 初查编号
     */
    @ApiModelProperty(value = "初查编号")
    private String initInspectNo;

    /**
     * APCIS检查编号
     */
    @ApiModelProperty(value = "APCIS检查编号")
    private String apcisNo;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    private String remark;

    /**
     * 原始创建日期
     */
    @ApiModelProperty(value = "原始创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createDate;

    /**
     * 操作日期
     */
    @ApiModelProperty(value = "操作日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date operateDate;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private String creatorId;

    /**
     * 记录创建时间
     */
    @ApiModelProperty(value = "记录创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recCreateDate;

    /**
     * 记录更新时间
     */
    @ApiModelProperty(value = "记录更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recModifyDate;
} 