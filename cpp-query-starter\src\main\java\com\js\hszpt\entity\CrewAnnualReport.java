package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 船员年度报告主表
 * @ClassName: CrewAnnualReport
 * @Description: 船员年度报告主表实体类
 * @author: System Generation
 */
@Data
@TableName("dwdz_crew_ar_annual_report")
@ApiModel(value = "船员年度报告主表")
public class CrewAnnualReport {

    private static final long serialVersionUID = 1L;

    /**
     * 报告ID
     */
    @ApiModelProperty(value = "报告ID")
    private String reportId;

    /**
     * 船员ID
     */
    @ApiModelProperty(value = "船员ID")
    private String crewId;

    /**
     * 报告年度(YYYY)
     */
    @ApiModelProperty(value = "报告年度(YYYY)")
    private String reportYear;

    /**
     * 当年违法记分汇总
     */
    @ApiModelProperty(value = "当年违法记分汇总")
    private BigDecimal illegalScoreTotal;

    /**
     * 当前销分情况
     */
    @ApiModelProperty(value = "当前销分情况")
    private BigDecimal deductedScore;

    /**
     * 清分日期字符串
     */
    @ApiModelProperty(value = "清分日期字符串")
    private String clearDateStr;

    /**
     * 年度履职总天数
     */
    @ApiModelProperty(value = "年度履职总天数")
    private Integer workingDaysTotal;

    /**
     * 无限航区履职天数
     */
    @ApiModelProperty(value = "无限航区履职天数")
    private Integer unlimitedWorkingDays;

    /**
     * 沿海航区履职天数
     */
    @ApiModelProperty(value = "沿海航区履职天数")
    private Integer coastalWorkingDays;

    /**
     * 在船状态(true:在船/false:不在船)
     */
    @ApiModelProperty(value = "在船状态(true:在船/false:不在船)")
    private Boolean onboardFlag;

    /**
     * 船舶名称
     */
    @ApiModelProperty(value = "船舶名称")
    private String shipName;

    /**
     * 任职开始日期字符串
     */
    @ApiModelProperty(value = "任职开始日期字符串")
    private String dutyStartDateStr;

    /**
     * 任职地点
     */
    @ApiModelProperty(value = "任职地点")
    private String dutyLocation;

    /**
     * 任职职务
     */
    @ApiModelProperty(value = "任职职务")
    private String dutyPosition;

    /**
     * 记录创建日期（字符串格式）
     */
    @ApiModelProperty(value = "记录创建日期字符串")
    private String recCreateDateStr;

    /**
     * 记录修改日期（字符串格式）
     */
    @ApiModelProperty(value = "记录修改日期字符串")
    private String recModifyDateStr;

    /**
     * 推送状态
     */
    @ApiModelProperty(value = "推送状态")
    private String mobilePushStatus;

    /**
     * 获取清分日期
     * @return 清分日期
     */
    public LocalDate getClearDate() {
        return parseDate(clearDateStr);
    }

    /**
     * 设置清分日期
     * @param clearDate 清分日期
     */
    public void setClearDate(LocalDate clearDate) {
        if (clearDate != null) {
            this.clearDateStr = clearDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else {
            this.clearDateStr = null;
        }
    }

    /**
     * 获取任职开始日期
     * @return 任职开始日期
     */
    public LocalDate getDutyStartDate() {
        return parseDate(dutyStartDateStr);
    }

    /**
     * 设置任职开始日期
     * @param dutyStartDate 任职开始日期
     */
    public void setDutyStartDate(LocalDate dutyStartDate) {
        if (dutyStartDate != null) {
            this.dutyStartDateStr = dutyStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else {
            this.dutyStartDateStr = null;
        }
    }

    /**
     * 获取记录创建日期
     * @return 记录创建日期
     */
    public LocalDateTime getRecCreateDate() {
        return parseDateTime(recCreateDateStr);
    }

    /**
     * 获取记录修改日期
     * @return 记录修改日期
     */
    public LocalDateTime getRecModifyDate() {
        return parseDateTime(recModifyDateStr);
    }

    private LocalDate parseDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 支持多种日期格式
            if (dateStr.contains("-")) {
                return LocalDate.parse(dateStr.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (StrUtil.isBlank(dateTimeStr)) {
            return null;
        }
        try {
            // 支持多种日期时间格式
            if (dateTimeStr.contains(" ")) {
                return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else if (dateTimeStr.contains("T")) {
                return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } else {
                LocalDate date = parseDate(dateTimeStr);
                return date != null ? date.atStartOfDay() : null;
            }
        } catch (Exception e) {
            return null;
        }
    }
} 