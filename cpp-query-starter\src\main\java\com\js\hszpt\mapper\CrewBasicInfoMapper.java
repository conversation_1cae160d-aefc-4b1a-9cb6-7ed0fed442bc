package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.hszpt.entity.CrewBasicInfo;
import com.js.hszpt.vo.CrewListVO;
import com.js.hszpt.vo.CrewQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CrewBasicInfoMapper extends BaseMapper<CrewBasicInfo> {
    
    /**
     * 关联查询船员列表（包含当前状态信息）- 统一方法，支持分页和导出
     * 当queryVO.pageSize为0或负数时，表示导出模式，返回所有匹配结果
     * 当queryVO.pageSize大于0时，表示分页模式，返回分页结果
     */
    List<CrewListVO> selectCrewListWithStatusOptimized(@Param("queryVO") CrewQueryVO queryVO);
    
    /**
     * 查询港口列表
     */
    List<String> selectPorts(@Param("query") String query);
    
    /**
     * 查询船名列表
     */
    List<String> selectShips(@Param("query") String query);
    
    /**
     * 查询当前职务列表
     */
    List<String> selectPositions(@Param("query") String query);
} 