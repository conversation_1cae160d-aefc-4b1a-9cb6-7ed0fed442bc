package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.core.service.BaseService;
import com.js.hszpt.entity.DwdzCrewTimeline;

import java.util.List;
import java.util.Map;

/**
 * 船员动态履历时间轴宽表Service接口
 * 
 * <AUTHOR> Generation
 */
public interface DwdzCrewTimelineService extends IService<DwdzCrewTimeline> {

        /**
         * 根据船员ID查询时间轴数据
         * 
         * @param crewId 船员ID
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> getByCrewId(String crewId);

        /**
         * 根据船员ID和年份查询时间轴数据
         * 
         * @param crewId 船员ID
         * @param year   年份
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> getByCrewIdAndYear(String crewId, Integer year);

        /**
         * 根据船员ID和事件类型查询时间轴数据
         * 
         * @param crewId    船员ID
         * @param eventType 事件类型
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> getByCrewIdAndEventType(String crewId, String eventType);

        /**
         * 根据船员ID和事件类型查询时间轴数据
         *
         * @param crewId    船员ID
         * @param eventTypeList 事件类型列表
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> getByCrewIdAndEventTypeList(String crewId, List<String> eventTypeList);

        /**
         * 根据船员ID、事件类型、船舶类型、职务类型查询时间轴数据
         * 
         * @param crewId    船员ID
         * @param eventType 事件类型
         * @param shipType  船舶类型
         * @param dutyType  职务类型
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> getByCrewIdAndEventTypeWithConditions(String crewId, String eventType, String shipType,
                        String dutyType);

        /**
         * 获取时间轴数据（按年份分组）
         * 
         * @param crewId   船员ID
         * @param year     指定年份
         * @param page     页码
         * @param pageSize 每页数量
         * @return 时间轴数据
         */
        List<Map<String, Object>> getTimelineData(String crewId, Integer year, Integer page, Integer pageSize);

        /**
         * 获取履职交叉验证时间轴数据
         * 
         * @param crewId       船员ID
         * @param dateStart    开始日期
         * @param dateEnd      结束日期
         * @param dutyType     职务类型
         * @param shipType     船舶类型
         * @param dynamicType  动态类型
         * @param businessData 业务数据类型
         * @return 履职时间轴数据
         */
        Map<String, Object> getCrossValidationTimeline(String crewId, String dateStart, String dateEnd,
                        String dutyType, String shipType, String dynamicType, String businessData);

        /**
         * 获取船员履历时间轴数据V1（按年份分组）
         * 
         * @param crewId 船员ID
         * @param year   指定年份
         * @return 按年份分组的时间轴数据
         */
        List<Map<String, Object>> getTimelineDataV1(String crewId, Integer year);
}