package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.ResultUtil;
import com.js.hszpt.entity.CrewPositionCategory;
import com.js.hszpt.mapper.CrewPositionCategoryMapper;
import com.js.hszpt.service.CrewPositionCategoryService;
import com.js.core.common.vo.Result;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 船员职务名称分类表Service实现类
 */
@Service
public class CrewPositionCategoryServiceImpl extends ServiceImpl<CrewPositionCategoryMapper, CrewPositionCategory>
        implements CrewPositionCategoryService {

    private static final Logger logger = LoggerFactory.getLogger(CrewPositionCategoryServiceImpl.class);

    /**
     * 获取所有职务分类列表
     */
    @Override
    public Result getAllPositionCategories() {
        try {
            // 调用mapper查询所有数据，按排序字段排序
            List<CrewPositionCategory> list = this.baseMapper.selectAllOrderBySort();
            return ResultUtil.data(list, "查询职务分类列表成功");
        } catch (Exception e) {
            logger.error("查询职务分类列表失败", e);
            return ResultUtil.error("查询职务分类列表失败：" + e.getMessage());
        }
    }
}