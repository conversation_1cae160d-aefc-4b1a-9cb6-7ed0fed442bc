package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewFeatureDict;
import com.js.hszpt.vo.TagValueSuggestionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * @ClassName: DwdzCrewFeatureDictDao   
 * @Description: 船员特征值字典表数据处理层
 * @author: System Generation 
 */
public interface DwdzCrewFeatureDictDao extends BaseMapper<DwdzCrewFeatureDict> {

    /**
     * 根据标签编码和查询文本获取标签值建议列表
     * 
     * @param tagCode 标签编码
     * @param queryText 查询文本
     * @param limit 限制返回条数
     * @return 标签值建议列表
     */
    List<TagValueSuggestionVo> getTagValueSuggestions(
            @Param("tagCode") String tagCode,
            @Param("queryText") String queryText,
            @Param("limit") Integer limit
    );
}
