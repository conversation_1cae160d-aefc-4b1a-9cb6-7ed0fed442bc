package com.js.hszpt.enmus;

import cn.hutool.core.util.StrUtil;

/**
 * 证书类别枚举
 */
public enum CertificateTypeEnum {

    COMPETENCE("COMPETENCE", "适任证书"),
    QUALIFICATION("QUALIFICATION", "培训合格证"),
    HEALTH("HEALTH", "健康证");

    private final String code;
    private final String description;

    CertificateTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取描述
     * 
     * @param code 证书类别代码
     * @return 证书类别描述，如果找不到则返回原代码
     */
    public static String getDescriptionByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        for (CertificateTypeEnum type : CertificateTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getDescription();
            }
        }

        // 如果找不到对应的枚举值，返回原代码
        return code;
    }

    /**
     * 根据代码获取枚举对象
     * 
     * @param code 证书类别代码
     * @return 证书类别枚举对象，如果找不到则返回null
     */
    public static CertificateTypeEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }

        for (CertificateTypeEnum type : CertificateTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static String getCodeByDescription(String description) {
        if (StrUtil.isBlank(description)) {
            return null;
        }

        for (CertificateTypeEnum type : CertificateTypeEnum.values()) {
            if (type.getDescription().equals(description)) {
                return type.getCode();
            }
        }

        return null;
    }
}