package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 晋升要求表实体类
 * 用于管理各职务晋升的具体要求信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_position_promo_req")
public class DwdzCppPositionPromotionReq {

    /**
     * 晋升要求唯一标识ID（主键）
     */
    @TableId(value = "requirement_id", type = IdType.ASSIGN_UUID)
    private String requirementId;

    /**
     * 当前证书职务分类id
     */
    @TableField("dq_subcategory_id")
    private String dqSubcategoryId;

    /**
     * 晋升目标证书职务分类id
     */
    @TableField("mb_subcategory_id")
    private String mbSubcategoryId;

    /**
     * 证书类别 0(海船船员适任证书) 2(海上游艇操作人员) 5(培训合格证)
     */
    @TableField("cert_type")
    private Integer certType;

    /**
     * 考试科目id列表，多条数据用逗号隔开
     */
    @TableField("kskmlb")
    private String kskmlb;

    /**
     * 培训合格证项目选择id列表，多条数据用逗号隔开
     */
    @TableField("pxhgzxmxz")
    private String pxhgzxmxz;

    /**
     * 申请条件基本内容
     */
    @TableField("sqtjjbnr")
    private String sqtjjbnr;

    /**
     * 申请条件补充内容
     */
    @TableField("sqtjbcnr")
    private String sqtjbcnr;

    /**
     * 申请条件附件
     */
    @TableField("sqtjfj")
    private String sqtjfj;

    /**
     * 备注
     */
    @TableField("bz")
    private String bz;

    /**
     * 证书申请形式 
     * 0：职务晋升　1：学生考试发证 2：航区扩大 3:功率提高　4：军转海 
     * 5:内转海 6:渔转海 7:再有效　8:职务签证　10:吨位提高　11:取消限制　
     * 9:初次申请(游艇 12:变更范围(游艇)
     */
    @TableField("relation_type")
    private Integer relationType;

    /**
     * 证书申请形式展示名称
     */
    @TableField("zssqxxzsmc")
    private String zssqxxzsmc;

    /**
     * 证书申请条件类型 
     * 0：要求总览 1：年龄要求　2：健康要求　3：资历要求　4：培训要求
     * 5：考试要求　6：见习要求 7：培训合格证要求　9：其他说明
     */
    @TableField("zssqtjlx")
    private Integer zssqtjlx;

    /**
     * 证书申请条件类型名称展示
     */
    @TableField("zssqtjlxmczs")
    private String zssqtjlxmczs;

    /**
     * 职务晋升关系备注信息
     */
    @TableField("zwjsgxbzxx")
    private String zwjsgxbzxx;

    /**
     * 排序序号（用于同一晋升来源下的多个晋升目标排序）
     */
    @TableField("sort_order")
    private BigDecimal sortOrder;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    // ==================== 关联查询字段（非数据库字段） ====================

    /**
     * 考试科目列表（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private List<Object> examSubjects;

    /**
     * 培训项目列表（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private List<Object> trainSubjects;

    /**
     * 当前职务分类名称（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private String dqSubcategoryName;

    /**
     * 目标职务分类名称（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private String mbSubcategoryName;
} 