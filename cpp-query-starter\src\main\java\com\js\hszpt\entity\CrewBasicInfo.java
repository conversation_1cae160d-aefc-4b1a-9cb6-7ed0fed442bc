package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 船员基本信息表
 */
@Data
@TableName("dwdz_crew_basic_info")
public class CrewBasicInfo {
    /**
     * 船员唯一识别ID
     */
    @TableId("crew_id")
    private String crewId;

    /**
     * 船员真实姓名
     */
    @TableField("full_name")
    private String fullName;

    /**
     * 船员曾用名（可选）
     */
    @TableField("used_name")
    private String usedName;

    /**
     * 18位身份证号码
     */
    @TableField("id_number")
    private String idNumber;

    /**
     * 性别（男/女）
     */
    @TableField("gender")
    private String gender;

    /**
     * 出生年月日
     */
    @TableField("birth_date")
    private Date birthDate;

    /**
     * 籍贯（精确到市县）
     */
    @TableField("native_place")
    private String nativePlace;

    /**
     * 国籍（默认中国）
     */
    @TableField("nationality")
    private String nationality;

    /**
     * 民族（默认汉族）
     */
    @TableField("nation")
    private String nation;

    /**
     * 常用手机号码
     */
    @TableField("phone")
    private String phone;

    /**
     * 现居住地址
     */
    @TableField("address")
    private String address;

    /**
     * 最高学历（如本科/专科）
     */
    @TableField("education")
    private String education;

    /**
     * 学位（学士/硕士等）
     */
    @TableField("degree")
    private String degree;

    /**
     * 毕业院校全称
     */
    @TableField("graduate_school")
    private String graduateSchool;

    /**
     * 所学专业
     */
    @TableField("major")
    private String major;

    /**
     * 毕业日期
     */
    @TableField("graduation_date")
    private Date graduationDate;

    /**
     * 毕业证书编号
     */
    @TableField("diploma_number")
    private String diplomaNumber;

    /**
     * 信息采集地点（如海事局）
     */
    @TableField("collection_place")
    private String collectionPlace;

    /**
     * 注册登记机构
     */
    @TableField("register_org")
    private String registerOrg;

    /**
     * 证件照存储路径
     */
    @TableField("photo_url")
    private String photoUrl;

    /**
     * 应急联系人
     */
    @TableField("emergency_contact")
    private String emergencyContact;

    /**
     * 应急联系方式
     */
    @TableField("emergency_phone")
    private String emergencyPhone;

    /**
     * 数据创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 数据创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 最后更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
} 