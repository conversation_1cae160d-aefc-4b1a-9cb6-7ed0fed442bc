package com.js.hszpt.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据脱敏工具类
 * 用于对敏感信息进行脱敏处理
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class DataMaskingUtil {

    /**
     * 身份证号脱敏处理
     * 保留前3位和后4位，中间用*号替换
     * 例如：110101199001011234 -> 110***********1234
     *
     * @param idNumber 身份证号
     * @param keepStart 保留前面的字符数
     * @param keepEnd 保留后面的字符数
     * @return 脱敏后的身份证号
     */
    public static String maskIdNumber(String idNumber, int keepStart, int keepEnd) {
        if (StringUtils.isBlank(idNumber)) {
            return idNumber;
        }

        return hideString(idNumber, keepStart, keepEnd);
    }

    /**
     * 身份证号脱敏处理
     * 保留前3位和后4位，中间用*号替换
     * 例如：110101199001011234 -> 110***********1234
     *
     * @param idNumber 身份证号
     * @return 脱敏后的身份证号
     */
    public static String maskIdNumber(String idNumber) {
        if (StringUtils.isBlank(idNumber)) {
            return idNumber;
        }

        // 18位身份证：保留前3位和后4位
        if (idNumber.length() == 18) {
            return hideString(idNumber, 3, 4);
        }

        // 15位身份证：保留前3位和后2位
        if (idNumber.length() == 15) {
            return hideString(idNumber, 3, 2);
        }

        // 其他长度：保留前3位和后2位
        return hideString(idNumber, 3, 2);
    }

    /**
     * 手机号脱敏处理
     * 保留前3位和后4位，中间用*号替换
     * 例如：13812345678 -> 138****5678
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (StringUtils.isBlank(phone) || phone.length() < 7) {
            return phone;
        }
        return hideString(phone, 3, 4);
    }

    /**
     * 姓名脱敏处理
     * 保留第一个字符，其余用*号替换
     * 例如：张三 -> 张*，张三丰 -> 张**
     *
     * @param name 姓名
     * @return 脱敏后的姓名
     */
    public static String maskName(String name) {
        if (StringUtils.isBlank(name) || name.length() <= 1) {
            return name;
        }
        return hideString(name, 1, 0);
    }

    /**
     * 邮箱脱敏处理
     * 保留@前的前两位和@后的内容
     * 例如：<EMAIL> -> te***@example.com
     *
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email) || !email.contains("@")) {
            return email;
        }

        String[] parts = email.split("@");
        if (parts.length != 2 || parts[0].length() <= 2) {
            return email;
        }

        String maskedPrefix = hideString(parts[0], 2, 0);
        return maskedPrefix + "@" + parts[1];
    }

    /**
     * 银行卡号脱敏处理
     * 保留前4位和后4位，中间用*号替换
     * 例如：**************** -> 6214********3456
     *
     * @param cardNumber 银行卡号
     * @return 脱敏后的银行卡号
     */
    public static String maskBankCard(String cardNumber) {
        if (StringUtils.isBlank(cardNumber) || cardNumber.length() < 8) {
            return cardNumber;
        }
        return hideString(cardNumber, 4, 4);
    }

    /**
     * 地址脱敏处理
     * 保留前6个字符，其余用*号替换
     * 例如：北京市朝阳区某某街道123号 -> 北京市朝阳区***
     *
     * @param address 地址
     * @return 脱敏后的地址
     */
    public static String maskAddress(String address) {
        if (StringUtils.isBlank(address) || address.length() <= 6) {
            return address;
        }
        return hideString(address, 6, 0);
    }

    /**
     * 字符串脱敏处理核心方法
     * 保留前面keepStart位和后面keepEnd位，中间用*号替换
     *
     * @param str       原始字符串
     * @param keepStart 保留前面的字符数
     * @param keepEnd   保留后面的字符数
     * @return 脱敏后的字符串
     */
    private static String hideString(String str, int keepStart, int keepEnd) {
        if (StringUtils.isBlank(str)) {
            return str;
        }

        int length = str.length();

        // 如果字符串长度小于等于需要保留的总位数，直接返回原字符串
        if (length <= keepStart + keepEnd) {
            return str;
        }

        // 构建脱敏字符串
        StringBuilder masked = new StringBuilder();

        // 保留前面的字符
        if (keepStart > 0) {
            masked.append(str.substring(0, keepStart));
        }

        // 中间用*号替换
        int maskLength = length - keepStart - keepEnd;
        for (int i = 0; i < maskLength; i++) {
            masked.append("*");
        }

        // 保留后面的字符
        if (keepEnd > 0) {
            masked.append(str.substring(length - keepEnd));
        }

        return masked.toString();
    }
}