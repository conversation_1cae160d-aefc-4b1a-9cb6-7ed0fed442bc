#!/bin/sh

APP_NAME=jsdp-fast-starter
APP_HOME=`cd $(dirname $0) && pwd`
APP_LOG_DIR=${APP_HOME}/logs
APP_PID_FILE=${APP_HOME}/application.pid
BOOT_JAR=`echo "${APP_HOME}"/${APP_NAME}*.jar`

# 通过 application.pid 关闭程序
echo "Trying shutdown application through pid file"
if [[ -f "${APP_HOME}/application.pid" ]] ; then
  PID="$(cat "${APP_HOME}/application.pid")"
  kill -9 ${PID}
  echo "Shutdown process [${PID}] successful"
  rm -fr "${APP_HOME}/application.pid"
  exit 0
fi

# 通过 jar 包名称 ps 查询关闭程序
echo "Pid file not found, try shutdown with application jar's name"
PID=`ps -ef | grep ${BOOT_JAR} | grep -v "grep" | grep -v "shutdown" | awk '{print $2}'`
if [[ x"$PID" != x"" ]] ; then
  kill -9 ${PID}
  echo "Shutdown process [${PID}] successful"
  exit 0
fi

echo "Application should be not running, do nothing."
