package com.js.hszpt.service;

import java.util.Map;

/**
 * 船员群体分类服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface SeafarerGroupsService {
    
    /**
     * 获取船员数据
     * @return 船员数据
     */
    Map<String, Object> getSeafarerData();
    
    /**
     * 获取颜色配置
     * @return 颜色配置
     */
    Map<String, Object> getSeafarerColors();
    
    /**
     * 获取图表配置
     * @return 图表配置
     */
    Map<String, Object> getChartConfig();
} 