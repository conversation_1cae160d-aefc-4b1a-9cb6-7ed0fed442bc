package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员在不同船舶类型上的履职统计表
 */
@Data
@TableName("dwdz_crew_ship_service")
@ApiModel(value = "船员船舶类型履职统计表")
public class DwdzCrewShipService {
    @TableId
    @ApiModelProperty(value = "船舶类型履职记录唯一标识")
    private String serviceId;

    @ApiModelProperty(value = "关联船员基本信息表的船员ID")
    private String crewId;

    @ApiModelProperty(value = "船舶类型（如：散货船、集装箱船、油轮等）")
    private String shipType;

    @ApiModelProperty(value = "履职总月数（含折算月数，保留一位小数）")
    private BigDecimal totalMonths;

    @ApiModelProperty(value = "记录创建日期")
    private Date recCreateDate;

    @ApiModelProperty(value = "记录修改日期")
    private Date recModifyDate;
} 