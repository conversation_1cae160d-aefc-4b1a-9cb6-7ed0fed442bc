package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewExamConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员考试信息配置表DAO接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCrewExamConfigDao extends BaseMapper<DwdzCrewExamConfig> {

    /**
     * 根据考试ID列表查询考试科目信息
     * @param examIds 考试ID列表
     * @return 考试科目信息列表
     */
    List<DwdzCrewExamConfig> selectByExamIds(@Param("examIds") List<String> examIds);
} 