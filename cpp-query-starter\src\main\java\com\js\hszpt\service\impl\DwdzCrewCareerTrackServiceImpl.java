package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewCareerTrack;
import com.js.hszpt.mapper.DwdzCrewCareerTrackMapper;
import com.js.hszpt.service.DwdzCrewCareerTrackService;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 船员职业生涯成长轨迹表Service实现
 */
@Service
public class DwdzCrewCareerTrackServiceImpl extends ServiceImpl<DwdzCrewCareerTrackMapper, DwdzCrewCareerTrack> implements DwdzCrewCareerTrackService {
    /**
     * 根据船员ID查询成长轨迹（按track_order升序）
     * @param crewId 船员ID
     * @return 成长轨迹列表
     */
    @Override
    public List<DwdzCrewCareerTrack> getCareerTrackByCrewId(String crewId) {
        QueryWrapper<DwdzCrewCareerTrack> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("crew_id", crewId)
                   .orderByAsc("track_order");
        return baseMapper.selectList(queryWrapper);
    }
} 