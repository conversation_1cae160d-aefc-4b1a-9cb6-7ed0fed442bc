package com.js.hszpt.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.js.hszpt.entity.DwdzCppCertificatePath;
import com.js.hszpt.entity.DwdzCppPositionSubcategory;
import com.js.hszpt.entity.DwdzCppPositionPromotionReq;
import com.js.hszpt.entity.DwdzCrewCertificatePath;
import com.js.hszpt.mapper.DwdzCppCertificatePathDao;
import com.js.hszpt.mapper.DwdzCppPositionSubcategoryDao;
import com.js.hszpt.mapper.DwdzCppPositionPromotionReqDao;
import com.js.hszpt.mapper.DwdzCrewCertificatePathDao;
import com.js.hszpt.service.CertificatePathService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 证书路径服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@DS("dzzzdwdz")
public class CertificatePathServiceImpl implements CertificatePathService {

    @Autowired
    private DwdzCppCertificatePathDao certificatePathDao;

    @Autowired
    private DwdzCppPositionSubcategoryDao positionSubcategoryDao;

    @Autowired
    private DwdzCppPositionPromotionReqDao positionPromotionReqDao;

    @Autowired
    private DwdzCrewCertificatePathDao crewCertificatePathDao;

    @Override
    public Map<String, Object> getCertificatePath(String certificateType) {
        log.info("开始获取证书路径数据，证书类型：{}", certificateType);
        
        try {
            // 第一步：根据根节点名称查询所有非叶子节点（parent_id != node_id）
            List<DwdzCppCertificatePath> nonLeafNodes = certificatePathDao.getNodesByRootName(certificateType);
            log.info("第一步完成：查询到{}个非叶子节点", nonLeafNodes.size());
            
            if (nonLeafNodes == null || nonLeafNodes.isEmpty()) {
                log.warn("未找到非叶子节点，证书类型：{}", certificateType);
                return null;
            }
            
            // 第二步：在Java方法中构建成一棵树
            Map<String, Object> treeResult = buildCertificatePathTree(nonLeafNodes, certificateType);
            log.info("第二步完成：构建树形结构");
            
            // 第三步：查询叶子节点（parent_id = node_id）并合并到树中
            List<DwdzCppCertificatePath> leafNodes = certificatePathDao.getLeafNodesByCertType(certificateType);
            log.info("第三步开始：查询到{}个叶子节点", leafNodes.size());
            
            if (leafNodes != null && !leafNodes.isEmpty()) {
                // 将叶子节点合并到树形结构中
                mergeLeafNodesIntoTree(treeResult, leafNodes);
                log.info("第三步完成：叶子节点已合并到树中");
            }
            
            log.info("获取证书路径数据成功，总节点数：{}", nonLeafNodes.size() + leafNodes.size());
            return treeResult;
            
        } catch (Exception e) {
            log.error("获取证书路径数据失败，根节点名称：{}，错误：{}", certificateType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> searchCertificateNodes(String keyword, Integer limit) {
        log.info("开始搜索证书节点，关键词：{}，限制数量：{}", keyword, limit);
        
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> nodes = new ArrayList<>();
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                // 从数据库搜索证书节点
                List<DwdzCppCertificatePath> dbNodes = certificatePathDao.searchNodesByKeyword(keyword, limit);
                
                // 转换为API返回格式
                for (DwdzCppCertificatePath node : dbNodes) {
                    Map<String, Object> nodeMap = convertToNodeMap(node);
                    nodes.add(nodeMap);
                }
            }
            
            result.put("nodes", nodes);
            result.put("total", nodes.size());
            result.put("keyword", keyword);
            
            log.info("搜索证书节点成功，找到{}个结果", nodes.size());
            return result;
            
        } catch (Exception e) {
            log.error("搜索证书节点失败，关键词：{}，错误：{}", keyword, e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("nodes", new ArrayList<>());
            result.put("total", 0);
            result.put("keyword", keyword);
            return result;
        }
    }

    @Override
    public Map<String, Object> getCertificatePathFlat(String certificateType) {
        log.info("开始获取证书路径数据（平铺查询），证书类型：{}", certificateType);
        
        try {
            // 第一步：使用平铺查询获取所有相关节点
            List<DwdzCppCertificatePath> allNodes = certificatePathDao.getAllNodesByRootName(certificateType);
            log.info("平铺查询完成：查询到{}个节点", allNodes.size());
            
            if (allNodes == null || allNodes.isEmpty()) {
                log.warn("未找到相关节点，证书类型：{}", certificateType);
                return null;
            }
            
            // 第二步：在Java中构建树形结构（只保留node_id、parent_id和必要的树字段）
            Map<String, Object> treeStructure = buildLightweightTreeFromFlatNodes(allNodes, certificateType);
            log.info("Java构建轻量级树完成，根节点：{}", certificateType);
            
            // 第三步：构建节点数据映射表，供前端根据node_id和parent_id查询显示信息
            List<Map<String, Object>> nodeDataList = buildNodeDataList(allNodes);
            log.info("构建节点数据映射表完成，共{}个节点", nodeDataList.size());
            
            // 组装最终返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("tree", treeStructure);           // 轻量级树结构
            result.put("nodeData", nodeDataList);        // 节点数据映射表
            result.put("totalNodes", allNodes.size());   // 总节点数
            
            log.info("获取证书路径数据（平铺查询）成功，树结构+节点数据映射表");
            return result;
            
        } catch (Exception e) {
            log.error("获取证书路径数据（平铺查询）失败，证书类型：{}，错误：{}", certificateType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getCertificatePromotionRequirements(Map<String, Object> requestData) {
        log.info("开始获取证书晋升要求详情，请求数据：{}", requestData);
        
        try {
            // 从请求数据中获取节点信息
            String nodeId = (String) requestData.get("id");
            String nodeName = (String) requestData.get("name");
            String nodeType = (String) requestData.get("node_type");
            String parentId = (String) requestData.get("parent_id");
            
            // 检查节点类型，只有证书职务节点（node_type = "0"）才能查看晋升要求
            if (!"0".equals(nodeType)) {
                log.warn("该节点不是证书职务节点，无法查看晋升要求：nodeType={}", nodeType);
                return buildEmptyPromotionRequirements();
            }
            
            if (nodeId == null || nodeId.trim().isEmpty()) {
                log.warn("请求数据中缺少id字段");
                return buildEmptyPromotionRequirements();
            }
            
            log.info("查询证书职务节点的晋升要求：nodeId={}, nodeName={}, parentId={}", nodeId, nodeName, parentId);
            
            // 使用节点ID和父节点ID查询晋升要求
            // dq_subcategory_id对应parent_id，mb_subcategory_id对应node_id
            String dqSubcategoryId = parentId;
            String mbSubcategoryId = nodeId;
            
            // 如果有父节点ID，查询对应的晋升要求
            if (dqSubcategoryId != null && !dqSubcategoryId.trim().isEmpty()) {
                List<DwdzCppPositionPromotionReq> promotionReqs = positionPromotionReqDao.getRequirementsByIds(dqSubcategoryId, mbSubcategoryId);
                if (promotionReqs != null && !promotionReqs.isEmpty()) {
                    log.info("找到晋升要求记录，开始构建返回数据");
                    return buildPromotionRequirementsFromEntity(promotionReqs.get(0)); // 取第一个记录
                }
            }
            
            // 如果没有找到具体的晋升要求，尝试根据节点名称查询
            if (nodeName != null && !nodeName.trim().isEmpty()) {
                log.info("根据节点名称查询晋升要求：{}", nodeName);
                DwdzCppPositionSubcategory subcategory = positionSubcategoryDao.getSubcategoryByName(nodeName);
                if (subcategory != null) {
                    String subcategoryId = subcategory.getSubcategoryId();
                    log.info("找到职务子分类ID：{}", subcategoryId);
                    
                    DwdzCppPositionPromotionReq promotionReq = positionPromotionReqDao.getRequirementBySubcategoryId(subcategoryId);
                    if (promotionReq != null) {
                        log.info("找到晋升要求记录，开始构建返回数据");
                        return buildPromotionRequirementsFromEntity(promotionReq);
                    }
                }
            }
            
            log.warn("未找到对应的晋升要求：nodeId={}, nodeName={}", nodeId, nodeName);
            return buildEmptyPromotionRequirements();
            
        } catch (Exception e) {
            log.error("获取证书晋升要求详情失败，错误：{}", e.getMessage(), e);
            return buildEmptyPromotionRequirements();
        }
    }

    /**
     * 构建证书路径树形结构
     */
    private Map<String, Object> buildCertificatePathTree(List<DwdzCppCertificatePath> nodes, String certificateType) {
        // 找到根节点（node_name匹配传入的certificateType）
        DwdzCppCertificatePath rootNode = nodes.stream()
            .filter(node -> certificateType.equals(node.getNodeName()))
            .findFirst()
            .orElse(null);
        
        if (rootNode == null) {
            // 如果没有找到根节点，返回空结构
            Map<String, Object> result = new HashMap<>();
            result.put("id", "empty");
            result.put("name", certificateType);
            result.put("type", "certificate");
            result.put("children", new ArrayList<>());
            return result;
        }
        
        // 构建完整的树形结构
        return buildNodeWithChildren(rootNode, nodes);
    }
    
    /**
     * 构建节点及其子节点的完整树形结构
     */
    private Map<String, Object> buildNodeWithChildren(DwdzCppCertificatePath node, List<DwdzCppCertificatePath> allNodes) {
        Map<String, Object> nodeMap = new HashMap<>();
        
        // 基础字段映射
        nodeMap.put("id", node.getNodeId());
        nodeMap.put("name", node.getNodeName());
        nodeMap.put("node_type", node.getNodeType()); // 0:证书职务 1:普通分类
        nodeMap.put("cert_type", node.getCertType());
        nodeMap.put("parent_id", node.getParentId());
        
        // 申请关系形式相关字段
        if (node.getRelationType() != null) {
            nodeMap.put("relation_type", node.getRelationType());
        }
        
        if (node.getSqxszsmc() != null && !node.getSqxszsmc().trim().isEmpty()) {
            nodeMap.put("sqxszsmc", node.getSqxszsmc());
        }
        
        if (node.getSortOrder() != null) {
            nodeMap.put("sort_order", node.getSortOrder());
        }
        
        // 检查是否为叶子节点（parent_id和node_id相等或者没有子节点）
        boolean isLeafNode = node.getParentId() != null && node.getParentId().equals(node.getNodeId());
        
        if (!isLeafNode) {
            // 查找并构建子节点
            List<DwdzCppCertificatePath> childNodes = allNodes.stream()
                .filter(child -> node.getNodeId().equals(child.getParentId()) && !child.getNodeId().equals(child.getParentId()))
                .sorted((a, b) -> {
                    // 按排序字段排序
                    if (a.getSortOrder() != null && b.getSortOrder() != null) {
                        return a.getSortOrder().compareTo(b.getSortOrder());
                    }
                    return 0;
                })
                .collect(java.util.stream.Collectors.toList());
            
            if (!childNodes.isEmpty()) {
                List<Map<String, Object>> children = new ArrayList<>();
                for (DwdzCppCertificatePath child : childNodes) {
                    children.add(buildNodeWithChildren(child, allNodes));
                }
                nodeMap.put("children", children);
            }
        }
        
        return nodeMap;
    }

    /**
     * 将叶子节点合并到树形结构中
     */
    private void mergeLeafNodesIntoTree(Map<String, Object> treeRoot, List<DwdzCppCertificatePath> leafNodes) {
        if (leafNodes == null || leafNodes.isEmpty()) {
            return;
        }
        
        log.info("开始合并{}个叶子节点到树中", leafNodes.size());
        
        // 为每个叶子节点找到其应该归属的父节点并添加
        for (DwdzCppCertificatePath leafNode : leafNodes) {
            log.debug("处理叶子节点：{}, parent_id=node_id={}", leafNode.getNodeName(), leafNode.getNodeId());
            
            // 在树中找到合适的位置添加这个叶子节点
            addLeafNodeToTree(treeRoot, leafNode);
        }
        
        log.info("叶子节点合并完成");
    }
    
    /**
     * 递归查找并添加叶子节点到树中合适的位置
     */
    @SuppressWarnings("unchecked")
    private boolean addLeafNodeToTree(Map<String, Object> node, DwdzCppCertificatePath leafNode) {
        // 获取当前节点的信息
        String nodeId = (String) node.get("id");
        String nodeType = (String) node.get("node_type");
        String nodeName = (String) node.get("name");
        
        // 判断叶子节点是否应该添加到当前节点下
        // 这里可以根据业务逻辑调整判断条件
        if (shouldAddLeafToThisNode(node, leafNode)) {
            // 获取或创建children列表
            List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("children");
            if (children == null) {
                children = new ArrayList<>();
                node.put("children", children);
            }
            
            // 构建叶子节点的数据结构并添加
            Map<String, Object> leafNodeMap = buildLeafNodeData(leafNode);
            children.add(leafNodeMap);
            
            log.debug("叶子节点 {} 已添加到节点 {} 下", leafNode.getNodeName(), nodeName);
            return true;
        }
        
        // 递归查找子节点
        List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("children");
        if (children != null) {
            for (Map<String, Object> child : children) {
                if (addLeafNodeToTree(child, leafNode)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 判断叶子节点是否应该添加到当前节点下
     * 可以根据具体业务需求调整这个逻辑
     */
    private boolean shouldAddLeafToThisNode(Map<String, Object> parentNode, DwdzCppCertificatePath leafNode) {
        // 简单的匹配逻辑：都是证书职务类型的节点可以匹配
        String parentNodeType = (String) parentNode.get("node_type");
        String leafNodeType = leafNode.getNodeType();
        
        // 如果父节点是证书职务类型，并且叶子节点也是证书职务类型
        if ("0".equals(parentNodeType) && "0".equals(leafNodeType)) {
            // 可以添加更复杂的匹配逻辑，比如名称相似度等
            return true;
        }
        
        return false;
    }
    
    /**
     * 构建叶子节点的数据结构
     */
    private Map<String, Object> buildLeafNodeData(DwdzCppCertificatePath leafNode) {
        Map<String, Object> nodeMap = new HashMap<>();
        
        // 基础字段映射
        nodeMap.put("id", leafNode.getNodeId());
        nodeMap.put("name", leafNode.getNodeName());
        nodeMap.put("node_type", leafNode.getNodeType());
        nodeMap.put("cert_type", leafNode.getCertType());
        nodeMap.put("parent_id", leafNode.getParentId());
        
        // 申请关系形式相关字段
        if (leafNode.getRelationType() != null) {
            nodeMap.put("relation_type", leafNode.getRelationType());
        }
        
        if (leafNode.getSqxszsmc() != null && !leafNode.getSqxszsmc().trim().isEmpty()) {
            nodeMap.put("sqxszsmc", leafNode.getSqxszsmc());
        }
        
        if (leafNode.getSortOrder() != null) {
            nodeMap.put("sort_order", leafNode.getSortOrder());
        }
        
        // 叶子节点没有子节点
        nodeMap.put("children", new ArrayList<>());
        
        return nodeMap;
    }

    /**
     * 将数据库节点转换为API返回格式（用于搜索结果）
     */
    private Map<String, Object> convertToNodeMap(DwdzCppCertificatePath node) {
        Map<String, Object> nodeMap = new HashMap<>();
        
        // 基础字段
        nodeMap.put("id", node.getNodeId());
        nodeMap.put("name", node.getNodeName());
        nodeMap.put("node_type", node.getNodeType());
        nodeMap.put("cert_type", node.getCertType());
        nodeMap.put("parent_id", node.getParentId());
        
        // 可选字段
        if (node.getRelationType() != null) {
            nodeMap.put("relation_type", node.getRelationType());
        }
        
        if (node.getSqxszsmc() != null && !node.getSqxszsmc().trim().isEmpty()) {
            nodeMap.put("sqxszsmc", node.getSqxszsmc());
        }
        
        if (node.getSortOrder() != null) {
            nodeMap.put("sort_order", node.getSortOrder());
        }
        
        // 构建路径信息（用于搜索结果显示）
        if (node.getPath() != null) {
            nodeMap.put("path", node.getPath());
        }
        
        return nodeMap;
    }

    /**
     * 基于数据库节点构建晋升要求信息
     */
    private Map<String, Object> buildPromotionRequirementsFromEntity(DwdzCppPositionPromotionReq promotionReq) {
        Map<String, Object> result = new HashMap<>();
        
        // 使用新的字段结构构建晋升要求
        List<String> basicRequirements = new ArrayList<>();
        List<String> supplementaryRequirements = new ArrayList<>();
        List<String> notes = new ArrayList<>();
        
        // 基本申请条件内容
        if (promotionReq.getSqtjjbnr() != null && !promotionReq.getSqtjjbnr().trim().isEmpty()) {
            basicRequirements.addAll(parseRequirementText(promotionReq.getSqtjjbnr()));
        }
        
        // 补充申请条件内容
        if (promotionReq.getSqtjbcnr() != null && !promotionReq.getSqtjbcnr().trim().isEmpty()) {
            supplementaryRequirements.addAll(parseRequirementText(promotionReq.getSqtjbcnr()));
        }
        
        // 备注信息
        if (promotionReq.getBz() != null && !promotionReq.getBz().trim().isEmpty()) {
            notes.addAll(parseRequirementText(promotionReq.getBz()));
        }
        
        // 职务晋升关系备注信息
        if (promotionReq.getZwjsgxbzxx() != null && !promotionReq.getZwjsgxbzxx().trim().isEmpty()) {
            notes.addAll(parseRequirementText(promotionReq.getZwjsgxbzxx()));
        }
        
        // 构建返回结果，保持与前端期望的格式一致
        result.put("overview", basicRequirements);
        result.put("age", new ArrayList<>());
        result.put("health", new ArrayList<>());
        result.put("experience", supplementaryRequirements);
        result.put("training", new ArrayList<>());
        result.put("certificates", new ArrayList<>());
        result.put("notes", notes);
        
        return result;
    }

    /**
     * 解析要求文本为列表，支持多种分隔符（备用方法）
     */
    private List<String> parseRequirementText(String text) {
        List<String> requirements = new ArrayList<>();
        
        if (text != null && !text.trim().isEmpty()) {
            // 支持换行符、分号、中文分号作为分隔符
            String[] parts = text.split("[\\n\\r；;]+");
            for (String part : parts) {
                String trimmed = part.trim();
                if (!trimmed.isEmpty()) {
                    requirements.add(trimmed);
                }
            }
        }
        
        return requirements;
    }

    /**
     * 构建空的晋升要求返回结果
     */
    private Map<String, Object> buildEmptyPromotionRequirements() {
        Map<String, Object> result = new HashMap<>();
        result.put("overview", new ArrayList<>());
        result.put("age", new ArrayList<>());
        result.put("health", new ArrayList<>());
        result.put("experience", new ArrayList<>());
        result.put("training", new ArrayList<>());
        result.put("certificates", new ArrayList<>());
        result.put("notes", new ArrayList<>());
        return result;
    }

    /**
     * 从平铺节点数据构建树形结构
     */
    private Map<String, Object> buildTreeFromFlatNodes(List<DwdzCppCertificatePath> allNodes, String rootNodeName) {
        log.info("开始从{}个平铺节点构建树形结构", allNodes.size());
        
        // 找到根节点（可能有多个同名的根节点，取第一个）
        DwdzCppCertificatePath rootNode = allNodes.stream()
            .filter(node -> rootNodeName.equals(node.getNodeName()))
            .findFirst()
            .orElse(null);
        
        if (rootNode == null) {
            log.warn("未找到根节点：{}", rootNodeName);
            // 返回空的树结构
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("id", "empty");
            emptyResult.put("name", rootNodeName);
            emptyResult.put("node_type", "1");
            emptyResult.put("children", new ArrayList<>());
            return emptyResult;
        }
        
        log.info("找到根节点：{} (ID: {})", rootNode.getNodeName(), rootNode.getNodeId());
        
        // 递归构建树形结构，使用Set记录已访问的节点（用完整的节点信息避免重复处理）
        Map<String, Object> result = buildNodeTreeFromFlatWithLevel(rootNode, allNodes, new HashSet<>());
        log.info("树形结构构建完成");
        
        return result;
    }
    
    /**
     * 递归构建节点树（从平铺数据），逐级查找避免node_id重复问题
     */
    private Map<String, Object> buildNodeTreeFromFlatWithLevel(DwdzCppCertificatePath currentNode, 
                                                              List<DwdzCppCertificatePath> allNodes, 
                                                              Set<String> visitedNodeKeys) {
        
        // 构建当前节点的唯一标识（使用node_id）
        String nodeKey = currentNode.getNodeId() ;
        
        // 防止循环引用
        if (nodeKey != null && visitedNodeKeys.contains(nodeKey)) {
            log.warn("检测到循环引用，跳过节点：{} (key: {})", currentNode.getNodeName(), nodeKey);
            return null;
        }
        visitedNodeKeys.add(nodeKey);
        
        Map<String, Object> nodeData = new HashMap<>();
        
        // 基础字段映射
        nodeData.put("id", currentNode.getNodeId());
        nodeData.put("name", currentNode.getNodeName());
        nodeData.put("node_type", currentNode.getNodeType()); // 0:证书职务 1:普通分类
        nodeData.put("cert_type", currentNode.getCertType());
        nodeData.put("parent_id", currentNode.getParentId());
        
        // 申请关系形式相关字段
        if (currentNode.getRelationType() != null) {
            nodeData.put("relation_type", currentNode.getRelationType());
        }
        
        if (currentNode.getSqxszsmc() != null && !currentNode.getSqxszsmc().trim().isEmpty()) {
            nodeData.put("sqxszsmc", currentNode.getSqxszsmc());
        }
        
        if (currentNode.getSortOrder() != null) {
            nodeData.put("sort_order", currentNode.getSortOrder());
        }
        
        // 检查是否为叶子节点（node_id == parent_id）
        boolean isLeafNode = currentNode.getParentId() != null && 
                            currentNode.getParentId().equals(currentNode.getNodeId());
        
        List<Map<String, Object>> children = new ArrayList<>();
        
        // 如果不是叶子节点，查找所有子节点
        if (!isLeafNode) {
            // 查找所有以当前节点为父节点的子节点
            List<DwdzCppCertificatePath> childNodes = allNodes.stream()
                .filter(node -> {
                    // 子节点的parent_id等于当前节点的node_id
                    // 并且子节点不是叶子节点（即子节点的parent_id不等于自己的node_id）
                    return currentNode.getNodeId().equals(node.getParentId()) && 
                           !node.getNodeId().equals(node.getParentId());
                })
                .collect(java.util.stream.Collectors.toList());
            
            log.debug("节点 {} 找到 {} 个子节点", currentNode.getNodeName(), childNodes.size());
            
            // 递归构建每个子节点
            for (DwdzCppCertificatePath childNode : childNodes) {
                // 为每个子节点创建新的visited集合，避免跨分支的访问限制
                Set<String> childVisited = new HashSet<>(visitedNodeKeys);
                Map<String, Object> childData = buildNodeTreeFromFlatWithLevel(childNode, allNodes, childVisited);
                if (childData != null) {
                    children.add(childData);
                }
            }
            
            // 按排序字段和名称排序子节点
            children.sort((a, b) -> {
                // 先按sort_order排序
                Object sortOrderA = a.get("sort_order");
                Object sortOrderB = b.get("sort_order");
                
                if (sortOrderA != null && sortOrderB != null) {
                    int sortCompare = ((Comparable) sortOrderA).compareTo(sortOrderB);
                    if (sortCompare != 0) {
                        return sortCompare;
                    }
                }
                
                // 再按名称排序
                String nameA = (String) a.get("name");
                String nameB = (String) b.get("name");
                return nameA.compareTo(nameB);
            });
        }
        
        nodeData.put("children", children);
        
        log.debug("构建节点完成：{} (子节点数: {}, 是否叶子节点: {})",
                 currentNode.getNodeName(), children.size(), isLeafNode);
        return nodeData;
    }

    /**
     * 从平铺节点数据构建轻量级树形结构（只保留node_id、parent_id和必要的树字段）
     */
    private Map<String, Object> buildLightweightTreeFromFlatNodes(List<DwdzCppCertificatePath> allNodes, String rootNodeName) {
        log.info("开始从{}个平铺节点构建轻量级树形结构", allNodes.size());
        
        // 找到根节点（可能有多个同名的根节点，取第一个）
        DwdzCppCertificatePath rootNode = allNodes.stream()
            .filter(node -> rootNodeName.equals(node.getNodeName()))
            .findFirst()
            .orElse(null);
        
        if (rootNode == null) {
            log.warn("未找到根节点：{}", rootNodeName);
            // 返回空的树结构
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("node_id", "empty");
            emptyResult.put("parent_id", null);
            emptyResult.put("children", new ArrayList<>());
            return emptyResult;
        }
        
        log.info("找到根节点：{} (ID: {})", rootNode.getNodeName(), rootNode.getNodeId());
        
        // 递归构建轻量级树形结构
        Map<String, Object> result = buildLightweightNodeTreeFromFlat(rootNode, allNodes, new HashSet<>());
        log.info("轻量级树形结构构建完成");
        
        return result;
    }
    
    /**
     * 递归构建轻量级节点树（只保留关键字段）
     */
    private Map<String, Object> buildLightweightNodeTreeFromFlat(DwdzCppCertificatePath currentNode, 
                                                                List<DwdzCppCertificatePath> allNodes, 
                                                                Set<String> visitedNodeKeys) {
        
        // 构建当前节点的唯一标识（使用node_id）
        String nodeKey = currentNode.getNodeId();
        
        // 防止循环引用
        if (nodeKey != null && visitedNodeKeys.contains(nodeKey)) {
            log.warn("检测到循环引用，跳过节点：{} (key: {})", currentNode.getNodeName(), nodeKey);
            return null;
        }
        visitedNodeKeys.add(nodeKey);
        
        Map<String, Object> nodeData = new HashMap<>();
        
        // 只保留关键字段：node_id、parent_id
        nodeData.put("node_id", currentNode.getNodeId());
        nodeData.put("parent_id", currentNode.getParentId());
        
        // 保留排序字段，用于前端排序
        if (currentNode.getSortOrder() != null) {
            nodeData.put("sort_order", currentNode.getSortOrder());
        }
        
        // 检查是否为叶子节点（node_id == parent_id）
        boolean isLeafNode = currentNode.getParentId() != null && 
                            currentNode.getParentId().equals(currentNode.getNodeId());
        
        List<Map<String, Object>> children = new ArrayList<>();
        
        // 如果不是叶子节点，查找所有子节点
        if (!isLeafNode) {
            // 查找所有以当前节点为父节点的子节点
            List<DwdzCppCertificatePath> childNodes = allNodes.stream()
                .filter(node -> {
                    // 子节点的parent_id等于当前节点的node_id
                    // 并且子节点不是叶子节点（即子节点的parent_id不等于自己的node_id）
                    return currentNode.getNodeId().equals(node.getParentId()) && 
                           !node.getNodeId().equals(node.getParentId());
                })
                .collect(java.util.stream.Collectors.toList());
            
            log.debug("轻量级树节点 {} 找到 {} 个子节点", currentNode.getNodeName(), childNodes.size());
            
            // 递归构建每个子节点
            for (DwdzCppCertificatePath childNode : childNodes) {
                // 为每个子节点创建新的visited集合，避免跨分支的访问限制
                Set<String> childVisited = new HashSet<>(visitedNodeKeys);
                Map<String, Object> childData = buildLightweightNodeTreeFromFlat(childNode, allNodes, childVisited);
                if (childData != null) {
                    children.add(childData);
                }
            }
            
            // 按排序字段排序子节点
            children.sort((a, b) -> {
                // 先按sort_order排序
                Object sortOrderA = a.get("sort_order");
                Object sortOrderB = b.get("sort_order");
                
                if (sortOrderA != null && sortOrderB != null) {
                    return ((Comparable) sortOrderA).compareTo(sortOrderB);
                }
                
                // 如果没有排序字段，按node_id排序以保持一致性
                String nodeIdA = (String) a.get("node_id");
                String nodeIdB = (String) b.get("node_id");
                return nodeIdA.compareTo(nodeIdB);
            });
        }
        
        nodeData.put("children", children);
        
        log.debug("构建轻量级节点完成：{} (子节点数: {}, 是否叶子节点: {})",
                 currentNode.getNodeName(), children.size(), isLeafNode);
        return nodeData;
    }

    /**
     * 构建节点数据映射表，供前端根据node_id和parent_id查询显示信息
     */
    private List<Map<String, Object>> buildNodeDataList(List<DwdzCppCertificatePath> allNodes) {
        log.info("开始构建节点数据映射表，共{}个节点", allNodes.size());
        
        List<Map<String, Object>> nodeDataList = new ArrayList<>();
        
        for (DwdzCppCertificatePath node : allNodes) {
            Map<String, Object> nodeMap = new HashMap<>();
            
            // 主键字段
            nodeMap.put("node_id", node.getNodeId());
            nodeMap.put("parent_id", node.getParentId());
            
            // 显示字段：name（对应NodeName）和sqxszsmc
            nodeMap.put("name", node.getNodeName());
            
            if (node.getSqxszsmc() != null && !node.getSqxszsmc().trim().isEmpty()) {
                nodeMap.put("sqxszsmc", node.getSqxszsmc());
            } else {
                nodeMap.put("sqxszsmc", ""); // 确保字段存在
            }
            
            // 可能需要的其他字段
            nodeMap.put("node_type", node.getNodeType());
            nodeMap.put("cert_type", node.getCertType());
            
            if (node.getRelationType() != null) {
                nodeMap.put("relation_type", node.getRelationType());
            }
            
            if (node.getSortOrder() != null) {
                nodeMap.put("sort_order", node.getSortOrder());
            }
            
            nodeDataList.add(nodeMap);
        }
        
        log.info("节点数据映射表构建完成，共{}条记录", nodeDataList.size());
        return nodeDataList;
    }

    @Override
    public Map<String, Object> getCertificateTypes() {
        log.info("开始获取证书类型列表");

        try {
            // 使用新版本的DAO获取根节点（parent_id为空的节点）
            List<DwdzCrewCertificatePath> rootNodes = crewCertificatePathDao.getRootNodes();
            log.info("查询到{}个根节点", rootNodes.size());

            if (rootNodes == null || rootNodes.isEmpty()) {
                log.warn("未找到任何根节点");
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("data", new ArrayList<>());
                result.put("message", "暂无证书类型数据");
                return result;
            }

            // 构建证书类型列表
            List<Map<String, Object>> certificateTypes = new ArrayList<>();
            for (DwdzCrewCertificatePath rootNode : rootNodes) {
                Map<String, Object> typeInfo = new HashMap<>();
                typeInfo.put("id", rootNode.getNodeId());
                typeInfo.put("name", rootNode.getNodeName());
                typeInfo.put("certType", rootNode.getCertType());
                typeInfo.put("nodeType", rootNode.getNodeType());
                typeInfo.put("sortOrder", rootNode.getSortOrder());
                certificateTypes.add(typeInfo);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", certificateTypes);
            result.put("total", certificateTypes.size());
            result.put("message", "获取证书类型列表成功");

            log.info("获取证书类型列表成功，共{}个类型", certificateTypes.size());
            return result;

        } catch (Exception e) {
            log.error("获取证书类型列表失败，错误：{}", e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("data", new ArrayList<>());
            result.put("message", "获取证书类型列表失败：" + e.getMessage());
            return result;
        }
    }
}