package com.js.hszpt.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.enmus.CertificateTypeEnum;
import com.js.hszpt.entity.CrewBasicInfo;
import com.js.hszpt.entity.DwdzCrewCurrentCert;
import com.js.hszpt.entity.DwdzCrewFeature;
import com.js.hszpt.entity.ResCrewPersonPhoto;
import com.js.hszpt.mapper.CrewBasicInfoMapper;
import com.js.hszpt.mapper.DwdzCrewCurrentCertDao;
import com.js.hszpt.mapper.DwdzCrewFeatureMapper;
import com.js.hszpt.mapper.ResCrewPersonPhotoMapper;
import com.js.hszpt.service.CrewBasicInfoService;
import com.js.hszpt.service.DwdzCrewFeatureService;
import com.js.hszpt.utils.DataMaskingUtil;
import com.js.hszpt.vo.CrewListVO;
import com.js.hszpt.vo.CrewQueryVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Base64;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.ss.usermodel.*;
import java.io.ByteArrayOutputStream;
import java.util.concurrent.CompletableFuture;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import javax.imageio.ImageIO;

@Service
public class CrewBasicInfoServiceImpl extends ServiceImpl<CrewBasicInfoMapper, CrewBasicInfo>
        implements CrewBasicInfoService {

    private static final Logger logger = LoggerFactory.getLogger(CrewBasicInfoServiceImpl.class);

    @Autowired
    private DwdzCrewFeatureService dwdzCrewFeatureService;

    @Autowired
    private DwdzCrewFeatureMapper dwdzCrewFeatureMapper;

    @Autowired
    private com.js.hszpt.mapper.DwdzCrewCurrentCertDao dwdzCrewCurrentCertDao;

    @Autowired
    private ResCrewPersonPhotoMapper resCrewPersonPhotoMapper;

    @Autowired
    private com.js.hszpt.mapper.CrewBasicInfoMapper crewBasicInfoMapper;

    @Autowired
    private com.js.hszpt.service.DwdzCrewSummaryService dwdzCrewSummaryService;

    @Autowired
    private com.js.hszpt.service.DwdzCrewShipServiceService dwdzCrewShipServiceService;

    @Autowired
    private com.js.hszpt.service.DwdzCrewCareerTrackService dwdzCrewCareerTrackService;

    @Value("${crewBaseInfo.exportCount:3000}")
    private Integer exportCount;

    @Override
    public Result getCrewList(CrewQueryVO queryVO) {
        long startTime = System.currentTimeMillis();
        logger.info("开始查询船员列表，查询条件：{}", queryVO);

        try {
            // 参数验证和默认值设置
            if (queryVO.getPageNumber() < 1) {
                queryVO.setPageNumber(1);
            }
            if (queryVO.getPageSize() < 1) {
                queryVO.setPageSize(10);
            }
            if (queryVO.getPageSize() > 100) {
                queryVO.setPageSize(100);
            }
            // 身份证查询传大写
            if (StrUtil.isNotBlank(queryVO.getIdCard())) {
                queryVO.setIdCard(queryVO.getIdCard().toUpperCase());
            }

            // 计算偏移量
            int offset = (queryVO.getPageNumber() - 1) * queryVO.getPageSize();
            queryVO.setOffset(offset);

            logger.debug("开始关联查询船员列表，page={}, limit={}, offset={}, 证书类型={}, 证书号码={}",
                    queryVO.getPageNumber(), queryVO.getPageSize(), offset,
                    queryVO.getCertificateType(), queryVO.getCertificateNumber());

            // 使用优化的关联查询获取船员列表（包含当前状态信息和总数）
            long queryStartTime = System.currentTimeMillis();
            List<CrewListVO> crewList = crewBasicInfoMapper.selectCrewListWithStatusOptimized(queryVO);
            long queryEndTime = System.currentTimeMillis();
            logger.info("船员基础数据查询完成，耗时：{}ms，查询到{}条记录",
                       queryEndTime - queryStartTime, crewList != null ? crewList.size() : 0);

            // 获取总数（从第一条记录中获取）
            Long total = 0L;
            if (!crewList.isEmpty()) {
                total = crewList.get(0).getTotalCount();
            }

            // ====== 综合批量查询优化 ======
            if (!crewList.isEmpty()) {
                // 提取船员ID列表（只提取一次）
                List<String> crewIds = crewList.stream()
                        .map(CrewListVO::getCrewId)
                        .collect(Collectors.toList());

                // 并行执行多个批量查询
                long batchQueryStartTime = System.currentTimeMillis();
                CompletableFuture<List<DwdzCrewCurrentCert>> certsFuture = CompletableFuture.supplyAsync(() ->
                    dwdzCrewCurrentCertDao.getByCrewIds(crewIds));

                CompletableFuture<List<DwdzCrewFeature>> featuresFuture = CompletableFuture.supplyAsync(() ->
                    dwdzCrewFeatureMapper.selectByCrewIdsAndCategoryCodePrefix(crewIds, "SITE"));

                CompletableFuture<List<String>> competencyCertsFuture = CompletableFuture.supplyAsync(() ->
                    dwdzCrewCurrentCertDao.getCrewIdsWithCompetencyCert(crewIds));

                // 等待所有查询完成
                List<DwdzCrewCurrentCert> allCerts = certsFuture.join();
                List<DwdzCrewFeature> dwdzCrewFeatureList = featuresFuture.join();
                List<String> crewIdsWithCompetencyCert = competencyCertsFuture.join();
                long batchQueryEndTime = System.currentTimeMillis();
                logger.info("并行批量查询完成，耗时：{}ms，证书记录：{}条，特征记录：{}条，适任证书船员：{}人",
                           batchQueryEndTime - batchQueryStartTime,
                           allCerts.size(), dwdzCrewFeatureList.size(), crewIdsWithCompetencyCert.size());
                
                // 构建数据映射
                Map<String, List<DwdzCrewCurrentCert>> certMap = allCerts.stream()
                        .collect(Collectors.groupingBy(DwdzCrewCurrentCert::getCrewId));
                
                Map<String, List<DwdzCrewFeature>> crewFeatureMap = dwdzCrewFeatureList.stream()
                        .collect(Collectors.groupingBy(DwdzCrewFeature::getCrewId));
                
                Set<String> competencyCertSet = new HashSet<>(crewIdsWithCompetencyCert);
                
                // 证书类型翻译缓存
                Map<String, String> certTypeTranslationCache = new HashMap<>();

                // 一次性处理所有船员数据
                long dataProcessStartTime = System.currentTimeMillis();
                for (CrewListVO vo : crewList) {
                    String crewId = vo.getCrewId();
                    
                    // 处理证书信息
                    List<DwdzCrewCurrentCert> certs = certMap.getOrDefault(crewId, Collections.emptyList());
                    String certTypes = certs.stream()
                            .map(DwdzCrewCurrentCert::getCertCategory)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.joining(","));
                    
                    String certNumbers = certs.stream()
                            .map(DwdzCrewCurrentCert::getCertNumber)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.joining(","));
                    
                    // 证书类型翻译（使用缓存）
                    if (StrUtil.isNotBlank(certTypes)) {
                        String translatedTypes = Arrays.stream(certTypes.split(","))
                                .map(type -> certTypeTranslationCache.computeIfAbsent(type, 
                                    k -> CertificateTypeEnum.getDescriptionByCode(k)))
                                .collect(Collectors.joining(","));
                        vo.setCertificateType(translatedTypes);
                    } else {
                        vo.setCertificateType(certTypes);
                    }
                    
                    vo.setCertificateNumber(certNumbers);
                    
                    // 设置适任证书标识
                    vo.setHasCompetencyCert(competencyCertSet.contains(crewId) ? 1 : 0);
                    
                    // 处理特性标签
                    String features = crewFeatureMap.getOrDefault(crewId, Collections.emptyList())
                            .stream()
                            .map(DwdzCrewFeature::getTriggerCategoryName)
                            .collect(Collectors.joining(","));
                    vo.setFeatureTags(features);
                    
                    // 身份证信息脱敏处理
                    String idNumber = vo.getIdNumber();
                    if (StrUtil.isNotBlank(idNumber)) {
                        vo.setIdNumber(DataMaskingUtil.maskIdNumber(idNumber, idNumber.length() - 4, 0));
                    }
                }
                long dataProcessEndTime = System.currentTimeMillis();
                logger.info("船员数据处理完成，耗时：{}ms，处理{}条记录",
                           dataProcessEndTime - dataProcessStartTime, crewList.size());
            }
            // ====== 综合批量查询优化结束 ======

            logger.info("船员列表关联查询完成，总记录数：{}，当前页记录数：{}", total, crewList.size());

            // 构建统一的返回格式
            long resultBuildStartTime = System.currentTimeMillis();
            Map<String, Object> result = new HashMap<>();
            result.put("total", total);
            result.put("pageNumber", queryVO.getPageNumber());
            result.put("pageSize", queryVO.getPageSize());
            result.put("list", crewList);
            long resultBuildEndTime = System.currentTimeMillis();
            logger.info("返回结果构建完成，耗时：{}ms", resultBuildEndTime - resultBuildStartTime);

            long totalEndTime = System.currentTimeMillis();
            logger.info("船员列表查询完成，总耗时：{}ms", totalEndTime - startTime);

            return ResultUtil.data(result);
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            logger.error("船员列表查询异常，总耗时：{}ms", errorTime - startTime, e);
            return ResultUtil.error("查询船员列表失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSeafarerBasicInfo(String seafarerId) {
        logger.info("开始获取船员基本信息，船员ID：{}", seafarerId);

        try {
            // 根据船员ID查询基本信息
            LambdaQueryWrapper<CrewBasicInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CrewBasicInfo::getCrewId, seafarerId);

            // 使用getOne(queryWrapper, false)避免多条记录时抛出异常，如果有多条记录则取第一条
            CrewBasicInfo crewInfo = this.getOne(queryWrapper, false);

            if (crewInfo == null) {
                logger.warn("未找到船员信息，船员ID：{}", seafarerId);
                return null;
            }

            // 构建返回结果
            Map<String, Object> result = BeanUtil.beanToMap(crewInfo);
            result.put("id", crewInfo.getCrewId());

            // 身份证信息脱敏处理
            if (result.get("idNumber") != null) {
                String maskedIdNumber = DataMaskingUtil.maskIdNumber((String) result.get("idNumber"));
                result.put("idNumber", maskedIdNumber);
                logger.debug("身份证信息已脱敏处理");
            }

            logger.info("获取船员基本信息成功，船员ID：{}", seafarerId);
            return result;
        } catch (Exception e) {
            logger.error("获取船员基本信息异常，船员ID：{}", seafarerId, e);
            return null;
        }
    }

    @Override
    public String getSeafarerIdNumberPlain(String seafarerId) {
        logger.info("开始获取船员身份证号明文，船员ID：{}", seafarerId);

        try {
            LambdaQueryWrapper<CrewBasicInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CrewBasicInfo::getCrewId, seafarerId);

            // 使用getOne(queryWrapper, false)避免多条记录时抛出异常，如果有多条记录则取第一条
            CrewBasicInfo crewInfo = this.getOne(queryWrapper, false);
            if (crewInfo == null) {
                logger.warn("未找到船员信息，船员ID：{}", seafarerId);
                return null;
            }
            return crewInfo.getIdNumber();
        } catch (Exception e) {
            logger.error("获取船员身份证号明文异常，船员ID：{}", seafarerId, e);
            return null;
        }
    }

    @Override
    public Result getFeatureTags() {
        logger.info("开始获取特征标签列表");
        try {
            // 预定义的特征标签列表
            List<FeatureTag> featureTags = Arrays.asList(
                    new FeatureTag(1L, "交叉任职", "cross-duty"),
                    new FeatureTag(2L, "岗位异常", "position-abnormal"),
                    new FeatureTag(3L, "长期在船", "long-term-onboard"),
                    new FeatureTag(4L, "证书即将过期", "certificate-expiring"),
                    new FeatureTag(5L, "船舶类型突变", "ship-type-change"));

            logger.info("获取特征标签列表成功，标签数量：{}", featureTags.size());
            return ResultUtil.data(featureTags);
        } catch (Exception e) {
            logger.error("获取特征标签列表异常：", e);
            return ResultUtil.error("获取特征标签列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result getPorts(String query) {
        logger.info("开始查询港口列表，查询关键字：{}", query);
        try {
            // 从船员当前状态表中查询港口信息
            List<String> ports = baseMapper.selectPorts(query);

            logger.info("港口列表查询完成，查询结果数量：{}", ports.size());
            return ResultUtil.data(ports);
        } catch (Exception e) {
            logger.error("查询港口列表异常：", e);
            return ResultUtil.error("查询港口列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result getShips(String query) {
        logger.info("开始查询船名列表，查询关键字：{}", query);
        try {
            // 从船员当前状态表中查询船名信息
            List<String> ships = baseMapper.selectShips(query);

            logger.info("船名列表查询完成，查询结果数量：{}", ships.size());
            return ResultUtil.data(ships);
        } catch (Exception e) {
            logger.error("查询船名列表异常：", e);
            return ResultUtil.error("查询船名列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result getPositions(String query) {
        logger.info("开始查询职务列表，查询关键字：{}", query);
        try {
            // 从船员当前状态表中查询职务信息
            List<String> positions = baseMapper.selectPositions(query);

            logger.info("职务列表查询完成，查询结果数量：{}", positions.size());
            return ResultUtil.data(positions);
        } catch (Exception e) {
            logger.error("查询职务列表异常：", e);
            return ResultUtil.error("查询职务列表失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, List<Map<String, Object>>> getSeafarerFeatureTags(String crewId) {
        logger.info("开始获取船员特征标签数据，船员ID：{}", crewId);

        try {
            // 查询船员的所有特征记录
            LambdaQueryWrapper<DwdzCrewFeature> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DwdzCrewFeature::getCrewId, crewId);

            List<DwdzCrewFeature> featureList = dwdzCrewFeatureService.list(queryWrapper);

            // 初始化分类结果
            Map<String, List<Map<String, Object>>> result = new HashMap<>();
            result.put("现场监管标签", new ArrayList<>());
            result.put("身份标签", new ArrayList<>());
            result.put("状态标签", new ArrayList<>());
            result.put("履历信息", new ArrayList<>());
            result.put("其他标签", new ArrayList<>());

            if (CollUtil.isEmpty(featureList)) {
                logger.info("船员特征标签数据为空，船员ID：{}", crewId);
                return result;
            }

            // 根据trigger_category_code进行分类
            for (DwdzCrewFeature feature : featureList) {
                String categoryCode = feature.getTriggerCategoryCode();
                String categoryName = determineCategoryName(categoryCode);

                // 构建特征标签数据
                Map<String, Object> tagData = new HashMap<>();
                tagData.put("featureId", feature.getFeatureId());
                tagData.put("featureType", feature.getFeatureType());
                tagData.put("featureValue", feature.getFeatureValue());
                tagData.put("triggerCategoryName", feature.getTriggerCategoryName());
                tagData.put("ruleCalculationDesc", feature.getRuleCalculationDesc());
                tagData.put("warningLevel", feature.getWarningLevel());
                tagData.put("calcTime", feature.getCalcTime());

                // 添加到对应分类
                result.get(categoryName).add(tagData);
            }

            logger.info("获取船员特征标签数据成功，船员ID：{}，总标签数：{}", crewId, featureList.size());

            // 添加履历信息标签
            addCareerHistoryTags(result, crewId);

            // 打印分类统计
            for (Map.Entry<String, List<Map<String, Object>>> entry : result.entrySet()) {
                logger.debug("分类：{}，标签数量：{}", entry.getKey(), entry.getValue().size());
            }

            return result;
        } catch (Exception e) {
            logger.error("获取船员特征标签数据异常，船员ID：{}", crewId, e);
            throw new RuntimeException("获取船员特征标签数据失败：" + e.getMessage());
        }
    }

    /**
     * 根据特征类别编码确定分类名称
     */
    private String determineCategoryName(String categoryCode) {
        if (StrUtil.isBlank(categoryCode)) {
            return "其他标签";
        }

        if (categoryCode.startsWith("SITE_")) {
            return "现场监管标签";
        } else if (categoryCode.startsWith("IDENT_")) {
            return "身份标签";
        } else if (categoryCode.startsWith("STATUS_")) {
            return "状态标签";
        } else {
            return "其他标签";
        }
    }

    // 特征标签内部类
    private static class FeatureTag {
        private Long id;
        private String name;
        private String value;

        public FeatureTag(Long id, String name, String value) {
            this.id = id;
            this.name = name;
            this.value = value;
        }

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    @Override
    public String getSeafarerPhoto(String idcardNo) {
        logger.info("开始获取船员照片，传入参数：{}", idcardNo);

        try {
            // 参数验证
            if (StrUtil.isBlank(idcardNo)) {
                logger.warn("传入参数为空，无法查询照片");
                return null;
            }

            // 先根据crew_id查询船员基本信息，获取身份证号
            String actualIdNumber = null;
            LambdaQueryWrapper<CrewBasicInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CrewBasicInfo::getCrewId, idcardNo);

            // 使用getOne(queryWrapper, false)避免多条记录时抛出异常，如果有多条记录则取第一条
            CrewBasicInfo crewInfo = this.getOne(queryWrapper, false);

            if (crewInfo != null) {
                actualIdNumber = crewInfo.getIdNumber();
                logger.info("根据crew_id查询到船员信息，crew_id：{}，身份证号：{}", idcardNo, actualIdNumber);
            } else {
                // 如果根据crew_id没找到，尝试直接使用传入的参数作为身份证号查询
                logger.info("根据crew_id未找到船员信息，尝试直接使用传入参数作为身份证号查询，参数：{}", idcardNo);
                actualIdNumber = idcardNo;
            }

            // 查询船员照片
            ResCrewPersonPhoto photo = resCrewPersonPhotoMapper.selectByIdcardNo(actualIdNumber);

            if (photo == null || photo.getPhotoInfo() == null) {
                logger.warn("未找到船员照片，crew_id：{}，身份证号：{}", idcardNo, actualIdNumber);
                return null;
            }

            byte[] imageBytes;

            // 检查数据格式并进行相应处理
            if (photo.getPhotoInfo().length > 2 &&
                photo.getPhotoInfo()[0] == '0' &&
                (photo.getPhotoInfo()[1] == 'x' || photo.getPhotoInfo()[1] == 'X')) {

                logger.info("检测到0x开头的十六进制格式数据，进行转换处理");

                // 将byte[]转换为字符串（去掉0x前缀）
                String hexString = new String(photo.getPhotoInfo()).substring(2);

                // 将十六进制字符串转换为字节数组
                imageBytes = hexStringToByteArray(hexString);

                logger.info("0x十六进制转换完成，原始长度：{} bytes，转换后长度：{} bytes",
                        photo.getPhotoInfo().length, imageBytes.length);

            } else if (isBinaryImageData(photo.getPhotoInfo())) {

                logger.info("检测到二进制图片数据（如BMP格式），原始数据长度：{} bytes", photo.getPhotoInfo().length);
                logger.info("原始数据前10字节：{}", bytesToHexString(photo.getPhotoInfo(), 10));
                imageBytes = processBytEaData(photo.getPhotoInfo());
                logger.info("处理后数据长度：{} bytes", imageBytes.length);
                logger.info("处理后数据前10字节：{}", bytesToHexString(imageBytes, 10));

            } else if (isHexStringWithoutPrefix(photo.getPhotoInfo())) {

                logger.info("检测到无前缀的十六进制字符串数据，进行转换处理");

                // 直接将byte[]转换为字符串，然后转换为字节数组
                String hexString = new String(photo.getPhotoInfo());

                // 将十六进制字符串转换为字节数组
                imageBytes = hexStringToByteArray(hexString);

                logger.info("无前缀十六进制转换完成，原始长度：{} bytes，转换后长度：{} bytes",
                        photo.getPhotoInfo().length, imageBytes.length);

            } else {
                // 如果都不匹配，尝试作为原始数据处理
                logger.info("未识别的数据格式，尝试直接使用原始字节数组");
                imageBytes = photo.getPhotoInfo();
            }

            // 检查是否为BMP格式，如果是则转换为JPEG
            if (isBMPFormat(imageBytes)) {
                logger.info("检测到BMP格式，转换为JPEG格式");
                imageBytes = convertBMPToJPEG(imageBytes);
                logger.info("BMP转JPEG完成，转换后大小：{} bytes", imageBytes.length);
            }

            // 将处理后的字节数组转换为Base64字符串
            String base64Photo = Base64.getEncoder().encodeToString(imageBytes);

            // 添加详细的调试信息
            logger.info("获取船员照片成功，crew_id：{}，身份证号：{}，最终照片大小：{} bytes",
                    idcardNo, actualIdNumber, imageBytes.length);
            logger.info("Base64字符串长度：{}", base64Photo.length());
            logger.info("Base64字符串前50字符：{}",
                    base64Photo.length() > 50 ? base64Photo.substring(0, 50) + "..." : base64Photo);

            // 验证生成的base64是否有效
            try {
                byte[] decodedBytes = Base64.getDecoder().decode(base64Photo);
                logger.info("Base64解码验证成功，解码后大小：{} bytes", decodedBytes.length);

                // 检查解码后的数据是否与原始数据一致
                if (decodedBytes.length == imageBytes.length) {
                    logger.info("Base64编解码数据大小一致");
                } else {
                    logger.warn("Base64编解码数据大小不一致！原始：{}, 解码后：{}",
                            imageBytes.length, decodedBytes.length);
                }
            } catch (Exception e) {
                logger.error("Base64解码验证失败", e);
            }

            return base64Photo;

        } catch (Exception e) {
            logger.error("获取船员照片异常，身份证号：{}", idcardNo, e);
            return null;
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    private byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                                 + Character.digit(hexString.charAt(i+1), 16));
        }
        return data;
    }

    /**
     * 处理PostgreSQL/人大金仓 bytea 字段的特殊格式
     * @param data 原始字节数组
     * @return 处理后的图片字节数组
     */
    private byte[] processBytEaData(byte[] data) {
        try {
            logger.info("开始处理BytEa数据，原始数据长度：{} bytes", data.length);
            logger.info("原始数据十六进制预览：{}", bytesToHexString(data, 20));

            // 检查数据是否看起来像文本（ASCII可打印字符）
            boolean isLikelyText = isLikelyTextData(data);
            logger.info("数据是否像文本格式：{}", isLikelyText);

            if (isLikelyText) {
                // 将字节数组转换为字符串查看内容
                String dataStr = new String(data, "UTF-8");
                logger.info("BytEa数据内容预览（前100字符）：{}",
                        dataStr.length() > 100 ? dataStr.substring(0, 100) + "..." : dataStr);

                // 情况1：如果是PostgreSQL的转义格式（如 \x424d... 或类似格式）
                if (dataStr.startsWith("\\x")) {
                    logger.info("检测到PostgreSQL转义格式（\\x开头），进行转换");
                    String hexString = dataStr.substring(2); // 去掉 \x 前缀
                    return hexStringToByteArray(hexString);
                }

                // 情况2：如果包含转义字符，尝试解析转义格式
                if (dataStr.contains("\\")) {
                    logger.info("检测到包含转义字符的数据，尝试解析");
                    return parseEscapedBytEa(dataStr);
                }

                // 情况3：检查是否为纯十六进制字符串
                if (isHexStringWithoutPrefix(data)) {
                    logger.info("检测到纯十六进制字符串，进行转换");
                    return hexStringToByteArray(dataStr);
                }
            } else {
                logger.info("数据为二进制格式，直接使用原始字节数组");
                return data;
            }

            // 情况4：直接返回原始数据
            logger.info("使用原始字节数组");
            return data;

        } catch (Exception e) {
            logger.error("处理BytEa数据时发生异常", e);
            return data; // 发生异常时返回原始数据
        }
    }

    /**
     * 检查数据是否像文本数据（包含大量ASCII可打印字符）
     * @param data 字节数组
     * @return 是否像文本数据
     */
    private boolean isLikelyTextData(byte[] data) {
        if (data == null || data.length == 0) {
            return false;
        }

        int printableCount = 0;
        int sampleSize = Math.min(data.length, 100); // 只检查前100个字节

        for (int i = 0; i < sampleSize; i++) {
            byte b = data[i];
            // 检查是否为ASCII可打印字符 (32-126) 或常见控制字符 (9,10,13)
            if ((b >= 32 && b <= 126) || b == 9 || b == 10 || b == 13) {
                printableCount++;
            }
        }

        // 如果80%以上是可打印字符，认为是文本数据
        double printableRatio = (double) printableCount / sampleSize;
        return printableRatio > 0.8;
    }

    /**
     * 解析PostgreSQL转义格式的bytea数据
     * @param escapedStr 转义格式的字符串
     * @return 解析后的字节数组
     */
    private byte[] parseEscapedBytEa(String escapedStr) {
        try {
            // 这里实现PostgreSQL bytea转义格式的解析
            // 例如：\001\002\003 这样的格式
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < escapedStr.length(); i++) {
                char c = escapedStr.charAt(i);
                if (c == '\\' && i + 3 < escapedStr.length()) {
                    // 检查是否为八进制转义 \nnn
                    String octal = escapedStr.substring(i + 1, i + 4);
                    try {
                        int value = Integer.parseInt(octal, 8);
                        result.append((char) value);
                        i += 3; // 跳过已处理的字符
                    } catch (NumberFormatException e) {
                        result.append(c);
                    }
                } else {
                    result.append(c);
                }
            }
            return result.toString().getBytes("ISO-8859-1");
        } catch (Exception e) {
            logger.error("解析转义BytEa数据失败", e);
            return escapedStr.getBytes();
        }
    }

    /**
     * 检查是否为有效的图片文件头
     * @param data 字节数组
     * @return 是否为有效图片头
     */
    private boolean isValidImageHeader(byte[] data) {
        if (data == null || data.length < 4) {
            return false;
        }

        // BMP: 42 4D (BM)
        if (data.length >= 2 &&
            (data[0] & 0xFF) == 0x42 &&
            (data[1] & 0xFF) == 0x4D) {
            return true;
        }

        // JPEG: FF D8 FF
        if (data.length >= 3 &&
            (data[0] & 0xFF) == 0xFF &&
            (data[1] & 0xFF) == 0xD8 &&
            (data[2] & 0xFF) == 0xFF) {
            return true;
        }

        // PNG: 89 50 4E 47
        if (data.length >= 4 &&
            (data[0] & 0xFF) == 0x89 &&
            (data[1] & 0xFF) == 0x50 &&
            (data[2] & 0xFF) == 0x4E &&
            (data[3] & 0xFF) == 0x47) {
            return true;
        }

        return false;
    }

    /**
     * 检测是否为二进制图片数据
     * @param data 字节数组
     * @return 是否为二进制图片数据
     */
    private boolean isBinaryImageData(byte[] data) {
        if (data == null || data.length < 4) {
            return false;
        }

        // 检查常见图片格式的文件头（二进制格式）

        // BMP: 42 4D (BM) - 这是最可能的情况
        if (data.length >= 2 &&
            (data[0] & 0xFF) == 0x42 &&
            (data[1] & 0xFF) == 0x4D) {
            logger.info("检测到BMP格式的二进制数据（BM文件头）");
            return true;
        }

        // JPEG: FF D8 FF
        if (data.length >= 3 &&
            (data[0] & 0xFF) == 0xFF &&
            (data[1] & 0xFF) == 0xD8 &&
            (data[2] & 0xFF) == 0xFF) {
            logger.info("检测到JPEG格式的二进制数据");
            return true;
        }

        // PNG: 89 50 4E 47
        if (data.length >= 4 &&
            (data[0] & 0xFF) == 0x89 &&
            (data[1] & 0xFF) == 0x50 &&
            (data[2] & 0xFF) == 0x4E &&
            (data[3] & 0xFF) == 0x47) {
            logger.info("检测到PNG格式的二进制数据");
            return true;
        }

        // GIF: 47 49 46 38 (GIF8)
        if (data.length >= 4 &&
            (data[0] & 0xFF) == 0x47 &&
            (data[1] & 0xFF) == 0x49 &&
            (data[2] & 0xFF) == 0x46 &&
            (data[3] & 0xFF) == 0x38) {
            logger.info("检测到GIF格式的二进制数据");
            return true;
        }

        return false;
    }

    /**
     * 检测是否为无前缀的十六进制字符串
     * @param data 字节数组
     * @return 是否为十六进制字符串
     */
    private boolean isHexStringWithoutPrefix(byte[] data) {
        if (data == null || data.length < 6) {
            return false;
        }

        try {
            // 将byte[]转换为字符串
            String str = new String(data);

            // 检查字符串长度是否为偶数（十六进制字符串必须是偶数长度）
            if (str.length() % 2 != 0) {
                return false;
            }

            // 检查是否所有字符都是有效的十六进制字符
            for (char c : str.toCharArray()) {
                if (!((c >= '0' && c <= '9') ||
                      (c >= 'A' && c <= 'F') ||
                      (c >= 'a' && c <= 'f'))) {
                    return false;
                }
            }

            // 如果字符串较长且全部是十六进制字符，认为是十六进制字符串
            if (str.length() > 100) {
                logger.debug("检测到长十六进制字符串");
                return true;
            }

            return false;

        } catch (Exception e) {
            logger.debug("检测十六进制字符串时发生异常", e);
            return false;
        }
    }

    /**
     * 检测是否为有效的图片格式
     * @param data 字节数组
     * @return 是否为有效图片格式
     */
    private boolean isValidImageFormat(byte[] data) {
        if (data == null || data.length < 4) {
            return false;
        }

        // 检查常见图片格式的文件头
        // JPEG: FF D8 FF
        if (data.length >= 3 &&
            (data[0] & 0xFF) == 0xFF &&
            (data[1] & 0xFF) == 0xD8 &&
            (data[2] & 0xFF) == 0xFF) {
            logger.debug("检测到JPEG格式");
            return true;
        }

        // PNG: 89 50 4E 47
        if (data.length >= 4 &&
            (data[0] & 0xFF) == 0x89 &&
            (data[1] & 0xFF) == 0x50 &&
            (data[2] & 0xFF) == 0x4E &&
            (data[3] & 0xFF) == 0x47) {
            logger.debug("检测到PNG格式");
            return true;
        }

        // BMP: 42 4D (BM)
        if (data.length >= 2 &&
            (data[0] & 0xFF) == 0x42 &&
            (data[1] & 0xFF) == 0x4D) {
            logger.debug("检测到BMP格式");
            return true;
        }

        // GIF: 47 49 46 38 (GIF8)
        if (data.length >= 4 &&
            (data[0] & 0xFF) == 0x47 &&
            (data[1] & 0xFF) == 0x49 &&
            (data[2] & 0xFF) == 0x46 &&
            (data[3] & 0xFF) == 0x38) {
            logger.debug("检测到GIF格式");
            return true;
        }

        return false;
    }

    /**
     * 检查是否为BMP格式
     * @param imageBytes 图片字节数组
     * @return 是否为BMP格式
     */
    private boolean isBMPFormat(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length < 2) {
            return false;
        }

        // BMP文件头：42 4D (BM)
        return (imageBytes[0] & 0xFF) == 0x42 && (imageBytes[1] & 0xFF) == 0x4D;
    }

    /**
     * 将BMP格式转换为JPEG格式
     * @param bmpBytes BMP格式的字节数组
     * @return JPEG格式的字节数组
     */
    private byte[] convertBMPToJPEG(byte[] bmpBytes) {
        try {
            // 将字节数组转换为BufferedImage
            ByteArrayInputStream bais = new ByteArrayInputStream(bmpBytes);
            BufferedImage image = ImageIO.read(bais);

            if (image == null) {
                logger.warn("无法读取BMP图像，返回原始数据");
                return bmpBytes;
            }

            logger.info("BMP图像信息：宽度={}, 高度={}, 类型={}",
                    image.getWidth(), image.getHeight(), image.getType());

            // 如果图像有透明通道，需要转换为RGB格式
            BufferedImage rgbImage = image;
            if (image.getType() != BufferedImage.TYPE_INT_RGB) {
                rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
                rgbImage.getGraphics().drawImage(image, 0, 0, null);
                rgbImage.getGraphics().dispose();
            }

            // 转换为JPEG格式
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            boolean success = ImageIO.write(rgbImage, "JPEG", baos);

            if (!success) {
                logger.warn("JPEG转换失败，返回原始数据");
                return bmpBytes;
            }

            byte[] jpegBytes = baos.toByteArray();
            logger.info("BMP转JPEG成功，原始大小：{} bytes，转换后大小：{} bytes",
                    bmpBytes.length, jpegBytes.length);

            return jpegBytes;

        } catch (Exception e) {
            logger.error("BMP转JPEG异常，返回原始数据", e);
            return bmpBytes;
        }
    }

    /**
     * 将字节数组的前N个字节转换为十六进制字符串（用于调试）
     * @param bytes 字节数组
     * @param maxBytes 最大字节数
     * @return 十六进制字符串
     */
    private String bytesToHexString(byte[] bytes, int maxBytes) {
        if (bytes == null) {
            return "null";
        }

        int length = Math.min(bytes.length, maxBytes);
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < length; i++) {
            String hex = Integer.toHexString(0xFF & bytes[i]);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
            if (i < length - 1) {
                hexString.append(' ');
            }
        }
        if (bytes.length > maxBytes) {
            hexString.append("...");
        }
        return hexString.toString().toUpperCase();
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串（不包含0x前缀）
     */
    private String byteArrayToHexString(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xFF & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase();
    }

    @Override
    public boolean insertSeafarerPhoto(String idcardNo, String filePath) {
        logger.info("开始插入船员照片，身份证号：{}，文件路径：{}", idcardNo, filePath);

        try {
            // 参数验证
            if (StrUtil.isBlank(idcardNo) || StrUtil.isBlank(filePath)) {
                logger.warn("参数验证失败 - 身份证号：{}，文件路径：{}", idcardNo, filePath);
                return false;
            }

            // 验证文件是否存在
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                logger.warn("文件不存在，文件路径：{}", filePath);
                return false;
            }

            // 验证文件类型（可选）
            String fileName = path.getFileName().toString().toLowerCase();
            if (!fileName.endsWith(".jpg") && !fileName.endsWith(".jpeg") &&
                    !fileName.endsWith(".png") && !fileName.endsWith(".bmp")) {
                logger.warn("不支持的图片格式，文件：{}", fileName);
                return false;
            }

            // 读取文件为byte数组
            byte[] photoBytes = Files.readAllBytes(path);

            if (photoBytes.length == 0) {
                logger.warn("文件为空，文件路径：{}", filePath);
                return false;
            }

            // 检查文件大小（限制为5MB）
            if (photoBytes.length > 5 * 1024 * 1024) {
                logger.warn("文件过大，文件大小：{} bytes，文件路径：{}", photoBytes.length, filePath);
                return false;
            }

            // 将byte数组转换为16进制字符串格式（以0x开头）
            String hexString = "0x" + byteArrayToHexString(photoBytes);
            byte[] hexBytes = hexString.getBytes();

            logger.info("图片转换为16进制格式，原始大小：{} bytes，转换后大小：{} bytes", 
                    photoBytes.length, hexBytes.length);

            // 检查是否已存在该身份证号的照片
            ResCrewPersonPhoto existingPhoto = resCrewPersonPhotoMapper.selectByIdcardNo(idcardNo);

            if (existingPhoto != null) {
                // 如果已存在，更新照片
                existingPhoto.setPhotoInfo(hexBytes);
                int updateResult = resCrewPersonPhotoMapper.updateById(existingPhoto);

                if (updateResult > 0) {
                    logger.info("更新船员照片成功，身份证号：{}，16进制格式大小：{} bytes", 
                            idcardNo, hexBytes.length);
                    return true;
                } else {
                    logger.warn("更新船员照片失败，身份证号：{}", idcardNo);
                    return false;
                }
            } else {
                // 如果不存在，插入新照片
                ResCrewPersonPhoto newPhoto = new ResCrewPersonPhoto();
                newPhoto.setSerialNo(generateSerialNo()); // 生成流水号
                newPhoto.setIdcardNo(idcardNo);
                newPhoto.setPhotoInfo(hexBytes);

                int insertResult = resCrewPersonPhotoMapper.insert(newPhoto);

                if (insertResult > 0) {
                    logger.info("插入船员照片成功，身份证号：{}，16进制格式大小：{} bytes", 
                            idcardNo, hexBytes.length);
                    return true;
                } else {
                    logger.warn("插入船员照片失败，身份证号：{}", idcardNo);
                    return false;
                }
            }

        } catch (IOException e) {
            logger.error("读取文件异常，文件路径：{}", filePath, e);
            return false;
        } catch (Exception e) {
            logger.error("插入船员照片异常，身份证号：{}，文件路径：{}", idcardNo, filePath, e);
            return false;
        }
    }

    /**
     * 生成流水号
     */
    private String generateSerialNo() {
        return "PHOTO_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    @Override
    public byte[] exportCrewListXlsx(CrewQueryVO queryVO) {
        long startTime = System.currentTimeMillis();
        logger.info("开始导出船员列表，查询条件：{}", queryVO);

        try {
            // 设置导出模式：pageSize设为0表示导出所有数据
            queryVO.setPageSize(0);
            queryVO.setOffset(0);

            // 使用统一的查询方法导出数据
            long queryStartTime = System.currentTimeMillis();
            List<CrewListVO> list = crewBasicInfoMapper.selectCrewListWithStatusOptimized(queryVO);
            long queryEndTime = System.currentTimeMillis();
            logger.info("船员基础数据查询完成，耗时：{}ms，查询到{}条记录",
                       queryEndTime - queryStartTime, list != null ? list.size() : 0);

            // ====== 新增：批量查询适任证书并设置hasCompetencyCert属性 ======
            if (CollUtil.isNotEmpty(list)) {
                List<String> crewIds = list.stream()
                        .map(CrewListVO::getCrewId)
                        .collect(Collectors.toList());

                // 分批查询适任证书，避免IN子句过长（人大金仓数据库限制）
                long certStartTime = System.currentTimeMillis();
                Set<String> competencyCertSet = batchQueryCompetencyCert(crewIds);
                long certEndTime = System.currentTimeMillis();
                logger.info("适任证书查询完成，耗时：{}ms，船员总数：{}，持证船员数：{}",
                           certEndTime - certStartTime, crewIds.size(), competencyCertSet.size());

                // 设置每个船员的hasCompetencyCert属性
                for (CrewListVO vo : list) {
                    vo.setHasCompetencyCert(competencyCertSet.contains(vo.getCrewId()) ? 1 : 0);
                }

                // ====== 新增：批量查询船员标签信息 ======
                long tagStartTime = System.currentTimeMillis();
                Map<String, Map<String, String>> crewTagsMap = batchQueryCrewTags(crewIds);
                long tagEndTime = System.currentTimeMillis();
                logger.info("船员标签查询完成，耗时：{}ms，船员总数：{}",
                           tagEndTime - tagStartTime, crewIds.size());

                // 设置每个船员的标签信息
                long tagProcessStartTime = System.currentTimeMillis();
                for (CrewListVO vo : list) {
                    Map<String, String> tags = crewTagsMap.getOrDefault(vo.getCrewId(), new HashMap<>());
                    vo.setSiteTagsStr(tags.getOrDefault("现场监管标签", ""));
                    vo.setIdentityTagsStr(tags.getOrDefault("身份标签", ""));
                    vo.setStatusTagsStr(tags.getOrDefault("状态标签", ""));
                }
                long tagProcessEndTime = System.currentTimeMillis();
                logger.info("船员标签数据设置完成，耗时：{}ms", tagProcessEndTime - tagProcessStartTime);
                // ====== 标签查询结束 ======
            }
            // ====== 适任证书查询结束 ======

            // ====== 开始生成Excel文件 ======
            long excelStartTime = System.currentTimeMillis();
            SXSSFWorkbook wb = new SXSSFWorkbook(200);
            Sheet sheet = wb.createSheet("船员列表");

            String[] headers = new String[]{
                    "姓名","身份证号","手机","在船状态","船名","职务","外派公司","管理公司","船东公司","经营公司",
                    "现场监管标签","身份标签","状态标签"};
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
            }
            logger.info("Excel表头创建完成，共{}列", headers.length);

            CreationHelper ch = wb.getCreationHelper();
            CellStyle dateStyle = wb.createCellStyle();
            dateStyle.setDataFormat(ch.createDataFormat().getFormat("yyyy-mm-dd"));

            // 需要在写入数据前开启列宽跟踪，否则 SXSSF 不允许 autoSize
            if (sheet instanceof SXSSFSheet) {
                ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();
            }

            // 开始写入数据行
            long dataWriteStartTime = System.currentTimeMillis();
            int rowIdx = 1;
            for (CrewListVO r : list) {
                Row row = sheet.createRow(rowIdx++);
                int c = 0;
                row.createCell(c++).setCellValue(s(r.getFullName()));           // 姓名
                // 身份证号进行脱敏处理
                String maskedIdNumber = DataMaskingUtil.maskIdNumber(r.getIdNumber());
                row.createCell(c++).setCellValue(s(maskedIdNumber));            // 身份证号（脱敏）
                row.createCell(c++).setCellValue(s(r.getPhone()));              // 手机
                row.createCell(c++).setCellValue(s(r.getOnboardStatus()));      // 在船状态
                row.createCell(c++).setCellValue(s(r.getShipName()));           // 船名
                row.createCell(c++).setCellValue(s(r.getPosition()));           // 职务
                row.createCell(c++).setCellValue(s(r.getDispatchCompanyName())); // 外派公司
                row.createCell(c++).setCellValue(s(r.getShipManagementCompany())); // 管理公司
                row.createCell(c++).setCellValue(s(r.getShipOwnerCompany()));   // 船东公司
                row.createCell(c++).setCellValue(s(r.getShipOperatorCompany())); // 经营公司
                row.createCell(c++).setCellValue(s(r.getSiteTagsStr()));        // 现场监管标签
                row.createCell(c++).setCellValue(s(r.getIdentityTagsStr()));    // 身份标签
                row.createCell(c++).setCellValue(s(r.getStatusTagsStr()));      // 状态标签
            }
            long dataWriteEndTime = System.currentTimeMillis();
            logger.info("Excel数据写入完成，耗时：{}ms，写入{}行数据",
                       dataWriteEndTime - dataWriteStartTime, list != null ? list.size() : 0);

            // 调整列宽
            long columnAdjustStartTime = System.currentTimeMillis();
            for (int i = 0; i < headers.length; i++) {
                if (sheet instanceof SXSSFSheet) {
                    ((SXSSFSheet) sheet).autoSizeColumn(i);
                } else {
                    sheet.autoSizeColumn(i);
                }
                int width = sheet.getColumnWidth(i);
                sheet.setColumnWidth(i, Math.min(width + 512, 10000));
            }
            long columnAdjustEndTime = System.currentTimeMillis();
            logger.info("Excel列宽调整完成，耗时：{}ms", columnAdjustEndTime - columnAdjustStartTime);

            // 生成文件字节数组
            long fileGenerateStartTime = System.currentTimeMillis();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            wb.dispose();
            wb.close();
            byte[] result = bos.toByteArray();
            long fileGenerateEndTime = System.currentTimeMillis();
            logger.info("Excel文件生成完成，耗时：{}ms，文件大小：{}KB",
                       fileGenerateEndTime - fileGenerateStartTime, result.length / 1024);

            long totalEndTime = System.currentTimeMillis();
            logger.info("船员列表导出完成，总耗时：{}ms", totalEndTime - startTime);

            return result;
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            logger.error("导出xlsx失败，总耗时：{}ms", errorTime - startTime, e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    private String s(Object v) { return v == null ? "" : String.valueOf(v); }
    private void setDateCell(Row row, int idx, java.util.Date d, CellStyle style) {
        if (d == null) {
            row.createCell(idx).setCellValue("");
        } else {
            Cell cell = row.createCell(idx);
            cell.setCellValue(d);
            cell.setCellStyle(style);
        }
    }

    /**
     * 分批查询适任证书，避免IN子句过长（人大金仓数据库限制）
     * @param crewIds 船员ID列表
     * @return 持有适任证书的船员ID集合
     */
    private Set<String> batchQueryCompetencyCert(List<String> crewIds) {
        Set<String> competencyCertSet = new HashSet<>();
        int batchSize = 1000; // 每次查询不超过1000个ID

        for (int i = 0; i < crewIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, crewIds.size());
            List<String> batchCrewIds = crewIds.subList(i, endIndex);

            List<String> batchResult = dwdzCrewCurrentCertDao.getCrewIdsWithCompetencyCert(batchCrewIds);
            competencyCertSet.addAll(batchResult);

            logger.debug("批量查询适任证书完成，船员数：{}，持证船员数：{}",
                        batchCrewIds.size(), batchResult.size());
        }

        logger.info("适任证书查询完成，总船员数：{}，持证船员数：{}",
                   crewIds.size(), competencyCertSet.size());
        return competencyCertSet;
    }

    /**
     * 分批查询船员标签信息，避免IN子句过长（人大金仓数据库限制）
     * @param crewIds 船员ID列表
     * @return 船员标签映射 Map<船员ID, Map<标签类型, 标签字符串>>
     */
    private Map<String, Map<String, String>> batchQueryCrewTags(List<String> crewIds) {
        Map<String, Map<String, String>> result = new HashMap<>();
        int batchSize = 1000; // 每次查询不超过1000个ID

        for (int i = 0; i < crewIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, crewIds.size());
            List<String> batchCrewIds = crewIds.subList(i, endIndex);

            // 查询这批船员的所有特征记录
            LambdaQueryWrapper<DwdzCrewFeature> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DwdzCrewFeature::getCrewId, batchCrewIds);
            List<DwdzCrewFeature> featureList = dwdzCrewFeatureService.list(queryWrapper);

            // 按船员ID分组处理标签
            Map<String, List<DwdzCrewFeature>> crewFeatureMap = featureList.stream()
                    .collect(Collectors.groupingBy(DwdzCrewFeature::getCrewId));

            for (String crewId : batchCrewIds) {
                List<DwdzCrewFeature> crewFeatures = crewFeatureMap.getOrDefault(crewId, new ArrayList<>());
                Map<String, String> tagMap = processCrewTags(crewFeatures);
                result.put(crewId, tagMap);
            }

            logger.debug("批量查询船员标签完成，船员数：{}，标签记录数：{}",
                        batchCrewIds.size(), featureList.size());
        }

        logger.info("船员标签查询完成，总船员数：{}", crewIds.size());
        return result;
    }

    /**
     * 处理单个船员的标签信息，按类型分组并拼接
     * @param crewFeatures 船员特征列表
     * @return 标签映射 Map<标签类型, 标签字符串>
     */
    private Map<String, String> processCrewTags(List<DwdzCrewFeature> crewFeatures) {
        Map<String, String> result = new HashMap<>();
        result.put("现场监管标签", "");
        result.put("身份标签", "");
        result.put("状态标签", "");

        if (CollUtil.isEmpty(crewFeatures)) {
            return result;
        }

        // 按标签类型分组
        Map<String, List<String>> tagGroups = new HashMap<>();
        tagGroups.put("现场监管标签", new ArrayList<>());
        tagGroups.put("身份标签", new ArrayList<>());
        tagGroups.put("状态标签", new ArrayList<>());

        for (DwdzCrewFeature feature : crewFeatures) {
            String categoryCode = feature.getTriggerCategoryCode();
            String categoryName = determineCategoryName(categoryCode);

            // 只处理三种主要标签类型
            if (tagGroups.containsKey(categoryName)) {
                String tagName = feature.getTriggerCategoryName();
                if (StrUtil.isNotBlank(tagName)) {
                    tagGroups.get(categoryName).add(tagName);
                }
            }
        }

        // 拼接标签字符串
        for (Map.Entry<String, List<String>> entry : tagGroups.entrySet()) {
            String categoryName = entry.getKey();
            List<String> tags = entry.getValue();

            // 去重并拼接
            String tagStr = tags.stream()
                    .distinct()
                    .collect(Collectors.joining(","));
            result.put(categoryName, tagStr);
        }

        return result;
    }

    /**
     * 添加履历信息标签
     * @param result 结果Map
     * @param crewId 船员ID
     */
    private void addCareerHistoryTags(Map<String, List<Map<String, Object>>> result, String crewId) {
        try {
            List<Map<String, Object>> careerTags = new ArrayList<>();

            // 1. 注册海员时长标签
            String totalExperience = getSeafarerTotalExperience(crewId);
            if (totalExperience != null) {
                Map<String, Object> experienceTag = new HashMap<>();
                experienceTag.put("featureType", "注册海员时长");
                experienceTag.put("featureValue", totalExperience);
                experienceTag.put("triggerCategoryName", "履历信息");
                careerTags.add(experienceTag);
            }

            // 2. 履职船舶类型TOP1标签
            String topShipType = getTopShipType(crewId);
            if (topShipType != null) {
                Map<String, Object> shipTypeTag = new HashMap<>();
                shipTypeTag.put("featureType", "履职船舶类型TOP1");
                shipTypeTag.put("featureValue", topShipType);
                shipTypeTag.put("triggerCategoryName", "履历信息");
                careerTags.add(shipTypeTag);
            }

            // 3. 职务成长轨迹标签
            String careerTrack = getCareerTrack(crewId);
            if (careerTrack != null) {
                Map<String, Object> trackTag = new HashMap<>();
                trackTag.put("featureType", "职务成长轨迹");
                trackTag.put("featureValue", careerTrack);
                trackTag.put("triggerCategoryName", "履历信息");
                careerTags.add(trackTag);
            }

            result.put("履历信息", careerTags);
            logger.info("成功添加履历信息标签，船员ID：{}，标签数量：{}", crewId, careerTags.size());

        } catch (Exception e) {
            logger.error("添加履历信息标签失败，船员ID：{}", crewId, e);
            // 不抛出异常，避免影响其他标签的获取
        }
    }

    /**
     * 获取注册海员时长
     */
    private String getSeafarerTotalExperience(String crewId) {
        try {
            Object careerData = dwdzCrewSummaryService.getCareerAnalysisData(crewId);
            if (careerData instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) careerData;
                Map<String, Object> careerInfo = (Map<String, Object>) dataMap.get("careerData");
                if (careerInfo != null) {
                    return (String) careerInfo.get("totalExperience");
                }
            }
        } catch (Exception e) {
            logger.warn("获取注册海员时长失败，船员ID：{}", crewId, e);
        }
        return null;
    }

    /**
     * 获取履职船舶类型TOP1
     */
    private String getTopShipType(String crewId) {
        try {
            List<com.js.hszpt.entity.DwdzCrewShipService> shipServices =
                dwdzCrewShipServiceService.getByCrewId(crewId, "1");

            if (shipServices != null && !shipServices.isEmpty()) {
                // 找到totalMonths最大的船舶类型
                com.js.hszpt.entity.DwdzCrewShipService topService = shipServices.stream()
                    .filter(service -> service.getTotalMonths() != null)
                    .max((s1, s2) -> s1.getTotalMonths().compareTo(s2.getTotalMonths()))
                    .orElse(null);

                if (topService != null && topService.getShipType() != null) {
                    return topService.getShipType();
                }
            }
        } catch (Exception e) {
            logger.warn("获取履职船舶类型TOP1失败，船员ID：{}", crewId, e);
        }
        return null;
    }

    /**
     * 获取职务成长轨迹
     */
    private String getCareerTrack(String crewId) {
        try {
            List<com.js.hszpt.entity.DwdzCrewCareerTrack> careerTracks =
                dwdzCrewCareerTrackService.getCareerTrackByCrewId(crewId);

            if (careerTracks != null && !careerTracks.isEmpty()) {
                // 按trackOrder排序，拼接职务名称
                String track = careerTracks.stream()
                    .filter(t -> t.getPositionName() != null)
                    .sorted((t1, t2) -> {
                        Integer order1 = t1.getTrackOrder() != null ? t1.getTrackOrder() : 0;
                        Integer order2 = t2.getTrackOrder() != null ? t2.getTrackOrder() : 0;
                        return order1.compareTo(order2);
                    })
                    .map(com.js.hszpt.entity.DwdzCrewCareerTrack::getPositionName)
                    .collect(Collectors.joining(" -> "));

                return track.isEmpty() ? null : track;
            }
        } catch (Exception e) {
            logger.warn("获取职务成长轨迹失败，船员ID：{}", crewId, e);
        }
        return null;
    }
}