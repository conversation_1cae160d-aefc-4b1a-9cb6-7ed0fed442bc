package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewTrainSubject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员培训项目科目表DAO接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCrewTrainSubjectDao extends BaseMapper<DwdzCrewTrainSubject> {

    /**
     * 根据科目ID列表查询培训项目科目信息
     * @param subjectIds 科目ID列表
     * @return 培训项目科目信息列表
     */
    List<DwdzCrewTrainSubject> selectBySubjectIds(@Param("subjectIds") List<String> subjectIds);
} 