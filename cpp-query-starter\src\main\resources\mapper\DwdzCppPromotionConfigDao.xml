<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCppPromotionConfigDao">

    <!-- 晋升配置表结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCppPromotionConfig">
        <id column="config_id" property="configId" />
        <result column="path_selector_options" property="pathSelectorOptions" />
        <result column="career_paths" property="careerPaths" />
        <result column="management_relations" property="managementRelations" />
        <result column="rec_create_date" property="recCreateDate" />
        <result column="rec_modify_date" property="recModifyDate" />
    </resultMap>

    <!-- 获取最新的晋升配置 -->
    <select id="getLatestConfig" resultMap="BaseResultMap">
        SELECT 
            config_id,
            path_selector_options,
            career_paths,
            management_relations,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_promo_config 
        ORDER BY rec_modify_date DESC, rec_create_date DESC
        LIMIT 1
    </select>

</mapper> 