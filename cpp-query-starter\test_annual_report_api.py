#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
船员年度报告API测试脚本（仅使用GET和POST方法）
"""

import requests
import json
import time
import sys
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8285"  # 根据实际端口调整
API_PREFIX = "/api/annual-report"

# 测试数据
TEST_DATA = {
    "pdf_request": {
        "year": 2024,
        "seafarerId": "123456"
    },
    "pdf_request_without_id": {
        "year": 2023
    }
}

def make_request(method, url, params=None, data=None, headers=None):
    """发送HTTP请求"""
    try:
        if headers is None:
            headers = {'Content-Type': 'application/json'}
        
        response = requests.request(
            method=method,
            url=url,
            params=params,
            json=data,
            headers=headers,
            timeout=30
        )
        
        return {
            'status_code': response.status_code,
            'response': response.json() if response.content else None,
            'success': response.status_code == 200
        }
    except requests.exceptions.RequestException as e:
        return {
            'status_code': 0,
            'response': {'error': str(e)},
            'success': False
        }
    except json.JSONDecodeError as e:
        return {
            'status_code': response.status_code if 'response' in locals() else 0,
            'response': {'error': f'JSON解析错误: {str(e)}, 原始响应: {response.text if "response" in locals() else "无响应"}'},
            'success': False
        }

def print_result(test_name, result):
    """打印测试结果"""
    status = "✅ 成功" if result['success'] else "❌ 失败"
    print(f"\n{test_name}: {status}")
    print(f"状态码: {result['status_code']}")
    if result['response']:
        print(f"响应: {json.dumps(result['response'], ensure_ascii=False, indent=2)}")
    print("-" * 50)

def test_get_year_options():
    """测试获取年份选项列表"""
    url = f"{BASE_URL}{API_PREFIX}/year-options"
    result = make_request("GET", url)
    print_result("获取年份选项列表", result)
    
    # 验证响应数据结构
    if result['success'] and result['response']:
        data = result['response'].get('data', [])
        if isinstance(data, list) and len(data) > 0:
            first_item = data[0]
            if 'value' in first_item and 'label' in first_item:
                print(f"✅ 数据结构验证通过，共有{len(data)}个年份选项")
                return data
            else:
                print("❌ 数据结构验证失败：缺少必要字段 value 或 label")
        else:
            print("⚠️  返回的年份选项列表为空")
    
    return []

def test_get_annual_report_data_with_id():
    """测试获取年度报告数据（带船员ID）"""
    url = f"{BASE_URL}{API_PREFIX}/data"
    params = {
        "year": 2024,
        "seafarerId": "123456"
    }
    result = make_request("GET", url, params=params)
    print_result("获取年度报告数据（带船员ID）", result)
    
    # 验证响应数据结构
    if result['success'] and result['response']:
        data = result['response'].get('data', {})
        required_keys = ['basicInfo', 'statistics', 'shipStatus', 'certificates']
        missing_keys = [key for key in required_keys if key not in data]
        
        if not missing_keys:
            print("✅ 主要数据结构验证通过")
            
            # 验证基本信息
            basic_info = data.get('basicInfo', {})
            if all(key in basic_info for key in ['name', 'idNumber', 'avatar']):
                print("✅ 基本信息结构验证通过")
            else:
                print("❌ 基本信息结构验证失败")
            
            # 验证统计数据
            statistics = data.get('statistics', {})
            stats_keys = ['violationScoreTotal', 'scoreReduction', 'clearanceDate', 
                         'serviceDaysTotal', 'unlimitedAreaDays', 'coastalAreaDays']
            if all(key in statistics for key in stats_keys):
                print("✅ 统计数据结构验证通过")
            else:
                print("❌ 统计数据结构验证失败")
            
            # 验证证书信息
            certificates = data.get('certificates', {})
            if all(key in certificates for key in ['competency', 'training', 'health']):
                print("✅ 证书信息结构验证通过")
            else:
                print("❌ 证书信息结构验证失败")
                
        else:
            print(f"❌ 数据结构验证失败：缺少字段 {missing_keys}")
    
    return result

def test_get_annual_report_data_without_id():
    """测试获取年度报告数据（不带船员ID）"""
    url = f"{BASE_URL}{API_PREFIX}/data"
    params = {"year": 2023}
    result = make_request("GET", url, params=params)
    print_result("获取年度报告数据（不带船员ID）", result)
    return result

def test_get_annual_report_data_invalid_year():
    """测试获取年度报告数据（无效年份）"""
    url = f"{BASE_URL}{API_PREFIX}/data"
    params = {"year": 1999}  # 使用一个不太可能有数据的年份
    result = make_request("GET", url, params=params)
    print_result("获取年度报告数据（无效年份）", result)
    return result

def test_generate_pdf_with_id():
    """测试生成PDF报告（带船员ID）"""
    url = f"{BASE_URL}{API_PREFIX}/generate-pdf"
    result = make_request("POST", url, data=TEST_DATA["pdf_request"])
    print_result("生成PDF报告（带船员ID）", result)
    
    # 验证PDF响应数据结构
    if result['success'] and result['response']:
        data = result['response'].get('data', {})
        required_keys = ['fileUrl', 'fileName', 'fileSize', 'generateTime']
        missing_keys = [key for key in required_keys if key not in data]
        
        if not missing_keys:
            print("✅ PDF响应数据结构验证通过")
            print(f"文件URL: {data.get('fileUrl')}")
            print(f"文件名: {data.get('fileName')}")
            print(f"文件大小: {data.get('fileSize')}")
            print(f"生成时间: {data.get('generateTime')}")
        else:
            print(f"❌ PDF响应数据结构验证失败：缺少字段 {missing_keys}")
    
    return result

def test_generate_pdf_without_id():
    """测试生成PDF报告（不带船员ID）"""
    url = f"{BASE_URL}{API_PREFIX}/generate-pdf"
    result = make_request("POST", url, data=TEST_DATA["pdf_request_without_id"])
    print_result("生成PDF报告（不带船员ID）", result)
    return result

def test_generate_pdf_invalid_data():
    """测试生成PDF报告（无效数据）"""
    url = f"{BASE_URL}{API_PREFIX}/generate-pdf"
    invalid_data = {"year": None}  # 年份为空
    result = make_request("POST", url, data=invalid_data)
    print_result("生成PDF报告（无效数据）", result)
    return result

def test_api_endpoints_availability():
    """测试API端点可用性"""
    endpoints = [
        {"method": "GET", "path": "/year-options", "name": "年份选项端点"},
        {"method": "GET", "path": "/data", "name": "报告数据端点"},
        {"method": "POST", "path": "/generate-pdf", "name": "PDF生成端点"}
    ]
    
    print(f"\n检查API端点可用性...")
    all_available = True
    
    for endpoint in endpoints:
        url = f"{BASE_URL}{API_PREFIX}{endpoint['path']}"
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(url, timeout=5)
            else:
                response = requests.post(url, json={}, timeout=5)
            
            # 不管返回什么状态码，只要能连通就认为端点可用
            if response.status_code != 404:
                print(f"✅ {endpoint['name']} 可用 (状态码: {response.status_code})")
            else:
                print(f"❌ {endpoint['name']} 不可用 (404)")
                all_available = False
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint['name']} 连接失败: {str(e)}")
            all_available = False
    
    return all_available

def run_performance_test():
    """运行性能测试"""
    print(f"\n运行性能测试...")
    
    # 测试年份选项接口响应时间
    url = f"{BASE_URL}{API_PREFIX}/year-options"
    start_time = time.time()
    result = make_request("GET", url)
    end_time = time.time()
    
    if result['success']:
        response_time = round((end_time - start_time) * 1000, 2)
        print(f"✅ 年份选项接口响应时间: {response_time}ms")
        
        if response_time < 1000:
            print("✅ 性能良好 (< 1秒)")
        elif response_time < 3000:
            print("⚠️  性能一般 (1-3秒)")
        else:
            print("❌ 性能较差 (> 3秒)")
    else:
        print("❌ 性能测试失败：接口不可用")

def main():
    """主测试流程"""
    print("=" * 60)
    print("船员年度报告API测试 - 开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务地址: {BASE_URL}")
    print("=" * 60)
    
    success_count = 0
    total_count = 0
    
    try:
        # 1. 检查API端点可用性
        if not test_api_endpoints_availability():
            print("\n⚠️  部分API端点不可用，继续测试...")
        
        # 2. 性能测试
        run_performance_test()
        
        print("\n" + "=" * 60)
        print("开始功能测试...")
        print("=" * 60)
        
        # 3. 获取年份选项列表
        result = test_get_year_options()
        total_count += 1
        if result:
            success_count += 1
        
        # 4. 获取年度报告数据（带船员ID）
        result = test_get_annual_report_data_with_id()
        total_count += 1
        if result['success']:
            success_count += 1
        
        # 5. 获取年度报告数据（不带船员ID）
        result = test_get_annual_report_data_without_id()
        total_count += 1
        if result['success']:
            success_count += 1
        
        # 6. 获取年度报告数据（无效年份）
        result = test_get_annual_report_data_invalid_year()
        total_count += 1
        # 这个测试预期可能失败，所以不计入成功统计
        
        # 7. 生成PDF报告（带船员ID）
        result = test_generate_pdf_with_id()
        total_count += 1
        if result['success']:
            success_count += 1
        
        # 8. 生成PDF报告（不带船员ID）
        result = test_generate_pdf_without_id()
        total_count += 1
        if result['success']:
            success_count += 1
        
        # 9. 生成PDF报告（无效数据）
        result = test_generate_pdf_invalid_data()
        total_count += 1
        # 这个测试预期可能失败，所以不计入成功统计
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("船员年度报告API测试 - 完成")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试结果: {success_count}/{total_count-2} 个核心功能测试通过")  # 减去2个预期失败的测试
    
    success_rate = (success_count / (total_count-2)) * 100 if total_count > 2 else 0
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("✅ 整体测试结果: 良好")
    elif success_rate >= 60:
        print("⚠️  整体测试结果: 一般")
    else:
        print("❌ 整体测试结果: 需要改进")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 