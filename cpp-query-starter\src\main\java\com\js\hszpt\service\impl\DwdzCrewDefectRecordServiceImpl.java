package com.js.hszpt.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewDefectRecord;
import com.js.hszpt.mapper.DwdzCrewDefectRecordMapper;
import com.js.hszpt.service.DwdzCrewDefectRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用缺陷记录表Service实现类
 * 
 * <AUTHOR> Generation
 */
@Slf4j
@Service
public class DwdzCrewDefectRecordServiceImpl extends ServiceImpl<DwdzCrewDefectRecordMapper, DwdzCrewDefectRecord>
        implements DwdzCrewDefectRecordService {

    @Override
    public List<DwdzCrewDefectRecord> getByInspectNo(String inspectNo) {
        if (StrUtil.isBlank(inspectNo)) {
            return new ArrayList<>();
        }
        log.info("根据检查编号查询缺陷记录，检查编号：{}", inspectNo);
        return baseMapper.selectByInspectNo(inspectNo);
    }

    @Override
    public List<DwdzCrewDefectRecord> getByInspectNoList(List<String> inspectNoList) {
        if (CollUtil.isEmpty(inspectNoList)) {
            return new ArrayList<>();
        }
        log.info("根据检查编号列表批量查询缺陷记录，检查编号数量：{}", inspectNoList.size());
        return baseMapper.selectByInspectNoList(inspectNoList);
    }

    @Override
    public List<DwdzCrewDefectRecord> getBySourceTypeAndInspectNo(String sourceType, String inspectNo) {
        if (StrUtil.isBlank(sourceType) || StrUtil.isBlank(inspectNo)) {
            return new ArrayList<>();
        }
        log.info("根据来源类型和检查编号查询缺陷记录，来源类型：{}，检查编号：{}", sourceType, inspectNo);
        return baseMapper.selectBySourceTypeAndInspectNo(sourceType, inspectNo);
    }

    @Override
    public List<Map<String, Object>> getDefectRecordsForDisplay(String inspectNo) {
        List<DwdzCrewDefectRecord> defectRecords = getByInspectNo(inspectNo);
        return convertToDisplayFormat(defectRecords);
    }

    @Override
    public Map<String, List<Map<String, Object>>> getDefectRecordsForDisplayBatch(List<String> inspectNoList) {
        if (CollUtil.isEmpty(inspectNoList)) {
            return new HashMap<>();
        }

        List<DwdzCrewDefectRecord> allDefectRecords = getByInspectNoList(inspectNoList);
        
        // 按检查编号分组
        Map<String, List<DwdzCrewDefectRecord>> groupedRecords = allDefectRecords.stream()
                .collect(Collectors.groupingBy(DwdzCrewDefectRecord::getInspectNo));

        // 转换为前端展示格式
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        for (String inspectNo : inspectNoList) {
            List<DwdzCrewDefectRecord> records = groupedRecords.get(inspectNo);
            if (records != null) {
                result.put(inspectNo, convertToDisplayFormat(records));
            } else {
                result.put(inspectNo, new ArrayList<>());
            }
        }

        return result;
    }

    /**
     * 将缺陷记录转换为前端展示格式
     * 
     * @param defectRecords 缺陷记录列表
     * @return 前端展示格式的缺陷记录列表
     */
    private List<Map<String, Object>> convertToDisplayFormat(List<DwdzCrewDefectRecord> defectRecords) {
        if (CollUtil.isEmpty(defectRecords)) {
            return new ArrayList<>();
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        
        return defectRecords.stream()
                .map(record -> {
                    Map<String, Object> displayRecord = new HashMap<>();
                    displayRecord.put("defectId", record.getDefectId());
                    displayRecord.put("sourceType", record.getSourceType());
                    displayRecord.put("inspectNo", record.getInspectNo());
                    displayRecord.put("seqNo", record.getSeqNo());
                    displayRecord.put("defectCode", record.getDefectCode());
                    displayRecord.put("defectDesc", record.getDefectDesc());
                    displayRecord.put("defectDescEn", record.getDefectDescEn());
                    displayRecord.put("processResultCode", record.getProcessResultCode());
                    displayRecord.put("processResultDesc", record.getProcessResultDesc());
                    displayRecord.put("correctFlagCode", record.getCorrectFlagCode());
                    displayRecord.put("correctFlagName", record.getCorrectFlagName());
                    displayRecord.put("defectStatusCode", record.getDefectStatusCode());
                    displayRecord.put("defectStatusName", record.getDefectStatusName());
                    displayRecord.put("defectConvCode", record.getDefectConvCode());
                    displayRecord.put("defectConvDesc", record.getDefectConvDesc());
                    displayRecord.put("actionOther", record.getActionOther());
                    displayRecord.put("defectRoCode", record.getDefectRoCode());
                    displayRecord.put("defectRoName", record.getDefectRoName());
                    displayRecord.put("ifRelateInspect", record.getIfRelateInspect());
                    displayRecord.put("enforceBasis", record.getEnforceBasis());
                    displayRecord.put("ifFindProblem", record.getIfFindProblem());
                    displayRecord.put("inspectContent", record.getInspectContent());
                    displayRecord.put("inspectResult", record.getInspectResult());
                    displayRecord.put("initInspectNo", record.getInitInspectNo());
                    displayRecord.put("apcisNo", record.getApcisNo());
                    displayRecord.put("remark", record.getRemark());
                    displayRecord.put("createDate", record.getCreateDate() != null ? dateFormat.format(record.getCreateDate()) : null);
                    displayRecord.put("operateDate", record.getOperateDate() != null ? dateFormat.format(record.getOperateDate()) : null);
                    displayRecord.put("creatorId", record.getCreatorId());
                    
                    // 添加来源类型的中文描述
                    displayRecord.put("sourceTypeDesc", getSourceTypeDesc(record.getSourceType()));
                    
                    return displayRecord;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取来源类型的中文描述
     * 
     * @param sourceType 来源类型代码
     * @return 中文描述
     */
    private String getSourceTypeDesc(String sourceType) {
        if (StrUtil.isBlank(sourceType)) {
            return "";
        }
        
        switch (sourceType) {
            case "SITE":
                return "现场检查";
            case "FSC":
                return "船旗国";
            case "PSC":
                return "港口国";
            default:
                return sourceType;
        }
    }
} 