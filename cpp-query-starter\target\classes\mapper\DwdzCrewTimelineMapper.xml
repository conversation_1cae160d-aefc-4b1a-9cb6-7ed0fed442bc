<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewTimelineMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewTimeline">
        <id column="timeline_id" property="timelineId" jdbcType="VARCHAR"/>
        <result column="crew_id" property="crewId" jdbcType="VARCHAR"/>
        <result column="event_date" property="eventDate" jdbcType="DATE"/>
        <result column="event_type" property="eventType" jdbcType="VARCHAR"/>
        <result column="sub_type" property="subType" jdbcType="VARCHAR"/>
        <result column="ref_id" property="refId" jdbcType="VARCHAR"/>
        <result column="event_title" property="eventTitle" jdbcType="VARCHAR"/>
        <result column="event_status" property="eventStatus" jdbcType="VARCHAR"/>
        <result column="event_age" property="eventAge" jdbcType="INTEGER"/>
        <result column="start_date" property="startDate" jdbcType="DATE"/>
        <result column="end_date" property="endDate" jdbcType="DATE"/>
        <result column="rec_create_date" property="recCreateDate" jdbcType="TIMESTAMP"/>
        <result column="rec_modify_date" property="recModifyDate" jdbcType="TIMESTAMP"/>
        
        <!-- 培训相关字段 -->
        <result column="training_end_date" property="trainingEndDate" jdbcType="DATE"/>
        <result column="training_status" property="trainingStatus" jdbcType="VARCHAR"/>
        <result column="training_institution" property="trainingInstitution" jdbcType="VARCHAR"/>
        <result column="training_class_name" property="trainingClassName" jdbcType="VARCHAR"/>
        <result column="training_project" property="trainingProject" jdbcType="VARCHAR"/>
        <result column="training_start_date" property="trainingStartDate" jdbcType="DATE"/>
        <result column="training_completion_date" property="trainingCompletionDate" jdbcType="DATE"/>
        <result column="training_result" property="trainingResult" jdbcType="VARCHAR"/>
        <result column="training_details" property="trainingDetails" jdbcType="VARCHAR"/>
        <result column="training_data_source" property="trainingDataSource" jdbcType="VARCHAR"/>
        <result column="training_marine_authority" property="trainingMarineAuthority" jdbcType="VARCHAR"/>
        
        <!-- 考试相关字段 -->
        <result column="exam_status" property="examStatus" jdbcType="VARCHAR"/>
        <result column="exam_location" property="examLocation" jdbcType="VARCHAR"/>
        <result column="exam_position" property="examPosition" jdbcType="VARCHAR"/>
        <result column="exam_subjects" property="examSubjects" jdbcType="VARCHAR"/>
        <result column="exam_exam_date" property="examExamDate" jdbcType="DATE"/>
        <result column="exam_result" property="examResult" jdbcType="VARCHAR"/>
        <result column="exam_marine_authority" property="examMarineAuthority" jdbcType="VARCHAR"/>
        <result column="exam_session" property="examSession" jdbcType="VARCHAR"/>
        <result column="exam_registration_date" property="examRegistrationDate" jdbcType="DATE"/>
        <result column="exam_registration_result" property="examRegistrationResult" jdbcType="VARCHAR"/>
        <result column="exam_details" property="examDetails" jdbcType="VARCHAR"/>
        <result column="exam_data_source" property="examDataSource" jdbcType="VARCHAR"/>
        <result column="exam_is_again" property="examIsAgain" jdbcType="VARCHAR"/>
        <result column="exam_competency_code" property="examCompetencyCode" jdbcType="VARCHAR"/>
        <result column="exam_period" property="examPeriod" jdbcType="VARCHAR"/>
        
        <!-- 证书申请相关字段 -->
        <result column="cert_apply_status" property="certApplyStatus" jdbcType="VARCHAR"/>
        <result column="cert_apply_certificate_name" property="certApplyCertificateName" jdbcType="VARCHAR"/>
        <result column="cert_apply_marine_authority" property="certApplyMarineAuthority" jdbcType="VARCHAR"/>
        <result column="cert_apply_certificate_number" property="certApplyCertificateNumber" jdbcType="VARCHAR"/>
        <result column="cert_apply_application_type" property="certApplyApplicationType" jdbcType="VARCHAR"/>
        <result column="cert_apply_approval_date" property="certApplyApprovalDate" jdbcType="DATE"/>
        <result column="cert_apply_application_result" property="certApplyApplicationResult" jdbcType="VARCHAR"/>
        <result column="cert_apply_details" property="certApplyDetails" jdbcType="VARCHAR"/>
        <result column="cert_apply_data_source" property="certApplyDataSource" jdbcType="VARCHAR"/>
        
        <!-- 服务相关字段 -->
        <result column="service_status" property="serviceStatus" jdbcType="VARCHAR"/>
        <result column="service_company_name" property="serviceCompanyName" jdbcType="VARCHAR"/>
        <result column="service_company_label" property="serviceCompanyLabel" jdbcType="VARCHAR"/>
        <result column="service_date" property="serviceDate" jdbcType="DATE"/>
        <result column="service_vessel_type" property="serviceVesselType" jdbcType="VARCHAR"/>
        <result column="service_vessel_name" property="serviceVesselName" jdbcType="VARCHAR"/>
        <result column="service_position" property="servicePosition" jdbcType="VARCHAR"/>
        <result column="service_appointment_date" property="serviceAppointmentDate" jdbcType="DATE"/>
        <result column="service_location" property="serviceLocation" jdbcType="VARCHAR"/>
        <result column="service_details" property="serviceDetails" jdbcType="VARCHAR"/>
        <result column="service_data_source" property="serviceDataSource" jdbcType="VARCHAR"/>
        <result column="service_duration_days" property="serviceDurationDays" jdbcType="INTEGER"/>
        <result column="service_end_date" property="serviceEndDate" jdbcType="DATE"/>
        <result column="service_end_location" property="serviceEndLocation" jdbcType="VARCHAR"/>
        <result column="service_vessel_number" property="serviceVesselNumber" jdbcType="VARCHAR"/>
        <result column="service_trainee_position" property="serviceTraineePosition" jdbcType="VARCHAR"/>
        <result column="service_ship_tonnage" property="serviceShipTonnage" jdbcType="DECIMAL"/>
        <result column="service_ship_engine_power" property="serviceShipEnginePower" jdbcType="DECIMAL"/>
        <result column="service_navi_category" property="serviceNaviCategory" jdbcType="VARCHAR"/>
        <result column="service_vessel_nationality" property="serviceVesselNationality" jdbcType="VARCHAR"/>
        <result column="service_voyage_direction" property="serviceVoyageDirection" jdbcType="VARCHAR"/>
        <result column="service_source" property="serviceSource" jdbcType="VARCHAR"/>
        <result column="service_dsp_company_name" property="serviceDspCompanyName" jdbcType="VARCHAR"/>
        <result column="service_dsp_company_id" property="serviceDspCompanyId" jdbcType="VARCHAR"/>
        <result column="service_vessel_regno" property="serviceVesselRegno" jdbcType="VARCHAR"/>
        <result column="service_vessel_imo" property="serviceVesselImo" jdbcType="VARCHAR"/>
        <result column="service_vessel_id" property="serviceVesselId" jdbcType="VARCHAR"/>
        <result column="service_vessel_name_en" property="serviceVesselNameEn" jdbcType="VARCHAR"/>
        <result column="service_vessel_level" property="serviceVesselLevel" jdbcType="VARCHAR"/>
        
        <!-- 体检相关字段 -->
        <result column="medical_status" property="medicalStatus" jdbcType="VARCHAR"/>
        <result column="medical_hospital" property="medicalHospital" jdbcType="VARCHAR"/>
        <result column="medical_department" property="medicalDepartment" jdbcType="VARCHAR"/>
        <result column="medical_exam_date" property="medicalExamDate" jdbcType="DATE"/>
        <result column="medical_result" property="medicalResult" jdbcType="VARCHAR"/>
        <result column="medical_details" property="medicalDetails" jdbcType="VARCHAR"/>
        <result column="medical_data_source" property="medicalDataSource" jdbcType="VARCHAR"/>
        <result column="medical_marine_authority" property="medicalMarineAuthority" jdbcType="VARCHAR"/>
        
        <!-- 出入境相关字段 -->
        <result column="immigration_status" property="immigrationStatus" jdbcType="VARCHAR"/>
        <result column="immigration_destination_country" property="immigrationDestinationCountry" jdbcType="VARCHAR"/>
        <result column="immigration_border_control" property="immigrationBorderControl" jdbcType="VARCHAR"/>
        <result column="immigration_port" property="immigrationPort" jdbcType="VARCHAR"/>
        <result column="immigration_entry_exit_time" property="immigrationEntryExitTime" jdbcType="TIMESTAMP"/>
        <result column="immigration_direction" property="immigrationDirection" jdbcType="VARCHAR"/>
        <result column="immigration_details" property="immigrationDetails" jdbcType="VARCHAR"/>
        <result column="immigration_data_source" property="immigrationDataSource" jdbcType="VARCHAR"/>
        
        <!-- 监管相关字段 -->
        <result column="supervision_authority" property="supervisionAuthority" jdbcType="VARCHAR"/>
        <result column="supervision_company_name" property="supervisionCompanyName" jdbcType="VARCHAR"/>
        <result column="supervision_vessel_name" property="supervisionVesselName" jdbcType="VARCHAR"/>
        <result column="supervision_processing_date" property="supervisionProcessingDate" jdbcType="DATE"/>
        <result column="supervision_award_reason" property="supervisionAwardReason" jdbcType="VARCHAR"/>
        <result column="supervision_award_content" property="supervisionAwardContent" jdbcType="VARCHAR"/>
        <result column="supervision_score_points" property="supervisionScorePoints" jdbcType="VARCHAR"/>
        <result column="supervision_defect_details" property="supervisionDefectDetails" jdbcType="VARCHAR"/>
        <result column="supervision_case_number" property="supervisionCaseNumber" jdbcType="VARCHAR"/>
        <result column="supervision_incident_date" property="supervisionIncidentDate" jdbcType="DATE"/>
        <result column="supervision_incident_location" property="supervisionIncidentLocation" jdbcType="VARCHAR"/>
        <result column="supervision_violation_location" property="supervisionViolationLocation" jdbcType="VARCHAR"/>
        <result column="supervision_penalty_decision" property="supervisionPenaltyDecision" jdbcType="VARCHAR"/>
        <result column="supervision_certificate_detain" property="supervisionCertificateDetain" jdbcType="VARCHAR"/>
        <result column="supervision_details" property="supervisionDetails" jdbcType="VARCHAR"/>
        <result column="supervision_data_source" property="supervisionDataSource" jdbcType="VARCHAR"/>
        <result column="supervision_sub_status" property="supervisionSubStatus" jdbcType="VARCHAR"/>
        <result column="supervision_vessel_number" property="supervisionVesselNumber" jdbcType="VARCHAR"/>
        <result column="supervision_inspect_no" property="supervisionInspectNo" jdbcType="VARCHAR"/>
        <result column="supervision_init_code" property="supervisionInitCode" jdbcType="VARCHAR"/>
        
        <!-- 职业转型相关字段 -->
        <result column="career_change_status" property="careerChangeStatus" jdbcType="VARCHAR"/>
        <result column="career_change_company" property="careerChangeCompany" jdbcType="VARCHAR"/>
        <result column="career_change_new_identity" property="careerChangeNewIdentity" jdbcType="VARCHAR"/>
        <result column="career_change_effective_date" property="careerChangeEffectiveDate" jdbcType="DATE"/>
        <result column="career_change_details" property="careerChangeDetails" jdbcType="VARCHAR"/>
        <result column="career_change_data_source" property="careerChangeDataSource" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        timeline_id, crew_id, event_date, event_type, sub_type, ref_id, event_title, 
        event_status, event_age, start_date, end_date, rec_create_date, rec_modify_date,
        training_end_date, training_status, training_institution, training_class_name, 
        training_project, training_start_date, training_completion_date, training_result, 
        training_details, training_data_source, training_marine_authority,
        exam_status, exam_location, exam_position, exam_subjects, exam_exam_date, 
        exam_result, exam_marine_authority, exam_session, exam_registration_date, 
        exam_registration_result, exam_details, exam_data_source, exam_is_again, exam_competency_code, exam_period,
        cert_apply_status, cert_apply_certificate_name, cert_apply_marine_authority, 
        cert_apply_certificate_number, cert_apply_application_type, cert_apply_approval_date, 
        cert_apply_application_result, cert_apply_details, cert_apply_data_source,cert_apply_comp_name,cert_apply_form_name,
        service_status, service_company_name, service_company_label, service_date, 
        service_vessel_type, service_vessel_name, service_position, service_appointment_date, 
        service_location, service_details, service_data_source, service_duration_days,
        service_end_date, service_end_location, service_vessel_number, service_trainee_position,
        service_ship_tonnage, service_ship_engine_power, service_navi_category, 
        service_vessel_nationality, service_voyage_direction, service_source, service_dsp_company_name, 
        service_dsp_company_id, service_vessel_regno, service_vessel_imo, service_vessel_id, 
        service_vessel_name_en, service_vessel_level,
        medical_status, medical_hospital, medical_department, medical_exam_date, 
        medical_result, medical_details, medical_data_source, medical_marine_authority,
        immigration_status, immigration_destination_country, immigration_border_control, 
        immigration_port, immigration_entry_exit_time, immigration_direction, 
        immigration_details, immigration_data_source,
        supervision_authority, supervision_company_name, supervision_vessel_name, 
        supervision_processing_date, supervision_award_reason, supervision_award_content, 
        supervision_score_points, supervision_defect_details, supervision_case_number, 
        supervision_incident_date, supervision_incident_location, supervision_violation_location, 
        supervision_penalty_decision, supervision_certificate_detain, supervision_details, 
        supervision_data_source, supervision_sub_status, supervision_vessel_number, supervision_inspect_no, 
        supervision_init_code,
        career_change_status, career_change_company, career_change_new_identity, 
        career_change_effective_date, career_change_details, career_change_data_source
    </sql>

    <!-- 根据船员ID查询时间轴数据 -->
    <select id="selectByCrewId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        ORDER BY event_date DESC, rec_create_date DESC
    </select>

    <!-- 根据船员ID和年份查询时间轴数据 -->
    <select id="selectByCrewIdAndYear" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        AND (
            <!-- 事件日期在指定年份 -->
            <![CDATA[EXTRACT(YEAR FROM event_date) = #{year,jdbcType=INTEGER}]]>
            <!-- 或者履职开始日期在指定年份 -->
            OR (event_type = 'service' AND service_date IS NOT NULL AND <![CDATA[EXTRACT(YEAR FROM service_date) = #{year,jdbcType=INTEGER}]]>)
            <!-- 或者履职结束日期在指定年份 -->
            OR (event_type = 'service' AND service_end_date IS NOT NULL AND <![CDATA[EXTRACT(YEAR FROM service_end_date) = #{year,jdbcType=INTEGER}]]>)
            <!-- 或者培训开始日期在指定年份 -->
            OR (event_type = 'training' AND training_start_date IS NOT NULL AND <![CDATA[EXTRACT(YEAR FROM training_start_date) = #{year,jdbcType=INTEGER}]]>)
            <!-- 或者培训结束日期在指定年份 -->
            OR (event_type = 'training' AND training_completion_date IS NOT NULL AND <![CDATA[EXTRACT(YEAR FROM training_completion_date) = #{year,jdbcType=INTEGER}]]>)
            <!-- 或者开始日期在指定年份 -->
            OR (start_date IS NOT NULL AND <![CDATA[EXTRACT(YEAR FROM start_date) = #{year,jdbcType=INTEGER}]]>)
            <!-- 或者结束日期在指定年份 -->
            OR (end_date IS NOT NULL AND <![CDATA[EXTRACT(YEAR FROM end_date) = #{year,jdbcType=INTEGER}]]>)
        )
        ORDER BY event_date DESC, rec_create_date DESC
    </select>

    <!-- 根据船员ID和事件类型查询时间轴数据 -->
    <select id="selectByCrewIdAndEventType" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        AND event_type = #{eventType,jdbcType=VARCHAR}
        ORDER BY event_date DESC, rec_create_date DESC
    </select>

    <!-- 根据船员ID、事件类型、船舶类型、职务类型查询时间轴数据 -->
    <select id="selectByCrewIdAndEventTypeWithConditions" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        AND event_type = #{eventType,jdbcType=VARCHAR}
        <if test="shipType != null and shipType != ''">
            AND service_vessel_type = #{shipType,jdbcType=VARCHAR}
        </if>
        <if test="dutyType != null and dutyType != ''">
            AND service_position = #{dutyType,jdbcType=VARCHAR}
        </if>
        ORDER BY event_date DESC, rec_create_date DESC
    </select>

    <!-- 根据查询条件查询时间轴数据（增强版） -->
    <select id="selectByQueryConditions" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{query.crewId,jdbcType=VARCHAR}
        AND event_type = #{query.eventType,jdbcType=VARCHAR}
        <!-- service类型的额外查询条件 -->
        <if test="query.dateStart != null and query.dateStart != ''">
            <![CDATA[AND event_date >= #{query.dateStart,jdbcType=VARCHAR}]]>
        </if>
        <if test="query.dateEnd != null and query.dateEnd != ''">
            <![CDATA[AND event_date <= #{query.dateEnd,jdbcType=VARCHAR}]]>
        </if>
        <if test="query.dutyType != null and query.dutyType != ''">
            AND service_position = #{query.dutyType,jdbcType=VARCHAR}
        </if>
        <if test="query.shipType != null and query.shipType != ''">
            AND service_vessel_type = #{query.shipType,jdbcType=VARCHAR}
        </if>
        <if test="query.businessData != null and query.businessData != ''">
            -- 待确认查询字段
        </if>
        ORDER BY event_date DESC, rec_create_date DESC
    </select>

    <!-- 根据船员ID和日期范围查询时间轴数据 -->
    <select id="selectByCrewIdAndDateRange" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        <if test="startDate != null">
            AND event_date &gt;= #{startDate,jdbcType=DATE}
        </if>
        <if test="endDate != null">
            <![CDATA[AND event_date &lt;= #{endDate,jdbcType=DATE}]]>
        </if>
        ORDER BY event_date DESC, rec_create_date DESC
    </select>

    <!-- 分页查询船员时间轴数据 -->
    <select id="selectByCrewIdWithPagination" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        ORDER BY event_date DESC, rec_create_date DESC
        LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <select id="getByCrewIdAndEventTypeList" resultType="com.js.hszpt.entity.DwdzCrewTimeline">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_timeline
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        AND event_type in
        <foreach item="item" collection="eventTypeList" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ORDER BY event_date DESC, rec_create_date DESC
    </select>

</mapper> 