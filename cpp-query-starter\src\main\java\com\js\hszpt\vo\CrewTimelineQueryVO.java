package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 船员时间轴查询参数VO
 */
@Data
@ApiModel(value = "船员时间轴查询参数")
public class CrewTimelineQueryVO {

    /**
     * 船员ID
     */
    @ApiModelProperty(value = "船员ID", required = true)
    private String crewId;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型（training/examination/certificate/service/medical/entryexit/supervision/transition）")
    private String eventType;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String dateStart;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String dateEnd;

    /**
     * 职务类型
     */
    @ApiModelProperty(value = "职务类型")
    private String dutyType;

    /**
     * 船舶类型
     */
    @ApiModelProperty(value = "船舶类型")
    private String shipType;

    /**
     * 动态类型
     */
    @ApiModelProperty(value = "动态类型")
    private String dynamicType;

    /**
     * 业务数据类型
     */
    @ApiModelProperty(value = "业务数据类型")
    private String businessData;

    /**
     * 资历来源（1-派遣报备即外轮，2-国轮）
     */
    @ApiModelProperty(value = "资历来源（1-派遣报备即外轮，2-国轮）")
    private String serviceSource;

    /**
     * 外派公司名称
     */
    @ApiModelProperty(value = "外派公司名称")
    private String dspCompanyName;

    /**
     * 船舶等级
     */
    @ApiModelProperty(value = "船舶等级")
    private String vesselLevel;

    /**
     * 考试期数
     */
    @ApiModelProperty(value = "考试期数")
    private String examPeriod;
}