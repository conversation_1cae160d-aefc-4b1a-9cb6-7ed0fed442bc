# 船员职务分类API接口文档

## 概述

本文档描述了船员职务分类管理系统的API接口规范，提供船员职务分类信息的查询功能。

**基础信息**
- 服务地址：`http://localhost:8285`
- API前缀：`/api/position-category`
- 数据格式：JSON
- 字符编码：UTF-8

## 通用响应格式

所有API接口都采用统一的响应格式：

```json
{
  "success": true,           // 请求是否成功
  "message": "success",      // 响应消息
  "code": 200,              // 响应状态码
  "timestamp": 1750671455297, // 时间戳
  "result": {}              // 业务数据
}
```

**响应状态码说明**
- `200`: 请求成功
- `500`: 服务器内部错误

---

## 1. 获取职务分类列表

### 接口信息
- **接口地址**: `GET /api/position-category/list`
- **接口描述**: 获取所有船员职务分类信息，按排序字段升序排列
- **请求方式**: GET

### 请求参数
无

### 响应参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| success | boolean | 是 | 请求是否成功 |
| message | string | 是 | 响应消息 |
| code | integer | 是 | 响应状态码 |
| timestamp | long | 是 | 时间戳 |
| result | array | 是 | 职务分类数组 |

**result数组元素结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| categoryId | string | 是 | 分类唯一标识ID |
| positionCode | string | 是 | 职务编码 |
| positionName | string | 是 | 职务名称 |
| positionLevel | string | 是 | 职务级别（management/operational/support） |
| totalCount | integer | 是 | 该职务总人数 |
| positionDescription | string | 否 | 职务描述 |
| recCreateDate | string | 是 | 记录创建日期 |
| recModifyDate | string | 是 | 记录修改日期 |
| sort | number | 否 | 排序值 |

### 请求示例

```bash
GET /api/position-category/list
```

### 响应示例

**成功响应**
```json
{
  "success": true,
  "message": "查询职务分类列表成功",
  "code": 200,
  "timestamp": 1750671455297,
  "result": [
    {
      "categoryId": "CAT001",
      "positionCode": "CAPTAIN",
      "positionName": "船长",
      "positionLevel": "management",
      "totalCount": 150,
      "positionDescription": "船舶最高指挥官，负责船舶的安全航行和全面管理",
      "recCreateDate": "2024-01-01T00:00:00",
      "recModifyDate": "2024-01-01T00:00:00",
      "sort": 1.0
    },
    {
      "categoryId": "CAT002",
      "positionCode": "CHIEF_OFFICER",
      "positionName": "大副",
      "positionLevel": "management",
      "totalCount": 320,
      "positionDescription": "甲板部负责人，协助船长管理船舶",
      "recCreateDate": "2024-01-01T00:00:00",
      "recModifyDate": "2024-01-01T00:00:00",
      "sort": 2.0
    },
    {
      "categoryId": "CAT003",
      "positionCode": "CHIEF_ENGINEER",
      "positionName": "轮机长",
      "positionLevel": "management",
      "totalCount": 180,
      "positionDescription": "轮机部负责人，负责船舶机械设备的运行管理",
      "recCreateDate": "2024-01-01T00:00:00",
      "recModifyDate": "2024-01-01T00:00:00",
      "sort": 3.0
    },
    {
      "categoryId": "CAT004",
      "positionCode": "SECOND_OFFICER",
      "positionName": "二副",
      "positionLevel": "operational",
      "totalCount": 450,
      "positionDescription": "负责航行值班和导航设备管理",
      "recCreateDate": "2024-01-01T00:00:00",
      "recModifyDate": "2024-01-01T00:00:00",
      "sort": 4.0
    },
    {
      "categoryId": "CAT005",
      "positionCode": "THIRD_OFFICER",
      "positionName": "三副",
      "positionLevel": "operational",
      "totalCount": 560,
      "positionDescription": "负责航行值班和救生设备管理",
      "recCreateDate": "2024-01-01T00:00:00",
      "recModifyDate": "2024-01-01T00:00:00",
      "sort": 5.0
    },
    {
      "categoryId": "CAT006",
      "positionCode": "ABLE_SEAMAN",
      "positionName": "值班水手",
      "positionLevel": "support",
      "totalCount": 1200,
      "positionDescription": "负责甲板部日常维护和航行值班",
      "recCreateDate": "2024-01-01T00:00:00",
      "recModifyDate": "2024-01-01T00:00:00",
      "sort": 6.0
    }
  ]
}
```

**失败响应**
```json
{
  "success": false,
  "message": "查询职务分类列表失败：数据库连接异常",
  "code": 500,
  "timestamp": 1750671455297,
  "result": null
}
```

---

## 2. 数据字典

### 2.1 职务级别说明

| 级别值 | 中文名称 | 说明 |
|--------|----------|------|
| management | 管理级 | 船舶管理岗位，如船长、大副、轮机长等 |
| operational | 操作级 | 船舶操作岗位，如二副、三副、二管轮等 |
| support | 支持级 | 船舶支持岗位，如值班水手、值班机工等 |

### 2.2 常见职务编码

| 职务编码 | 职务名称 | 职务级别 |
|----------|----------|----------|
| CAPTAIN | 船长 | management |
| CHIEF_OFFICER | 大副 | management |
| CHIEF_ENGINEER | 轮机长 | management |
| SECOND_OFFICER | 二副 | operational |
| THIRD_OFFICER | 三副 | operational |
| SECOND_ENGINEER | 二管轮 | operational |
| THIRD_ENGINEER | 三管轮 | operational |
| ABLE_SEAMAN | 值班水手 | support |
| ABLE_ENGINEER | 值班机工 | support |

---

## 3. 错误处理

### 3.1 错误响应格式

当接口出现错误时，响应格式如下：

```json
{
  "success": false,
  "message": "具体错误信息",
  "code": 500,
  "timestamp": 1750671455297,
  "result": null
}
```

### 3.2 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 500 | 服务器内部错误 | 检查请求参数格式，联系后端开发人员 |

### 3.3 常见错误信息

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "查询职务分类列表失败" | 数据库连接异常或查询出错 | 联系后端开发人员检查数据库连接 |
| "数据库连接异常" | 数据库服务不可用 | 检查数据库服务状态 |
| "SQL执行异常" | 数据库查询语句错误 | 联系后端开发人员检查SQL语句 |

---

## 4. 接口测试

### 4.1 测试工具推荐

推荐使用以下工具进行接口测试：
- Postman
- curl命令
- 浏览器开发者工具

### 4.2 curl测试示例

```bash
# 获取职务分类列表
curl -X GET "http://localhost:8285/api/position-category/list"
```

### 4.3 测试要点

1. **正常请求测试**
   - 验证返回数据格式是否正确
   - 验证数据是否按sort字段排序
   - 验证必填字段是否完整

2. **异常情况测试**
   - 网络异常时的错误处理
   - 数据库连接异常时的错误处理
   - 空数据时的返回格式

---

## 5. 业务规则

### 5.1 数据排序规则

- 主要按`sort`字段升序排列
- 相同`sort`值的记录按`recCreateDate`降序排列
- 返回所有有效记录，不进行分页

### 5.2 数据过滤规则

- 只返回状态为有效的职务分类记录
- 不返回已删除或禁用的记录

### 5.3 字段说明

- `categoryId`: 系统自动生成的唯一标识
- `positionCode`: 职务的标准编码，用于系统内部识别
- `positionName`: 职务的中文名称，用于显示
- `positionLevel`: 职务级别，用于分类管理
- `totalCount`: 当前该职务的总人数统计
- `positionDescription`: 职务的详细描述信息
- `recCreateDate`: 记录创建时间
- `recModifyDate`: 记录最后修改时间
- `sort`: 排序权重，数值越小排序越靠前

---

## 6. 性能说明

### 6.1 响应时间

- 正常情况下响应时间 < 500ms
- 数据量较大时响应时间 < 2s

### 6.2 并发处理

- 支持多用户并发访问
- 建议单用户请求频率不超过每秒10次

### 6.3 缓存策略

- 建议客户端对数据进行适当缓存
- 缓存时间建议设置为5-10分钟
- 数据更新后及时清除缓存

---

## 7. 安全说明

### 7.1 访问控制

- 接口需要用户登录认证
- 建议添加接口访问频率限制
- 敏感数据建议进行脱敏处理

### 7.2 数据安全

- 所有数据传输使用HTTPS加密
- 敏感信息不在日志中记录
- 定期备份重要数据

---

## 8. 接口总览

| 序号 | 接口名称 | HTTP方法 | 接口地址 | 说明 |
|------|----------|----------|----------|------|
| 1 | 获取职务分类列表 | GET | `/api/position-category/list` | 获取所有职务分类信息 |

---

## 9. 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2025-06-23 | 初始版本，包含职务分类列表查询接口 |

---

## 10. 技术支持

如有接口使用问题，请联系后端开发团队或查看项目文档。

**注意**: 本文档基于当前系统测试结果生成，所有接口都已验证可正常使用。

---

## 11. 相关文档

- [船员年度报告API接口文档](./船员年度报告API接口文档.md)
- [船员管理系统API接口文档](./船员管理系统API接口文档.md)
- [项目README文档](./README.md) 