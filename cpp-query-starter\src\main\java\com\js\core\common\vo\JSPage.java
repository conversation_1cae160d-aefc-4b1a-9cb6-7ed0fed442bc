package com.js.core.common.vo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;

import lombok.Data;

/**
 * 框架统一Page对象
 * 
 * <AUTHOR>
 * @param <T>
 *
 */
@Data
public class JSPage<T> {
    /**
     * 查询数据列表
     */
    private List<T> records = Collections.emptyList();

    /**
     * 总数
     */
    private long total = 0;
    /**
     * 每页显示条数，默认 10
     */
    private long size = 10;

    /**
     * 当前页
     */
    private long current = 1;
    
    private long pages ; //当前分页总页数

    /**
     * 排序字段信息
     */
    private List<OrderItem> orders = new ArrayList<>();
    
    /**
     * 分页构造函数
     *
     * @param current 当前页
     * @param size    每页显示条数
     */
    public JSPage(long current, long size) {
        this(current, size, 0);
    }
  
    public JSPage(long current, long size, long total) {
        if (current > 1) {
            this.current = current;
        }
        this.size = size;
        this.total = total;
    }

    public JSPage(long size, long current, long total, List<T> records) {
    	this(size, current, total, 0, records);
    }

	public JSPage(long size, long current, long total, long pages,List<T> records) {
		this.size = size;
		this.current = current;
		this.total = total;
		this.pages = pages;
		this.records = records;
	}
}
