package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewTimeline;
import com.js.hszpt.vo.CrewTimelineQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 船员动态履历时间轴宽表Mapper接口
 * 
 * <AUTHOR> Generation
 */
@Mapper
public interface DwdzCrewTimelineMapper extends BaseMapper<DwdzCrewTimeline> {

        /**
         * 根据船员ID查询时间轴数据
         * 
         * @param crewId 船员ID
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> selectByCrewId(@Param("crewId") String crewId);

        /**
         * 根据船员ID和年份查询时间轴数据
         * 
         * @param crewId 船员ID
         * @param year   年份
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> selectByCrewIdAndYear(@Param("crewId") String crewId, @Param("year") Integer year);

        /**
         * 根据船员ID和事件类型查询时间轴数据
         * 
         * @param crewId    船员ID
         * @param eventType 事件类型
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> selectByCrewIdAndEventType(@Param("crewId") String crewId,
                        @Param("eventType") String eventType);

        /**
         * 根据船员ID和事件类型查询时间轴数据
         *
         * @param crewId    船员ID
         * @param eventTypeList 事件类型列表
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> getByCrewIdAndEventTypeList(@Param("crewId") String crewId,
                                                          @Param("eventTypeList") List<String> eventTypeList);

        /**
         * 根据船员ID、事件类型、船舶类型、职务类型查询时间轴数据
         * 
         * @param crewId    船员ID
         * @param eventType 事件类型
         * @param shipType  船舶类型
         * @param dutyType  职务类型
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> selectByCrewIdAndEventTypeWithConditions(@Param("crewId") String crewId,
                        @Param("eventType") String eventType, @Param("shipType") String shipType,
                        @Param("dutyType") String dutyType);

        /**
         * 根据查询条件查询时间轴数据（增强版）
         * 
         * @param queryVO 查询参数对象
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> selectByQueryConditions(@Param("query") CrewTimelineQueryVO queryVO);

        /**
         * 根据船员ID和日期范围查询时间轴数据
         * 
         * @param crewId    船员ID
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> selectByCrewIdAndDateRange(@Param("crewId") String crewId,
                        @Param("startDate") Date startDate,
                        @Param("endDate") Date endDate);

        /**
         * 分页查询船员时间轴数据
         * 
         * @param crewId 船员ID
         * @param offset 偏移量
         * @param limit  限制数量
         * @return 时间轴数据列表
         */
        List<DwdzCrewTimeline> selectByCrewIdWithPagination(@Param("crewId") String crewId,
                        @Param("offset") Integer offset,
                        @Param("limit") Integer limit);
}