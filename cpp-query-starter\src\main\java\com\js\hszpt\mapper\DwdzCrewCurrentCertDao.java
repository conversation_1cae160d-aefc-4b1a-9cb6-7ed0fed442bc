package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewCurrentCert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员当前持证情况数据处理层
 * 
 * @ClassName: DwdzCrewCurrentCertDao
 * @Description: 船员当前持证情况数据处理层
 * @author: System Generation
 */
public interface DwdzCrewCurrentCertDao extends BaseMapper<DwdzCrewCurrentCert> {

    /**
     * 根据船员ID查询证书列表
     * 
     * @param crewId 船员ID
     * @return 证书列表
     */
    List<DwdzCrewCurrentCert> getByCrewId(@Param("crewId") String crewId);

    /**
     * 根据船员ID和证书类别查询证书列表
     * 
     * @param crewId       船员ID
     * @param certCategory 证书类别
     * @return 证书列表
     */
    List<DwdzCrewCurrentCert> getByCrewIdAndCategory(@Param("crewId") String crewId,
            @Param("certCategory") String certCategory);

    /**
     * 批量查询船员是否持有适任证书
     * 
     * @param crewIds 船员ID列表
     * @return 持有适任证书的船员ID列表
     */
    List<String> getCrewIdsWithCompetencyCert(@Param("crewIds") List<String> crewIds);

    /**
     * 批量查询船员证书信息
     * 
     * @param crewIds 船员ID列表
     * @return 证书列表
     */
    List<DwdzCrewCurrentCert> getByCrewIds(@Param("crewIds") List<String> crewIds);
}