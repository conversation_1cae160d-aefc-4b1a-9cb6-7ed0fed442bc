package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewPositionReqImgcfg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 晋升要求总览图片配置表DAO接口
 * 提供晋升要求总览图片配置数据的数据库访问方法
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCrewPositionReqImgcfgDao extends BaseMapper<DwdzCrewPositionReqImgcfg> {

    /**
     * 获取所有图片配置信息
     * 用于系统启动时加载到内存
     * 
     * @return 所有图片配置信息列表
     */
    @Select("SELECT cfg_id, visit_file_name, source_file_id, outside_url, intranet_url, remark " +
            "FROM dwdz_crew_position_req_imgcfg " +
            "ORDER BY rec_create_date ASC")
    List<DwdzCrewPositionReqImgcfg> selectAllConfigs();
}
