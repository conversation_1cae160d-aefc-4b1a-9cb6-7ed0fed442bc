package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewShipService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 船员船舶类型履职统计表 Mapper接口
 */
@Mapper
public interface DwdzCrewShipServiceMapper extends BaseMapper<DwdzCrewShipService> {
    /**
     * 根据船员ID查询船舶类型履职统计
     */
    List<DwdzCrewShipService> selectByCrewId(@Param("crewId") String crewId,@Param("statType") String statType);
} 