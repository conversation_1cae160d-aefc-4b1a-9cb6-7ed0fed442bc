<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewPositionServiceMapper">

    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewPositionService">
        <id column="position_id" property="positionId"/>
        <result column="crew_id" property="crewId"/>
        <result column="position_name" property="positionName"/>
        <result column="ship_type" property="shipType"/>
        <result column="total_months" property="totalMonths"/>
        <result column="is_first_position" property="isFirstPosition"/>
        <result column="promotion_from" property="promotionFrom"/>
        <result column="promotion_duration" property="promotionDuration"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
    </resultMap>

    <select id="selectByCrewId" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_position_service WHERE crew_id = #{crewId}
    </select>
</mapper> 