package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.core.service.BaseService;
import com.js.hszpt.entity.DwdzCrewSummary;
import com.js.hszpt.entity.DwdzCrewSvWarningInfo;

import java.util.List;
import java.util.Map;

/**
 * 船员履职预警信息主表Service接口
 * <AUTHOR> Generation
 */
public interface DwdzCrewSvWarningInfoService extends IService<DwdzCrewSvWarningInfo> {

    /**
     * 根据船员ID查询预警信息
     * @param crewId 船员ID
     * @return 预警信息列表
     */
    List<DwdzCrewSvWarningInfo> getByCrewId(String crewId);

    /**
     * 根据船员ID和预警级别查询预警信息
     * @param crewId 船员ID
     * @param warningLevel 预警级别
     * @return 预警信息列表
     */
    List<DwdzCrewSvWarningInfo> getByCrewIdAndLevel(String crewId, String warningLevel);

    /**
     * 更新预警信息为已读状态
     * @param warningId 预警ID
     * @return 更新结果
     */
    boolean updateAsRead(String warningId);

    /**
     * 获取预警信息（格式化后的数据）
     * @param crewId 船员ID
     * @return 预警信息列表
     */
    List<Map<String, Object>> getWarnings(String crewId);
} 