<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DemoDao">

	<!-- 查询条件构造 -->
	<sql id="where">
		<where>


		</where>
	</sql>

	<!-- 注意：只要需要表关联这种，自定义SQL才写在xml中 -->
	<!-- 以下方法为虚构需求，无实际意义 -->
	<select id="findList" resultType="com.js.hszpt.entity.Demo">
		select 1 from dual;
		<include refid="where" />
		order by t.id desc
	</select>


</mapper>