<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewTrainSubjectDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewTrainSubject">
        <id column="subject_id" property="subjectId" />
        <result column="project_name" property="projectName" />
        <result column="project_code" property="projectCode" />
        <result column="subject_name" property="subjectName" />
        <result column="subject_code" property="subjectCode" />
        <result column="subject_type" property="subjectType" />
        <result column="cert_type" property="certType" />
        <result column="cert_name" property="certName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        subject_id, project_name, project_code, subject_name, subject_code, 
        subject_type, cert_type, cert_name
    </sql>

    <!-- 根据科目ID列表查询培训项目科目信息 -->
    <select id="selectBySubjectIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dwdz_crew_train_subject
        WHERE subject_id IN
        <foreach collection="subjectIds" item="subjectId" open="(" separator="," close=")">
            #{subjectId}
        </foreach>
        ORDER BY cert_type ASC, project_name ASC, subject_name ASC
    </select>

</mapper> 