# 配置文件加密key(加密的salt)
jasypt:
  encryptor:
    password: jsdp

server:
  port: 8185
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 禁用JPA相关自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  # 数据源
  datasource:
    dynamic:
      primary: dzzzdwdz  # 设置主数据源名称
      strict: false
      datasource:
        dzzzdwdb: # 数据中台-标准库
          url: *****************************************************************************************************************
          username: system
          password: system
          driverClassName: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 25
            minimum-idle: 8
            idle-timeout: 60000
            connection-timeout: 30000
            max-lifetime: 1800000
            auto-commit: true
            pool-name: DzzzdwdbHikariCP-LOCAL
            validation-timeout: 5000
            connection-test-query: SELECT 1
            leak-detection-threshold: 60000
        dzzzdwdz: # 数据中台-主题库
          url: *****************************************************************************************************************
          username: system
          password: system
          driverClassName: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 50
            minimum-idle: 10
            idle-timeout: 60000
            connection-timeout: 30000
            max-lifetime: 1800000
            auto-commit: true
            pool-name: DzzzdwdzHikariCP-LOCAL
            validation-timeout: 5000
            connection-test-query: SELECT 1
            leak-detection-threshold: 60000
        dzzzdws: # 数据中台-专题库
          url: ***************************************************************************************************************
          username: system
          password: system
          driverClassName: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            maximum-pool-size: 50
            minimum-idle: 6
            idle-timeout: 60000
            connection-timeout: 30000
            max-lifetime: 1800000
            auto-commit: true
            pool-name: DzzzdwsHikariCP-LOCAL
            validation-timeout: 5000
            connection-test-query: SELECT 1
            leak-detection-threshold: 60000
      # Druid配置已移除，使用HikariCP连接池
  # JPA配置
  jpa:
    enabled: false
    show-sql: false
    hibernate:
      ddl-auto: none
  # Redis
  redis:
    open: true #开启Redis配置
    standalone: true #是否是单机环境。默认单机
    host: **************
    #password: csp@redis@2023
    # 数据库索引 默认0
    database: 12
    port: 16379
    # 超时时间   3秒
    timeout: 3000
    # 连接池配置
    lettuce:
      pool:
        max-active: 100
        max-wait: -1
        max-idle: 10
        min-idle: 5
      #jedis:
      #pool:
      #max-active: 1000 #连接池最大连接数
      #max-wait: -1 #连接池最大阻塞等待时间(使用负值没有限制)
      #max-idle: 10 #连接池中的最大空闲连接
      #min-idle: 5 #连接池中的最小空闲连接
      #集群配置
      #cluster:
      #nodes: #集群模式ip
      #- **************:7001
      #- **************:7002
      #- **************:7003
    #- **************:7004
    #- **************:7005
    #- **************:7006
  # 定时任务
  quartz:
    # 完全禁用Quartz
    enabled: false
    auto-startup: false
  # 工作流
  activiti:
    check-process-definitions: false
    db-identity-used: true
    # 自动生成Activiti相关表 第一次生成后建议关闭提高运行速度
    database-schema-update: true
    history-level: full
    # 扩展配置
    jsdp:
      # 流程图字体 默认宋体
      activityFontName: Microsoft YaHei
      labelFontName: Microsoft YaHei
  # 文件大小上传配置
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
  # 定时任务多线程配置
  task:
    scheduling:
      pool:
        # 比定时任务个数大
        size: 5
      thread-name-prefix: task-scheduling-
      enabled: false  # 全局禁用定时任务

#启用-webmvc配置功能（单独服务必须启动）
jsdp.webmvc.config.default: true
#启用-服务端渗透拦截器功能
jsdp.interceptor.config.default: false
#启用-服务端跨域请求拦截器功能
jsdp.cors.config.default: false
#启用-服务端tomcat请求限制功能
jsdp.tomcat.config.default: true

jsdp:
  security:
    smProperties:
      #国密2私钥
      sm2PrivateKey: 442024eb61ff92a4119c53ca24b0f8d07ab738706010ee69cc70b95df6a67472
      #国密2公钥
      sm2PublicKey: 04EF46D28BDE3F34CCF5052191B378C0E63AAC216C59AFD6AA2BE5B8E8EBC3F9B9D88024EC1F359D7E413518ECC2E22D4DEB2DF54B9F1DCD25612CD02CD7F8DC1F
      #国密2私钥(B)
      sm2PrivateKeyB: bb4fa3835d570abba3dfb663a614d855fe50b6017b07f55100804f3cecb9d1de
      #国密2公钥(B)
      sm2PublicKeyB: 040a2e021a7547201ecc00ee3fb52e15877b08ca39a954bcfd800e0b11f7bc35b9c237a3c6314c1cba97171956d7f67480203e9f89d65985a5879c3b235caef986
      #国密4秘钥(秘钥明文通过国密2加密后字符串)
      sm4Key: 04dc4b315ce1755b5c2dbd0011f1d59bf879e79cc7dae042587138932173ee752412333a05be1f6f48bdda1c50e17216f37e5a2f2948b0b43f462a1dc49f586c752f25d9358765a8f60c59b73e3e699926fd6bd9c224c9c420e91cba68a71dba714ac07ca7386017924c50c26e7936f2c8
  token:
  #默认true-开启redis模式。
  #redis: false
  # Elasticsearch
  es:
    user-name:
    password:
    #password: ENC(ksa2E3KS4Mk4fxm/DSxfZG1qfpBm2gbq)
    uris:
      - 127.0.0.1:19200
  # 全局限流
  rateLimit:
    enable: true
    # 每1秒内
    timeout: 1000
    # 总限制100个请求
    limit: 100

  #SQL拦截器：多租户配置和数据权限配置
  sqlinterceptor:
    #配置不进行控制的mapper方法，即不进行多租户也不进行数据权限控制
    ignoreSqls:   #需要配置mapper的全路径如：com.yirong.csp.user.mapper.SysUserMapper.findList
    #- com.js.auth.dao.mapper.UserMapper.findByUsername
    #多租户配置
    tenantEnable: true #是否开启多租户
    #tenantIdColumn:    #多租户where 条件 字段
    tenantIgnoreTables:   #配置不进行多租户隔离的表名
    #数据权限配置
    dataAuthEnable: true #是否开启数据权限控制
    dataAuthIdColumn: current_merchant_id    #数据权限where 条件 字段
    dataAuthIgnoreTables:  #配置不进行数据权限控制的表名
    #- mall_region
#  微信小程序配置 appid /appsecret
wx:
  minimode: 0 #//小程序模式：0-miniapp 独立部署模式 ;1-open 开放平台模式 。默认 0-miniapp
  miniapp:
    configs:
      - appid:
        secret:
        token:
        aesKey:
        msgDataFormat: JSON

# Swagger界面内容配置
# Swagger界面内容配置
swagger:
  enabled: false
  title: 统一开发平台接口文档
  description: JSDP Api Documentation
  version: 1.9.0
  termsOfServiceUrl:
  contact:
    name: JS
    url:
    email:
  packages:
    - com.js.hszpt

# Mybatis-plus
mybatis-plus:
  # 放在resource目录 classpath:/mapper/*Mapper.xml
  #mapper-locations: classpath:/mapper/*Mapper.xml
  mapper-locations: classpath*:mapper/**/**.xml
  global-config:
    # 主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 2
    # 字段策略 0:"忽略判断",1:"非 NULL 判断",2:"非空判断"
    field-strategy: 2
    # 驼峰下划线转换
    db-column-underline: true
    # 刷新mapper 调试神器
    refresh-mapper: true
    # SQL 解析缓存，开启后多租户 @SqlParser 注解生效
    sql-parser-cache: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 配置JdbcTypeForNull, oracle数据库必须配置
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志
logging:
  #指定日志文件
  config: classpath:jsdp-logback-spring.xml
  level:
    root: INFO
    com.zaxxer.hikari: INFO
    com.baomidou.dynamic: INFO
    org.springframework.jdbc: INFO
  file:
    #单个日志文件最大值
    max-size: 200MB
    #日志文件最多保留的天数
    max-history: 60
    #日志文件总体的最大值
    total-size-cap: 10GB
    #日志文件路径
    path: ./logs

crewBaseInfo:
  # 导出最大数量
  exportCount: 20

job:
  # 消息推送任务
  message:
    enable: false
  # 定时任务
    cron: "0/10 * * * * ?"
    # token获取接口
    tokenUrl: http://198.10.51.2:8093/api/api/mobile/token/getSysAccessTicket
    # 消息推送接口
    pushUrl: http://198.10.51.2:8093/api/api/mobile/oauth/openApi/pushMessage
    # 加密密钥
    encryptionKey: 5d3b282609644b4f8992b31a2e92f0f3
