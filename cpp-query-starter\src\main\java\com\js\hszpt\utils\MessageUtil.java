package com.js.hszpt.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.js.hszpt.vo.MessageRequest;
import com.js.hszpt.vo.UserPushResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * 海事通消息工具类
 * @version 1.0
 * @since 2025-07-23
 */
@Slf4j
public class MessageUtil {

    private static final String CONTENT_TYPE = "application/json;charset=utf-8";
    
    /**
     * 获取系统访问票据
     * @param url 请求URL
     * @param encryptedParams 加密后的参数
     * @return TokenResponse 包含响应信息的对象
     */
    public static String getSysAccessTicket(String url,String encryptedParams) {
        log.info("开始获取系统访问票据，加密参数长度：{}", encryptedParams != null ? encryptedParams.length() : 0);
        
        // 构建JSON请求体
        JSONObject json = new JSONObject();
        json.set("params", encryptedParams);
        String jsonBody = JSONUtil.toJsonStr(json);
        
        log.info("请求体：{}", jsonBody);

        // 发送POST请求
        try (HttpResponse response = HttpUtil.createPost(url)
                .header("Content-Type", CONTENT_TYPE)
                .body(jsonBody)
                .timeout(10000) // 设置超时时间10秒
                .execute()){
            // 解析响应
            String body = response.body();
            log.info("响应状态码：{}，响应体：{}", response.getStatus(), body);

            if (!response.isOk()) {
                return null;
            }

            JSONObject jsonResponse = JSONUtil.parseObj(body);

            if (!hasValid(jsonResponse)) {
                log.warn("系统访问票据获取失败，错误码：{}，错误信息：{}",
                        jsonResponse.getInt("code", -1),
                        jsonResponse.getStr("message", ""));
                return null;
            }
            
            String data = jsonResponse.getStr("data", "");
            log.info("系统访问票据获取成功，票据长度：{}", data.length());
            return data;
            
        } catch (Exception e) {
            log.error("获取系统访问票据时发生异常：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 推送消息到海事通系统
     * @param ticket 访问票据
     * @param messageRequest 消息推送请求参数
     * @param encryptionKey 加密密钥
     * @return PushMessageResponse 推送结果
     */
    public static List<UserPushResult> pushMessage(String url, String ticket, MessageRequest messageRequest, String encryptionKey) {
        log.info("开始推送消息，目标用户数：{}，消息类型：{}",
            (messageRequest.getCardList() != null ? messageRequest.getCardList().size() : 0) +
            (messageRequest.getOrgCodeList() != null ? messageRequest.getOrgCodeList().size() : 0),
            messageRequest.getType());

        try {
            // 构建请求参数

            // 加密参数（这里需要根据实际的加密工具类进行加密）
            String paramsJson = JSONUtil.toJsonStr(messageRequest);
            log.debug("推送参数JSON：{}", paramsJson);

            // 注意：这里需要使用实际的AES加密工具类
            // String encryptedParams = AESEncryptionWithEncodedKey.encrypt(paramsJson, encryptionKey);
            String encryptedParams = encryptParams(paramsJson, encryptionKey);

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.set("params", encryptedParams);
            String jsonBody = JSONUtil.toJsonStr(requestBody);

            log.debug("推送请求体：{}", jsonBody);

            // 发送POST请求
            try (HttpResponse response = HttpUtil.createPost(url)
                    .header("Content-Type", CONTENT_TYPE)
                    .header("ticket", ticket)
                    .body(jsonBody)
                    .timeout(10000) // 设置超时时间10秒
                    .execute()) {
                // 解析响应
                String body = response.body();

                if (!response.isOk()) {
                    log.error("推送消息时发生异常，状态码：{}，响应内容：{}", response.getStatus(), body);
                    return null;
                }

                JSONObject jsonResponse = JSONUtil.parseObj(body);

                // 解析推送结果
                if (!hasValid(jsonResponse)) {
                    log.warn("消息推送失败，错误码：{}，错误信息：{}",
                        jsonResponse.getInt("code", -1),
                        jsonResponse.getStr("message", ""));
                    return null;
                }
                JSONArray jsonArray = jsonResponse.getJSONArray("data");
                log.info("推送结果：{}", jsonArray);
                if (jsonArray == null) {
                    return null;
                }
                // 解析用户推送结果
                List<UserPushResult> userResults = jsonArray.toList(UserPushResult.class);
                return userResults;
            }
        } catch (Exception e) {
            log.error("推送消息时发生异常：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 参数加密方法（占位符，需要根据实际加密工具类实现）
     * @param params 待加密的参数
     * @param encryptionKey 加密密钥
     * @return 加密后的字符串
     */
    private static String encryptParams(String params, String encryptionKey) {
        // TODO: 这里需要根据实际的加密工具类进行实现
        // 可以使用项目中的SM4加密工具或AES加密工具
        log.warn("参数加密方法未实现，返回原始参数。请根据实际加密要求实现此方法。");
        return params; // 临时返回原始参数，实际使用时需要加密
    }

    /**
     * 判断是否为有效的响应
     * @return true表示成功
     */
    public static boolean hasValid(JSONObject jsonResponse) {
        return jsonResponse.getBool("success", false)
                && jsonResponse.getInt("code", -1) == 200
                && jsonResponse.getObj("data",null) != null;
    }

}
