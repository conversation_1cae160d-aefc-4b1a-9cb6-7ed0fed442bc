package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCppPositionPromotionReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 晋升要求表DAO接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCppPositionPromotionReqDao extends BaseMapper<DwdzCppPositionPromotionReq> {

    /**
     * 根据子分类ID获取晋升要求
     * @deprecated 已废弃，建议使用getRequirementsByIds方法
     */
    @Deprecated
    DwdzCppPositionPromotionReq getRequirementBySubcategoryId(@Param("subcategoryId") String subcategoryId);

    /**
     * 根据目标职位名称获取晋升要求
     * @deprecated 已废弃，建议使用getRequirementsByIds方法
     */
    @Deprecated
    DwdzCppPositionPromotionReq getRequirementByTargetPosition(@Param("targetPosition") String targetPosition);
    
    /**
     * 根据子分类名称获取晋升要求
     * @deprecated 已废弃，建议使用getRequirementsByIds方法
     */
    @Deprecated
    DwdzCppPositionPromotionReq getRequirementBySubcategoryName(@Param("subcategoryName") String subcategoryName);

    /**
     * 根据当前职务ID和目标职务ID查询晋升要求列表（新版本）
     * @param dqSubcategoryId 当前证书职务分类id
     * @param mbSubcategoryId 晋升目标证书职务分类id
     * @return 晋升要求列表，按条件类型和排序字段排序
     */
    List<DwdzCppPositionPromotionReq> getRequirementsByIds(
        @Param("dqSubcategoryId") String dqSubcategoryId, 
        @Param("mbSubcategoryId") String mbSubcategoryId
    );

    /**
     * 根据当前职务ID查询所有相关的晋升要求（作为源职务或目标职务）
     * @param subcategoryId 职务分类ID
     * @return 晋升要求列表
     */
    List<DwdzCppPositionPromotionReq> getRequirementsBySubcategoryId(@Param("subcategoryId") String subcategoryId);
} 