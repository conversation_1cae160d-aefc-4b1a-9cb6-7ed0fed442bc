<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CrewEmpShipServiceMapper">

    <!-- 根据任职关系ID查询船舶服务信息 -->
    <select id="selectByEmploymentId" resultType="com.js.hszpt.entity.CrewEmpShipService">
        SELECT 
            service_id,
            employment_id,
            ship_name,
            position,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_emp_ship_service
        WHERE employment_id = #{employmentId}
        ORDER BY rec_create_date DESC
    </select>

    <!-- 根据船员ID查询所有船舶服务信息 -->
    <select id="selectByCrewId" resultType="com.js.hszpt.entity.CrewEmpShipService">
        SELECT 
            s.service_id,
            s.employment_id,
            s.ship_name,
            s.position,
            s.rec_create_date,
            s.rec_modify_date
        FROM dwdz_crew_emp_ship_service s
        INNER JOIN dwdz_crew_employment e ON s.employment_id = e.employment_id
        WHERE e.crew_id = #{crewId}
        ORDER BY s.rec_create_date DESC
    </select>

    <!-- 根据任职关系ID列表查询船舶服务信息 -->
    <select id="selectByEmploymentIds" resultType="com.js.hszpt.entity.CrewEmpShipService">
        SELECT 
            service_id,
            employment_id,
            ship_name,
            position,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_emp_ship_service
        WHERE employment_id IN
        <foreach collection="employmentIds" item="employmentId" open="(" separator="," close=")">
            #{employmentId}
        </foreach>
        ORDER BY rec_create_date DESC
    </select>

</mapper> 