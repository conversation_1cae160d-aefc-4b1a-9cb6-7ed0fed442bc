<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCppPositionCategoryDao">

    <!-- 职务分类表结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCppPositionCategory">
        <id column="category_id" property="categoryId" />
        <result column="position_code" property="positionCode" />
        <result column="position_name" property="positionName" />
        <result column="position_level" property="positionLevel" />
        <result column="total_count" property="totalCount" />
        <result column="position_description" property="positionDescription" />
        <result column="rec_create_date" property="recCreateDate" />
        <result column="rec_modify_date" property="recModifyDate" />
    </resultMap>

    <!-- 根据职位编码获取职务分类信息 -->
    <select id="getCategoryByPositionCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
            category_id,
            position_code,
            position_name,
            position_level,
            total_count,
            position_description,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_category 
        WHERE position_code = #{positionCode}
        LIMIT 1
    </select>

    <!-- 获取所有职务的人数统计映射 -->
    <select id="getPositionCountsMap" resultType="java.util.Map">
        SELECT 
            position_code,
            total_count
        FROM dwdz_crew_position_category 
        ORDER BY position_code
    </select>

    <!-- 根据职位编码获取单个职务人数 -->
    <select id="getPositionCountByCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT total_count
        FROM dwdz_crew_position_category 
        WHERE position_code = #{positionCode}
        LIMIT 1
    </select>

    <!-- 获取包含子分类信息的职务分类列表 -->
    <select id="getCategoriesWithSubcategories" resultMap="BaseResultMap">
        SELECT 
            category_id,
            position_code,
            position_name,
            position_level,
            total_count,
            position_description,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_category 
        ORDER BY position_level, position_name
    </select>

    <!-- 根据职务级别获取职务分类列表 -->
    <select id="getCategoriesByLevel" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
            category_id,
            position_code,
            position_name,
            position_level,
            total_count,
            position_description,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_category 
        WHERE position_level = #{positionLevel}
        ORDER BY position_name
    </select>

</mapper> 