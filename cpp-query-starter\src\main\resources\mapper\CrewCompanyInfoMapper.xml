<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CrewCompanyInfoMapper">

    <!-- 根据船员ID查询关联的公司信息 -->
    <select id="selectCompaniesByCrewId" resultType="com.js.hszpt.entity.CrewCompanyInfo">
        SELECT DISTINCT 
            c.company_id,
            c.company_name,
            c.company_short_name,
            e.company_type,
            c.address,
            c.contact,
            c.rec_create_date,
            c.rec_modify_date
        FROM dwdz_crew_company_info c
        INNER JOIN dwdz_crew_employment e ON c.company_id = e.company_id
        WHERE e.crew_id = #{crewId}
        ORDER BY c.company_name
    </select>

    <!-- 根据船员ID和公司类型查询关联的公司信息 -->
    <select id="selectCompaniesByCrewIdAndTypes" resultType="com.js.hszpt.entity.CrewCompanyInfo">
        SELECT DISTINCT 
            c.company_id,
            c.company_name,
            c.company_short_name,
            e.company_type
            c.address,
            c.contact,
            c.rec_create_date,
            c.rec_modify_date
        FROM dwdz_crew_company_info c
        INNER JOIN dwdz_crew_employment e ON c.company_id = e.company_id
        WHERE e.crew_id = #{crewId}
        <if test="companyTypes != null and companyTypes.size() > 0">
            AND e.company_type IN
            <foreach collection="companyTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        ORDER BY c.company_name
    </select>

    <!-- 根据公司ID列表查询公司信息 -->
    <select id="selectByCompanyIds" resultType="com.js.hszpt.entity.CrewCompanyInfo">
        SELECT 
            company_id,
            company_name,
            company_short_name,
            address,
            contact,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_company_info
        WHERE company_id IN
        <foreach collection="companyIds" item="companyId" open="(" separator="," close=")">
            #{companyId}
        </foreach>
        ORDER BY company_name
    </select>

</mapper> 