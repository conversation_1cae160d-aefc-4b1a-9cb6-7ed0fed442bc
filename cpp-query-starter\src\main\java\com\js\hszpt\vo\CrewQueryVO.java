package com.js.hszpt.vo;

import com.js.core.common.vo.PageVo;
import lombok.Data;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;

@Data
public class CrewQueryVO extends PageVo {
    private String keyword; // 关键字（身份证/姓名/证书号）
    private String seafarerName; // 姓名
    private String idCard; // 身份证
    private String certificateType; // 证书类型
    private String certificateNumber; // 证书号码
    private String status; // 船员状态（all/onboard/onleave/pending）
    private String featureTags; // 特征标签
    private String port; // 港口名称
    private String shipName; // 中文船名、英文船名
    private String shipId; // 船舶识别号、imo、mmsi
    private String position; // 当前职务
    private String orgCode; // 登录用户机构（后端默认注入）
    private Integer offset; // 分页偏移量

    // 新增：标签组查询条件
    private List<TagGroupCondition> tagGroupConditions;

    /**
     * 标签组查询条件
     */
    @Data
    public static class TagGroupCondition {
        private String tagCode; // 标签编码
        private String filterCondition; // 过滤条件
        private String tagValueType; // 标签值数据类型
        private String tagName; // 标签名称
        private String groupId; // 标签组ID
        private String groupName; // 标签组名称

        /**
         * 获取分割后的过滤条件值列表
         * 用于MyBatis的foreach循环
         */
        public List<String> getFilterConditionValues() {
            if (filterCondition == null || filterCondition.trim().isEmpty()) {
                return new ArrayList<>();
            }

            // 检查是否包含顿号分隔符
            if (filterCondition.contains("、")) {
                return new ArrayList<>(Arrays.asList(filterCondition.split("、")));
            } else {
                // 单值情况，返回包含单个元素的列表
                List<String> result = new ArrayList<>();
                result.add(filterCondition);
                return result;
            }
        }

        /**
         * 判断是否为多值条件
         */
        public boolean isMultiValue() {
            return filterCondition != null && filterCondition.contains("、");
        }
    }
}