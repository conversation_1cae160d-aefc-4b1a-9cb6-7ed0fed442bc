package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 船员照片表
 */
@Data
@TableName("res_crew_person_photo")
public class ResCrewPersonPhoto {
    /**
     * 流水号
     */
    @TableId("serial_no")
    private String serialNo;

    /**
     * 身份证号码
     */
    @TableField("idcard_no")
    private String idcardNo;

    /**
     * 照片信息
     */
    @TableField("photo_info")
    private byte[] photoInfo;
} 