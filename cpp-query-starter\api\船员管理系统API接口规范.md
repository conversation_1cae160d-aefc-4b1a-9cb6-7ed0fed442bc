# 船员管理系统 - 后端API接口规范文档

## 文档信息
- **版本**: v1.0
- **创建日期**: 2024-01-01
- **最后更新**: 2024-01-01
- **维护人员**: 开发团队

## 概述
本文档定义了船员管理系统前端核心组件所需的后端API接口规范：
- index.vue - 首页组件
- CareerStructure.vue - 船员职业结构组件
- CertificatePath.vue - 证书路径组件  
- SeafarerGroups.vue - 船员群体分类组件

**接口总数**: 15个接口，涵盖船员数据统计、职业结构管理、证书路径配置、群体分类等核心功能。

## 基础信息

### 接口基础URL
```
开发环境: http://localhost:8080/api
测试环境: https://test-api.example.com/api
生产环境: https://api.example.com/api
```

### 通用请求头
```http
Content-Type: application/json
Authorization: Bearer {token}
Accept: application/json
```

### 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### HTTP状态码说明
- `200` - 请求成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 禁止访问
- `404` - 资源不存在
- `500` - 服务器内部错误

---

## 1. 首页相关接口 (index.vue)

### 1.1 获取船员总数

**接口描述**: 获取全国船员总数统计数据

```http
GET /api/index/seafarer-count
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalCount": 2080000,
    "lastUpdateTime": "2024-01-01T12:00:00Z"
  },
  "message": "获取船员总数成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**字段说明**:
- `totalCount` (integer): 船员总数
- `lastUpdateTime` (string): 数据最后更新时间

---

## 2. 船员职业结构相关接口 (CareerStructure.vue)

### 2.1 获取职业路径数据

**接口描述**: 获取所有职业晋升路径配置数据

```http
GET /api/career/paths
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "deck1": {
      "name": "甲板部通道1 (值班水手直升)",
      "path": ["able-seaman", "senior-seaman", "third-officer", "second-officer", "chief-officer", "captain"],
      "color": "#2980b9",
      "description": "甲板部传统晋升通道"
    },
    "deck2": {
      "name": "甲板部通道2 (高级水手)",
      "path": ["able-seaman", "third-officer", "second-officer", "chief-officer", "captain"],
      "color": "#27ae60",
      "description": "甲板部快速晋升通道"
    },
    "engine1": {
      "name": "轮机部通道1 (值班机工直升)",
      "path": ["able-engineer", "senior-engineer", "third-engineer", "second-engineer", "chief-engineer-officer", "chief-engineer"],
      "color": "#e74c3c",
      "description": "轮机部传统晋升通道"
    },
    "engine2": {
      "name": "轮机部通道2 (高级机工)",
      "path": ["able-engineer", "third-engineer", "second-engineer", "chief-engineer-officer", "chief-engineer"],
      "color": "#f39c12",
      "description": "轮机部快速晋升通道"
    },
    "electrical": {
      "name": "电子电气通道",
      "path": ["electrical-technician", "electrical-officer"],
      "color": "#9b59b6",
      "description": "电子电气专业通道"
    },
    "radio": {
      "name": "无线电操作通道",
      "path": ["limited-operator", "general-operator", "radio-operator-2", "radio-operator-1"],
      "color": "#1abc9c",
      "description": "无线电操作员晋升通道"
    }
  },
  "message": "获取职业路径数据成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.2 获取管理关系数据

**接口描述**: 获取职位间的管理关系配置

```http
GET /api/career/management-relations
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "deck_management": {
      "name": "甲板部管理关系",
      "color": "#ffd700",
      "connections": [
        {"from": "captain", "to": "chief-officer"},
        {"from": "chief-officer", "to": "second-officer"},
        {"from": "second-officer", "to": "third-officer"}
      ]
    },
    "engine_management": {
      "name": "轮机部管理关系", 
      "color": "#ff6b6b",
      "connections": [
        {"from": "chief-engineer", "to": "chief-engineer-officer"},
        {"from": "chief-engineer-officer", "to": "second-engineer"},
        {"from": "second-engineer", "to": "third-engineer"}
      ]
    }
  },
  "message": "获取管理关系数据成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.3 获取职位详情

**接口描述**: 获取指定职位的详细信息和子分类

```http
GET /api/career/position/{positionId}
```

**路径参数**:
- `positionId` (string, required): 职位ID

**可选职位ID列表**:
- `captain` - 船长
- `chief-officer` - 大副
- `second-officer` - 二副
- `third-officer` - 三副
- `chief-engineer` - 轮机长
- `chief-engineer-officer` - 大管轮
- `second-engineer` - 二管轮
- `third-engineer` - 三管轮
- `electrical-officer` - 电子电气员
- `radio-operator-1` - 一级无线电电子员
- `radio-operator-2` - 二级无线电电子员
- `general-operator` - 通用操作员
- `limited-operator` - 限用操作员
- `able-seaman` - 值班水手
- `senior-seaman` - 高级值班水手
- `able-engineer` - 值班机工
- `senior-engineer` - 高级值班机工
- `electrical-technician` - 电子技工

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "captain",
    "name": "船长",
    "level": "management",
    "description": "船舶最高指挥官，负责船舶的安全航行和全面管理",
    "subcategories": [
      {
        "name": "无限航区船长",
        "count": 8500,
        "description": "可在全球任何海域担任船长职务"
      },
      {
        "name": "沿海航区船长",
        "count": 6200,
        "description": "限制在沿海航区担任船长职务"
      },
      {
        "name": "近洋航区船长",
        "count": 4800,
        "description": "限制在近洋航区担任船长职务"
      },
      {
        "name": "客船船长",
        "count": 2100,
        "description": "专门担任客船船长职务"
      }
    ],
    "totalCount": 21600
  },
  "message": "获取职位详情成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.4 获取晋升关系数据

**接口描述**: 获取指定子分类的晋升来源和目标关系

```http
GET /api/career/promotion-relations/{subcategoryName}
```

**路径参数**:
- `subcategoryName` (string, required): 子分类名称，需要URL编码

**响应示例**:
```json
{
  "success": true,
  "data": {
    "lower": [
      {
        "name": "无限航区大副",
        "type": "正常晋升",
        "description": "通过正常晋升渠道"
      },
      {
        "name": "沿海航区大副",
        "type": "跨航区晋升",
        "description": "需要额外培训和考试"
      }
    ],
    "upper": [
      {
        "name": "高级船长",
        "type": "资历晋升",
        "description": "基于丰富的海上服务资历"
      },
      {
        "name": "船队船长",
        "type": "管理晋升",
        "description": "晋升至管理岗位"
      }
    ]
  },
  "message": "获取晋升关系数据成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.5 获取晋升要求详情

**接口描述**: 获取晋升到指定职位的详细要求

```http
GET /api/career/promotion-requirements/{targetPosition}
```

**路径参数**:
- `targetPosition` (string, required): 目标职位名称，需要URL编码

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overview": [
      "具备相应的适任证书",
      "满足海上服务资历要求",
      "通过海事局适任考试",
      "身体健康，符合船员健康标准"
    ],
    "age": [
      "年满18周岁",
      "不超过65周岁",
      "初次申请年龄不超过60周岁"
    ],
    "health": [
      "持有有效的海员健康证明",
      "视力矫正后达到0.8以上",
      "听力正常，无色盲色弱",
      "无影响安全履职的疾病"
    ],
    "experience": [
      "担任大副职务满18个月",
      "其中至少12个月在相应航区",
      "累计海上服务资历不少于36个月",
      "近5年内海上服务资历不少于12个月"
    ],
    "training": [
      "完成船长岗位适任培训",
      "完成船舶保安员培训",
      "完成高级消防培训",
      "完成精通救生艇筏和救助艇培训"
    ],
    "certificates": [
      "船舶操纵模拟器培训合格证",
      "雷达观测与标绘培训合格证",
      "自动雷达标绘仪培训合格证",
      "全球海上遇险与安全系统培训合格证"
    ],
    "notes": [
      "需通过海事局理论和实操考试",
      "考试合格后颁发适任证书",
      "证书有效期为5年",
      "到期前需完成知识更新培训"
    ]
  },
  "message": "获取晋升要求详情成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.6 获取职务人数统计

**接口描述**: 获取所有职务的人数统计数据

```http
GET /api/career/position-counts
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "captain": 150,
    "chief-engineer": 180,
    "chief-officer": 320,
    "chief-engineer-officer": 280,
    "second-officer": 450,
    "second-engineer": 380,
    "third-officer": 560,
    "third-engineer": 520,
    "electrical-officer": 230,
    "radio-operator-1": 120,
    "radio-operator-2": 180,
    "general-operator": 340,
    "limited-operator": 420,
    "able-seaman": 1200,
    "senior-seaman": 800,
    "able-engineer": 980,
    "senior-engineer": 650,
    "electrical-technician": 450
  },
  "message": "获取职务人数统计成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**字段说明**:
- 返回对象的key为职务ID，value为该职务的人数
- 职务ID对应关系：
  - `captain` - 船长
  - `chief-engineer` - 轮机长
  - `chief-officer` - 大副
  - `chief-engineer-officer` - 大管轮
  - `second-officer` - 二副
  - `second-engineer` - 二管轮
  - `third-officer` - 三副
  - `third-engineer` - 三管轮
  - `electrical-officer` - 电子电气员
  - `radio-operator-1` - 一级无线电电子员
  - `radio-operator-2` - 二级无线电电子员
  - `general-operator` - 通用操作员
  - `limited-operator` - 限用操作员
  - `able-seaman` - 值班水手
  - `senior-seaman` - 高级值班水手
  - `able-engineer` - 值班机工
  - `senior-engineer` - 高级值班机工
  - `electrical-technician` - 电子技工

### 2.7 获取单个职务人数

**接口描述**: 获取指定职务的人数统计

```http
GET /api/career/position-count/{positionId}
```

**路径参数**:
- `positionId` (string, required): 职务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "positionId": "captain",
    "count": 150,
    "lastUpdateTime": "2024-01-01T12:00:00Z"
  },
  "message": "获取职务人数成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "POSITION_NOT_FOUND",
    "message": "职务不存在",
    "details": "职务ID captain-invalid 未找到"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.8 获取路径选择器选项

**接口描述**: 获取职业结构图中路径选择器的所有可选项

```http
GET /api/career/path-selector-options
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "deck1",
      "name": "甲板部通道1 (值班水手直升)",
      "description": "甲板部传统晋升通道，从值班水手直接晋升",
      "category": "deck",
      "enabled": true,
      "sortOrder": 1
    },
    {
      "id": "deck2",
      "name": "甲板部通道2 (高级水手)",
      "description": "甲板部快速晋升通道，通过高级水手晋升",
      "category": "deck",
      "enabled": true,
      "sortOrder": 2
    },
    {
      "id": "engine1",
      "name": "轮机部通道1 (值班机工直升)",
      "description": "轮机部传统晋升通道，从值班机工直接晋升",
      "category": "engine",
      "enabled": true,
      "sortOrder": 3
    },
    {
      "id": "engine2",
      "name": "轮机部通道2 (高级机工)",
      "description": "轮机部快速晋升通道，通过高级机工晋升",
      "category": "engine",
      "enabled": true,
      "sortOrder": 4
    },
    {
      "id": "electrical",
      "name": "电子电气通道",
      "description": "电子电气专业晋升通道",
      "category": "electrical",
      "enabled": true,
      "sortOrder": 5
    },
    {
      "id": "radio",
      "name": "无线电操作通道",
      "description": "无线电操作员晋升通道",
      "category": "radio",
      "enabled": true,
      "sortOrder": 6
    },
    {
      "id": "management",
      "name": "管理关系",
      "description": "显示职务间的管理关系",
      "category": "management",
      "enabled": true,
      "sortOrder": 7
    }
  ],
  "message": "获取路径选择器选项成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**字段说明**:
- `id` (string): 选项唯一标识符
- `name` (string): 选项显示名称
- `description` (string): 选项详细描述
- `category` (string): 选项分类 (deck/engine/electrical/radio/management)
- `enabled` (boolean): 是否启用该选项
- `sortOrder` (integer): 排序顺序，数值越小越靠前

---

## 3. 证书路径相关接口 (CertificatePath.vue)

### 3.1 获取证书路径数据

**接口描述**: 获取指定证书类型的完整路径树结构

```http
GET /api/certificate/path/{certificateType}
```

**路径参数**:
- `certificateType` (string, required): 证书类型，需要URL编码

**支持的证书类型**:
- `海船船员适任证书`
- `内河船员适任证书`
- `船员培训合格证`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "seamans-certificate",
    "name": "海船船员适任证书",
    "type": "certificate",
    "description": "海船船员适任证书体系",
    "children": [
      {
        "id": "student",
        "name": "学生",
        "type": "category",
        "labelText": "[考试发证]",
        "description": "在校学生类别",
        "children": [
          {
            "id": "maritime-student",
            "name": "全日制航海类教育学生",
            "type": "subcategory",
            "labelText": "[考试发证]",
            "description": "全日制航海类专业学生",
            "children": [
              {
                "id": "second-officer-student",
                "name": "二副",
                "type": "position",
                "labelText": "[考试发证]",
                "description": "二副职务适任证书",
                "requirements": "完成相应的理论学习和实习"
              }
            ]
          }
        ]
      }
    ]
  },
  "message": "获取证书路径数据成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 3.2 搜索证书节点

**接口描述**: 根据关键词搜索证书节点

```http
GET /api/certificate/search?keyword={keyword}&limit={limit}
```

**查询参数**:
- `keyword` (string, required): 搜索关键词
- `limit` (integer, optional): 返回结果数量限制，默认20，最大100

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "coastal-second-officer-under-500",
      "name": "沿海航区未满500总吨二副",
      "path": "海船船员适任证书 > 学生 > 全日制航海类教育学生 > 二副 > 沿海航区未满500总吨二副",
      "type": "position",
      "description": "沿海航区小型船舶二副适任证书"
    }
  ],
  "total": 1,
  "message": "搜索证书节点成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 3.3 获取证书晋升要求详情

**接口描述**: 获取指定证书节点的晋升要求详情

```http
POST /api/certificate/promotion-requirements
```

**请求体**:
```json
{
  "nodeData": {
    "id": "second-officer",
    "name": "二副",
    "type": "position",
    "description": "二副职务适任证书"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overview": [
      "持有相应的适任证书",
      "完成规定的海上服务资历",
      "通过海事局适任考试",
      "满足健康和年龄要求"
    ],
    "age": [
      "年满18周岁",
      "不超过60周岁（初次申请）",
      "不超过65周岁（证书换发）"
    ],
    "health": [
      "持有有效的海员健康证明",
      "视力矫正后不低于0.5",
      "听力正常",
      "无色盲色弱"
    ],
    "experience": [
      "担任三副职务满12个月",
      "其中至少6个月在相应航区和船舶类型",
      "累计海上服务资历不少于18个月"
    ],
    "training": [
      "完成二副岗位适任培训",
      "完成雷达观测与标绘培训",
      "完成自动雷达标绘仪培训",
      "完成船舶操纵培训"
    ],
    "certificate": [
      "雷达观测与标绘培训合格证",
      "自动雷达标绘仪培训合格证",
      "船舶操纵模拟器培训合格证",
      "基本安全培训合格证"
    ],
    "notes": [
      "需通过海事局理论和实操考试",
      "理论考试包括航海学、船舶操纵等科目",
      "实操考试包括船舶操纵、雷达操作等项目",
      "考试合格后颁发适任证书"
    ]
  },
  "message": "获取证书晋升要求成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

---

## 4. 船员群体分类相关接口 (SeafarerGroups.vue)

### 4.1 获取船员数据

**接口描述**: 获取船员群体分类的完整树形数据

```http
GET /api/seafarer/data
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "name": "船员",
    "value": 2080000,
    "description": "全国船员总数统计",
    "children": [
      {
        "name": "现役船员",
        "value": 1850000,
        "description": "目前在职的船员",
        "children": [
          {
            "name": "甲板部船员",
            "value": 920000,
            "certifiedCount": 850000,
            "demandCount": 950000,
            "onboardCount": 780000,
            "description": "负责船舶驾驶和甲板作业的船员",
            "children": [
              {
                "name": "船长",
                "value": 45000,
                "certifiedCount": 42000,
                "demandCount": 48000,
                "onboardCount": 38000,
                "description": "船舶最高指挥官"
              }
            ]
          }
        ]
      }
    ]
  },
  "message": "获取船员数据成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 4.2 获取颜色配置

**接口描述**: 获取船员群体和分类的颜色配置

```http
GET /api/seafarer/colors
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "groupColors": {
      "现役船员": "#1E5F8B",
      "退役船员": "#2C3E50",
      "甲板部船员": "#2980B9",
      "轮机部船员": "#E74C3C"
    },
    "blockColors": {
      "船长": "#FF6B6B",
      "大副": "#4ECDC4",
      "二副": "#45B7D1",
      "三副": "#96CEB4"
    }
  },
  "message": "获取颜色配置成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 4.3 获取图表配置

**接口描述**: 获取图表显示相关的配置参数

```http
GET /api/seafarer/chart-config
```

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "defaultGroupIndex": 0,
    "canvas": {
      "defaultRadius": 120,
      "defaultInnerRadius": 50
    },
    "animation": {
      "rotationDuration": 800,
      "minimumDragDistance": 0.3,
      "particleCount": 30
    }
  },
  "message": "获取图表配置成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

---

## 5. 接口汇总清单

| 序号 | 接口路径 | 方法 | 功能描述 | 对应组件 | 优先级 |
|------|----------|------|----------|----------|--------|
| 1 | `/api/index/seafarer-count` | GET | 获取船员总数 | index | 高 |
| 2 | `/api/career/paths` | GET | 获取职业路径数据 | CareerStructure | 高 |
| 3 | `/api/career/management-relations` | GET | 获取管理关系数据 | CareerStructure | 高 |
| 4 | `/api/career/position/{positionId}` | GET | 获取职位详情 | CareerStructure | 高 |
| 5 | `/api/career/promotion-relations/{subcategoryName}` | GET | 获取晋升关系数据 | CareerStructure | 中 |
| 6 | `/api/career/promotion-requirements/{targetPosition}` | GET | 获取晋升要求详情 | CareerStructure | 中 |
| 7 | `/api/career/position-counts` | GET | 获取职务人数统计 | CareerStructure | 高 |
| 8 | `/api/career/position-count/{positionId}` | GET | 获取单个职务人数 | CareerStructure | 中 |
| 9 | `/api/career/path-selector-options` | GET | 获取路径选择器选项 | CareerStructure | 中 |
| 10 | `/api/certificate/path/{certificateType}` | GET | 获取证书路径数据 | CertificatePath | 高 |
| 11 | `/api/certificate/search` | GET | 搜索证书节点 | CertificatePath | 高 |
| 12 | `/api/certificate/promotion-requirements` | POST | 获取证书晋升要求 | CertificatePath | 中 |
| 13 | `/api/seafarer/data` | GET | 获取船员数据 | SeafarerGroups | 高 |
| 14 | `/api/seafarer/colors` | GET | 获取颜色配置 | SeafarerGroups | 低 |
| 15 | `/api/seafarer/chart-config` | GET | 获取图表配置 | SeafarerGroups | 低 |

---

## 6. 错误码定义

| 错误码 | HTTP状态码 | 错误描述 | 解决方案 |
|--------|------------|----------|----------|
| `INVALID_PARAMETER` | 400 | 请求参数无效 | 检查请求参数格式和必填项 |
| `RESOURCE_NOT_FOUND` | 404 | 资源不存在 | 确认请求的资源ID是否正确 |
| `UNAUTHORIZED` | 401 | 未授权访问 | 检查认证token是否有效 |
| `FORBIDDEN` | 403 | 禁止访问 | 检查用户权限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 | 联系技术支持 |
| `DATABASE_ERROR` | 500 | 数据库错误 | 联系技术支持 |

---

## 7. 涉及的数据库表

### 主要数据表
create table dwdz_cpp_position_hierarchy (
   hierarchy_id         VARCHAR(50)          not null default sys_guid(),
   parent_id            VARCHAR(50)          null,
   name                 VARCHAR(100)         not null,
   value                BIGINT               null,
   certified_count      BIGINT               null,
   demand_count         BIGINT               null,
   onboard_count        BIGINT               null,
   rec_create_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   rec_modify_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   constraint PK_DWDZ_CPP_POSITION_HIERARCHY primary key (hierarchy_id)
);

comment on table dwdz_cpp_position_hierarchy is
'船员群体层级关系表';

comment on column dwdz_cpp_position_hierarchy.hierarchy_id is
'唯一标识';

comment on column dwdz_cpp_position_hierarchy.parent_id is
'父级ID';

comment on column dwdz_cpp_position_hierarchy.name is
'船员类别名称';

comment on column dwdz_cpp_position_hierarchy.value is
'船员数量';

comment on column dwdz_cpp_position_hierarchy.certified_count is
'持证数量';

comment on column dwdz_cpp_position_hierarchy.demand_count is
'需求数量';

comment on column dwdz_cpp_position_hierarchy.onboard_count is
'在船数量';

comment on column dwdz_cpp_position_hierarchy.rec_create_date is
'记录创建日期';

comment on column dwdz_cpp_position_hierarchy.rec_modify_date is
'记录修改日期';

create table dwdz_cpp_position_subcategory (
   subcategory_id       VARCHAR(50)          not null default sys_guid(),
   category_id          VARCHAR(50)          not null,
   subcategory_name     VARCHAR(100)         not null,
   person_count         INT                  not null,
   rec_create_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   rec_modify_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   constraint PK_DWDZ_CPP_POSITION_SUBCATEGO primary key (subcategory_id)
);

comment on table dwdz_cpp_position_subcategory is
'职务子类表';

comment on column dwdz_cpp_position_subcategory.subcategory_id is
'子类唯一标识ID（主键）';

comment on column dwdz_cpp_position_subcategory.category_id is
'关联的职务分类ID（外键）';

comment on column dwdz_cpp_position_subcategory.subcategory_name is
'子类名称';

comment on column dwdz_cpp_position_subcategory.person_count is
'子类人数统计';

comment on column dwdz_cpp_position_subcategory.rec_create_date is
'记录创建日期';

comment on column dwdz_cpp_position_subcategory.rec_modify_date is
'记录修改日期';

alter table dwdz_cpp_position_subcategory
   add constraint fk_subcategory_category foreign key (category_id)
      references dwdz_cpp_position_category (category_id)
      on delete restrict on update restrict;


create table dwdz_cpp_position_category (
   category_id          VARCHAR(50)          not null default sys_guid(),
   position_code        VARCHAR(20)          not null,
   position_name        VARCHAR(100)         not null,
   position_level       VARCHAR(20)          not null,
   total_count          INT                  not null default '0',
   position_description TEXT                 null,
   rec_create_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   rec_modify_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   constraint PK_DWDZ_CPP_POSITION_CATEGORY primary key (category_id)
);

comment on table dwdz_cpp_position_category is
'职务分类表';

comment on column dwdz_cpp_position_category.category_id is
'分类唯一标识ID（主键）';

comment on column dwdz_cpp_position_category.position_code is
'标准化职位编码';

comment on column dwdz_cpp_position_category.position_name is
'职务名称';

comment on column dwdz_cpp_position_category.position_level is
'职务级别（management/operational/support）';

comment on column dwdz_cpp_position_category.total_count is
'该职务总人数';

comment on column dwdz_cpp_position_category.position_description is
'职务描述';

comment on column dwdz_cpp_position_category.rec_create_date is
'记录创建日期';

comment on column dwdz_cpp_position_category.rec_modify_date is
'记录修改日期';


create table dwdz_cpp_position_promotion_rel (
   promotion_relation_id VARCHAR(50)          not null default sys_guid(),
   from_subcategory_id  VARCHAR(50)          not null,
   to_subcategory_id    VARCHAR(50)          not null,
   relation_type        VARCHAR(50)          not null,
   person_count         INT                  not null,
   rec_create_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   rec_modify_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   constraint PK_DWDZ_CPP_POSITION_PROMOTION primary key (promotion_relation_id)
);

comment on table dwdz_cpp_position_promotion_rel is
'晋升关系表';

comment on column dwdz_cpp_position_promotion_rel.promotion_relation_id is
'晋升关系唯一标识ID（主键）';

comment on column dwdz_cpp_position_promotion_rel.from_subcategory_id is
'晋升来源子类ID（外键）';

comment on column dwdz_cpp_position_promotion_rel.to_subcategory_id is
'晋升目标子类ID（外键）';

comment on column dwdz_cpp_position_promotion_rel.relation_type is
'晋升类型（吨位提升/航区扩大/职务晋升）';

comment on column dwdz_cpp_position_promotion_rel.person_count is
'晋升人数统计';

comment on column dwdz_cpp_position_promotion_rel.rec_create_date is
'记录创建日期';

comment on column dwdz_cpp_position_promotion_rel.rec_modify_date is
'记录修改日期';

alter table dwdz_cpp_position_promotion_rel
   add constraint fk_from_subcategory foreign key (from_subcategory_id)
      references dwdz_cpp_position_subcategory (subcategory_id)
      on delete restrict on update restrict;

alter table dwdz_cpp_position_promotion_rel
   add constraint fk_to_subcategory foreign key (to_subcategory_id)
      references dwdz_cpp_position_subcategory (subcategory_id)
      on delete restrict on update restrict;


create table dwdz_cpp_position_promotion_req (
   requirement_id       VARCHAR(50)          not null default sys_guid(),
   subcategory_id       VARCHAR(50)          not null,
   overview             TEXT                 null,
   age_requirements     TEXT                 null,
   health_requirements  TEXT                 null,
   experience_requirements TEXT                 null,
   training_requirements TEXT                 null,
   certificate_requirements TEXT                 null,
   additional_notes     TEXT                 null,
   rec_create_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   rec_modify_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   constraint PK_DWDZ_CPP_POSITION_PROMOTION primary key (requirement_id)
);

comment on table dwdz_cpp_position_promotion_req is
'晋升要求表';

comment on column dwdz_cpp_position_promotion_req.requirement_id is
'晋升要求唯一标识ID（主键）';

comment on column dwdz_cpp_position_promotion_req.subcategory_id is
'关联的职务子类ID（外键）';

comment on column dwdz_cpp_position_promotion_req.overview is
'要求总览';

comment on column dwdz_cpp_position_promotion_req.age_requirements is
'年龄要求';

comment on column dwdz_cpp_position_promotion_req.health_requirements is
'健康要求';

comment on column dwdz_cpp_position_promotion_req.experience_requirements is
'资历要求';

comment on column dwdz_cpp_position_promotion_req.training_requirements is
'培训要求';

comment on column dwdz_cpp_position_promotion_req.certificate_requirements is
'证书要求';

comment on column dwdz_cpp_position_promotion_req.additional_notes is
'其他说明';

comment on column dwdz_cpp_position_promotion_req.rec_create_date is
'记录创建日期';

comment on column dwdz_cpp_position_promotion_req.rec_modify_date is
'记录修改日期';

alter table dwdz_cpp_position_promotion_req
   add constraint fk_requirement_subcategory foreign key (subcategory_id)
      references dwdz_cpp_position_subcategory (subcategory_id)
      on delete restrict on update restrict;


create table dwdz_cpp_position_promotion_cnl (
   promotion_channel_id VARCHAR(50)          not null default sys_guid(),
   channel_code         VARCHAR(20)          not null,
   channel_name         VARCHAR(100)         not null,
   display_color        VARCHAR(20)          not null,
   channel_type         VARCHAR(20)          not null,
   promotion_path_json  TEXT                 not null,
   sort_order           INT                  not null default '0',
   rec_create_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   rec_modify_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   constraint PK_DWDZ_CPP_POSITION_PROMOTION primary key (promotion_channel_id)
);

comment on table dwdz_cpp_position_promotion_cnl is
'晋升通道表';

comment on column dwdz_cpp_position_promotion_cnl.promotion_channel_id is
'晋升通道唯一标识ID（主键）';

comment on column dwdz_cpp_position_promotion_cnl.channel_code is
'晋升通道编码';

comment on column dwdz_cpp_position_promotion_cnl.channel_name is
'通道名称';

comment on column dwdz_cpp_position_promotion_cnl.display_color is
'通道显示颜色';

comment on column dwdz_cpp_position_promotion_cnl.channel_type is
'通道类型';

comment on column dwdz_cpp_position_promotion_cnl.promotion_path_json is
'晋升路径JSON数据';

comment on column dwdz_cpp_position_promotion_cnl.sort_order is
'通道显示排序序号';

comment on column dwdz_cpp_position_promotion_cnl.rec_create_date is
'记录创建日期';

comment on column dwdz_cpp_position_promotion_cnl.rec_modify_date is
'记录修改日期';

create table dwdz_cpp_certificate_path (
   node_id              VARCHAR(50)          not null default sys_guid(),
   node_name            VARCHAR(100)         not null,
   node_type            VARCHAR(20)          not null,
   label_text           VARCHAR(100)         null,
   show_exam_label      BOOLEAN              null,
   description          TEXT                 null,
   salary_range         VARCHAR(100)         null,
   career_prospects     TEXT                 null,
   parent_id            VARCHAR(50)          null,
   rec_create_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   rec_modify_date      TIMESTAMP            not null default CURRENT_TIMESTAMP,
   constraint PK_DWDZ_CPP_CERTIFICATE_PATH primary key (node_id)
);

comment on table dwdz_cpp_certificate_path is
'证书路径表';

comment on column dwdz_cpp_certificate_path.node_id is
'节点唯一标识ID（主键）';

comment on column dwdz_cpp_certificate_path.node_name is
'节点名称';

comment on column dwdz_cpp_certificate_path.node_type is
'节点类型（certificate/category/subcategory/position/pathway）';

comment on column dwdz_cpp_certificate_path.label_text is
'标签文本（如"[考试发证]"）';

comment on column dwdz_cpp_certificate_path.show_exam_label is
'是否显示考试标签';

comment on column dwdz_cpp_certificate_path.description is
'描述信息';

comment on column dwdz_cpp_certificate_path.salary_range is
'薪资范围';

comment on column dwdz_cpp_certificate_path.career_prospects is
'职业前景';

comment on column dwdz_cpp_certificate_path.parent_id is
'父节点ID（外键）';

comment on column dwdz_cpp_certificate_path.rec_create_date is
'记录创建日期';

comment on column dwdz_cpp_certificate_path.rec_modify_date is
'记录修改日期';


// 群体颜色配置
export const groupColors = {
  '预备船员': '#f39c12',  // 橙色 - 代表希望和未来
  '现役船员': '#1E5F8B',   // 海事蓝 - 代表现在和专业
  '退役船员': '#8e44ad'   // 紫色 - 代表经验和传承
}

// 特定区块颜色配置
export const blockColors = {
  '持海船高级船员适任证书': '#2E86AB',      // 深蓝色
  '持海船普通船员适任证书': '#A23B72',      // 深紫红色
  '持不参加航行和轮机值班海船船员适任证书': '#F18F01'  // 深橙色
}

// 图表配置参数
export const chartConfig = {
  // Canvas绘制参数
  canvas: {
    defaultRadius: 140,
    defaultInnerRadius: 60,
    minRadiusRatio: 0.35, // 相对于画布最小边的比例
    innerRadiusRatio: 0.15 // 相对于画布最小边的比例
  },
  
  // 动画配置
  animation: {
    rotationDuration: 800, // 轮盘旋转动画时长
    minimumDragDistance: Math.PI / 6, // 最小拖拽距离
    particleCount: 30 // 背景粒子数量
  },
  
  // 默认选中的群体索引
  defaultGroupIndex: 1, // 默认显示现役船员
  
  // 响应式断点
  responsive: {
    tablet: 1024,
    mobile: 768
  },
  
  // 轮盘显示尺寸配置
  wheelSizes: {
    desktop: { width: 280, height: 280 },
    tablet: { width: 200, height: 200 },
    mobile: { width: 180, height: 180 }
  }
}

---

## 8. 开发注意事项

### 性能优化
- 对于大数据量的树形结构，建议使用缓存机制
- 搜索接口应支持分页和结果数量限制
- 考虑使用Redis缓存热点数据

### 安全考虑
- 所有接口都应进行身份验证
- 对输入参数进行严格验证和过滤
- 防止SQL注入和XSS攻击

### 扩展性
- 数据结构设计应考虑未来扩展需求
- 接口版本控制，支持向后兼容
- 配置信息应支持动态修改

---

*本文档最后更新时间: 2024-01-01* 