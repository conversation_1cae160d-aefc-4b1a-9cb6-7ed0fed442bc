package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCppCertificatePath;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 证书路径表DAO接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCppCertificatePathDao extends BaseMapper<DwdzCppCertificatePath> {

    /**
     * 根据证书类型获取证书路径节点（已废弃，建议使用getNodesByRootName）
     */
    @Deprecated
    List<DwdzCppCertificatePath> getNodesByCertificateType(@Param("certificateType") String certificateType);

    /**
     * 第一步：根据根节点名称获取所有非叶子节点
     */
    List<DwdzCppCertificatePath> getNodesByRootName(@Param("rootName") String rootName);

    /**
     * 第三步：根据证书类型获取所有叶子节点
     */
    List<DwdzCppCertificatePath> getLeafNodesByCertType(@Param("rootName") String rootName);

    /**
     * 根据关键词搜索证书节点
     */
    List<DwdzCppCertificatePath> searchNodesByKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);

    /**
     * 根据父节点ID获取子节点
     */
    List<DwdzCppCertificatePath> getChildNodes(@Param("parentId") String parentId);

    /**
     * 获取根节点（没有父节点的节点）
     */
    List<DwdzCppCertificatePath> getRootNodes();

    /**
     * 根据节点类型获取节点
     */
    List<DwdzCppCertificatePath> getNodesByType(@Param("nodeType") String nodeType);

    /**
     * 根据证书类型和节点类型获取节点
     */
    List<DwdzCppCertificatePath> getNodesByCertificateTypeAndNodeType(@Param("certificateType") String certificateType, @Param("nodeType") String nodeType);

    /**
     * 平铺查询：根据证书类型名称获取所有相关节点（避免SQL递归死循环）
     */
    List<DwdzCppCertificatePath> getAllNodesByRootName(@Param("rootName") String rootName);

    /**
     * 根据节点名称获取证书类型
     */
    String getCertTypeByNodeName(@Param("nodeName") String nodeName);
} 