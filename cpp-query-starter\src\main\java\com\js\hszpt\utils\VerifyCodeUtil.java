package com.js.hszpt.utils;

import cn.hutool.core.util.StrUtil;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.vo.Captcha;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Component
public class VerifyCodeUtil {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public Captcha initCaptcha(){
        String captchaId = UUID.randomUUID().toString().replace("-", "");
        String code = (new CreateVerifyCode()).randomStr(4);
        Captcha captcha = new Captcha();
        captcha.setCaptchaId(captchaId);
        this.redisTemplate.opsForValue().set("captcha::"+captchaId, code, 2L, TimeUnit.MINUTES);
        return captcha;
    }

    public boolean check(String key,String value) {
        //验证码验证
        if(StrUtil.isBlank(value)){
            return false;
        }
        String code = redisTemplate.opsForValue().get("captcha::"+key);
        if(code==null){
            return false;
        }
        return code.equalsIgnoreCase(value);
    }
}
