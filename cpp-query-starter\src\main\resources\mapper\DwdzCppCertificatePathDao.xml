<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCppCertificatePathDao">
    
    <!-- 通用查询映射结果 - 根据新的表结构 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCppCertificatePath">
        <id column="node_id" property="nodeId" />
        <result column="cert_type" property="certType" />
        <result column="node_name" property="nodeName" />
        <result column="node_type" property="nodeType" />
        <result column="relation_type" property="relationType" />
        <result column="sqxszsmc" property="sqxszsmc" />
        <result column="parent_id" property="parentId" />
        <result column="sort_order" property="sortOrder" />
        <result column="rec_create_date" property="recCreateDate" />
        <result column="rec_modify_date" property="recModifyDate" />
    </resultMap>

    <!-- 第一步：根据根节点名称获取所有非叶子节点 -->
    <select id="getNodesByRootName" resultMap="BaseResultMap">
        SELECT node_id, cert_type, node_name, node_type, relation_type, sqxszsmc, 
               parent_id, sort_order, rec_create_date, rec_modify_date
        FROM dwdz_crew_certificate_path 
        START WITH node_name = #{rootName}
        CONNECT BY parent_id = PRIOR node_id 
               AND cert_type = PRIOR cert_type  -- 确保在同一证书类型范围内
               AND PRIOR node_id != PRIOR parent_id  -- 只查找非叶子节点
        ORDER SIBLINGS BY sort_order ASC, node_name ASC
    </select>

    <!-- 第三步：根据证书类型获取所有叶子节点 -->
    <select id="getLeafNodesByCertType" resultMap="BaseResultMap">
        SELECT node_id, cert_type, node_name, node_type, relation_type, sqxszsmc, 
               parent_id, sort_order, rec_create_date, rec_modify_date
        FROM dwdz_crew_certificate_path 
        WHERE parent_id = node_id 
        AND cert_type = (
            SELECT cert_type FROM dwdz_crew_certificate_path 
            WHERE node_name = #{rootName}
        )
        ORDER BY sort_order ASC, node_name ASC
    </select>

    <!-- 根据证书类型获取证书路径节点（已废弃，保留兼容性） -->
    <select id="getNodesByCertificateType" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_certificate_path 
        WHERE cert_type = #{certificateType}
        ORDER BY parent_id ASC, sort_order ASC, node_name ASC
    </select>

    <!-- 根据关键词搜索证书节点 -->
    <select id="searchNodesByKeyword" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_certificate_path 
        WHERE node_name LIKE '%' || #{keyword} || '%'
           OR sqxszsmc LIKE '%' || #{keyword} || '%'
           OR cert_type LIKE '%' || #{keyword} || '%'
        ORDER BY 
            CASE 
                WHEN node_name LIKE '%' || #{keyword} || '%' THEN 1
                WHEN sqxszsmc LIKE '%' || #{keyword} || '%' THEN 2
                WHEN cert_type LIKE '%' || #{keyword} || '%' THEN 3
                ELSE 4
            END,
            node_name ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据父节点ID获取子节点 -->
    <select id="getChildNodes" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_certificate_path 
        WHERE parent_id = #{parentId}
          AND node_id != parent_id  -- 排除parent_id=node_id的叶子节点
        ORDER BY sort_order ASC, node_name ASC
    </select>

    <!-- 获取根节点（没有父节点的节点） -->
    <select id="getRootNodes" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_certificate_path 
        WHERE parent_id IS NULL
        ORDER BY sort_order ASC, cert_type ASC, node_name ASC
    </select>

    <!-- 根据节点类型获取节点 -->
    <select id="getNodesByType" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_certificate_path 
        WHERE node_type = #{nodeType}
        ORDER BY sort_order ASC, cert_type ASC, node_name ASC
    </select>

    <!-- 根据证书类型和节点类型获取节点 -->
    <select id="getNodesByCertificateTypeAndNodeType" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_certificate_path 
        WHERE cert_type = #{certificateType}
          AND node_type = #{nodeType}
        ORDER BY sort_order ASC, node_name ASC
    </select>

    <!-- 平铺查询：根据证书类型名称获取所有相关节点（避免SQL递归死循环） -->
    <select id="getAllNodesByRootName" resultMap="BaseResultMap">
        SELECT DISTINCT node_id, cert_type, node_name, node_type, relation_type, sqxszsmc, 
               parent_id, sort_order, rec_create_date, rec_modify_date
        FROM dwdz_crew_certificate_path 
        WHERE cert_type = (
            SELECT cert_type FROM dwdz_crew_certificate_path 
            WHERE node_name = #{rootName}
        )
        ORDER BY sort_order ASC, node_name ASC
    </select>

    <!-- 根据节点名称获取证书类型 -->
    <select id="getCertTypeByNodeName" resultType="java.lang.String">
        SELECT cert_type FROM dwdz_crew_certificate_path 
        WHERE node_name = #{nodeName}
    </select>

</mapper> 