package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员当前实时状态信息表
 */
@Data
@TableName("dwdz_crew_current_status")
public class DwdzCrewCurrentStatus {
    /**
     * 状态记录唯一标识
     */
    @TableId("status_id")
    private String statusId;

    /**
     * 关联船员基本信息表的船员ID
     */
    @TableField("crew_id")
    private String crewId;

    /**
     * 当前身份 RESERVE_CREW(预备船员), ACTIVE_CREW(现役船员), RETIRED_CREW(退役船员)
     */
    @TableField("current_status")
    private String currentStatus;

    /**
     * 在船状态（在船/休假/待派/离船）
     */
    @TableField("onboard_status")
    private String onboardStatus;

    /**
     * 上船日期
     */
    @TableField("onboard_date")
    private Date onboardDate;

    /**
     * 当前服务船舶ID
     */
    @TableField("ship_id")
    private String shipId;

    /**
     * 当前服务船名
     */
    @TableField("ship_name")
    private String shipName;

    /**
     * 英文船名
     */
    @TableField("ship_name_en")
    private String shipNameEn;

    /**
     * 船舶IMO
     */
    @TableField("ship_imo")
    private String shipImo;

    /**
     * 船舶MMSI
     */
    @TableField("ship_mmsi")
    private String shipMmsi;

    /**
     * 船舶类型
     */
    @TableField("ship_type")
    private String shipType;

    /**
     * 船籍港
     */
    @TableField("home_port")
    private String homePort;

    /**
     * 船籍港所属海事辖区
     */
    @TableField("registry_port_authority")
    private String registryPortAuthority;

    /**
     * 当前职务航区 unlimited-无限航区 coastal-沿海航区
     */
    @TableField("navi_area")
    private String naviArea;

    /**
     * 当前职务等级编码 FIRST_CLASS(一等), SECOND_CLASS(二等), THIRD_CLASS(三等)
     */
    @TableField("position_level")
    private String positionLevel;

    /**
     * 适任证等级名称
     */
    @TableField("comp_level_name")
    private String compLevelName;

    /**
     * 当前职务
     */
    @TableField("position")
    private String position;

    /**
     * 任职开始日期
     */
    @TableField("position_start_date")
    private Date positionStartDate;

    /**
     * 当前位置（港口或经纬度）
     */
    @TableField("current_location")
    private String currentLocation;

    /**
     * 当前位置所属机构编码
     */
    @TableField("current_location_org")
    private String currentLocationOrg;

    /**
     * 位置更新时间
     */
    @TableField("location_update_time")
    private Date locationUpdateTime;

    /**
     * 风险等级
     */
    @TableField("risk_level")
    private String riskLevel;

    /**
     * 船员当前年龄
     */
    @TableField("current_age")
    private BigDecimal currentAge;

    /**
     * 航行类型：IN_PORT_DOMESTIC(境内在港航行), OFF_PORT_DOMESTIC(境内港外航行), OVERSEAS(境外航行)
     */
    @TableField("navi_category")
    private String naviCategory;

    /**
     * 是否外派船员：true-外派船员，false-非外派船员（默认值）
     */
    @TableField("is_dispatched")
    private Boolean isDispatched;

    /**
     * 外派公司所属辖区
     */
    @TableField("dispatch_company_region")
    private String dispatchCompanyRegion;

    /**
     * 外派公司名称
     */
    @TableField("dispatch_company_name")
    private String dispatchCompanyName;

    /**
     * 船舶管理公司名称
     */
    @TableField("ship_management_company")
    private String shipManagementCompany;

    /**
     * 船东公司名称
     */
    @TableField("ship_owner_company")
    private String shipOwnerCompany;

    /**
     * 船东公司所属辖区
     */
    @TableField("ship_owner_region")
    private String shipOwnerRegion;

    /**
     * 船舶管理公司所属辖区
     */
    @TableField("management_company_region")
    private String managementCompanyRegion;

    /**
     * 船舶经营公司
     */
    @TableField("ship_operator_company")
    private String shipOperatorCompany;

    /**
     * 船舶经营公司所属辖区
     */
    @TableField("operator_region")
    private String operatorRegion;

    /**
     * 当前是否有有效代办委托公司（true-有，false-无）
     */
    @TableField("has_valid_agent")
    private Boolean hasValidAgent;

    /**
     * 有效委托代办公司数量
     */
    @TableField("active_agent_count")
    private Integer activeAgentCount;

    /**
     * 当前是否有派遣报备代办业务（true-有，false-无）
     */
    @TableField("has_agent_dispatch_filing")
    private Boolean hasAgentDispatchFiling;

    /**
     * 所属派遣代办公司名称（主要服务公司）
     */
    @TableField("dispatch_agent_name")
    private String dispatchAgentName;

    /**
     * 所属派遣代办公司的所在辖区
     */
    @TableField("dispatch_agent_region")
    private String dispatchAgentRegion;

    /**
     * 记录几年内有派遣报备代办业务（值代表年数）
     */
    @TableField("dispatch_years_record")
    private Integer dispatchYearsRecord;

    /**
     * 记录几年内有有效代办委托公司（值代表年数）
     */
    @TableField("valid_agent_years_record")
    private Integer validAgentYearsRecord;

    /**
     * 近10年内委托代办公司总数（不重复计数）
     */
    @TableField("agent_count_10y")
    private Integer agentCount10y;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
} 