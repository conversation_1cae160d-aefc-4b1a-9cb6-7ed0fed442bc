package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 公司类型字典表
 * 对应表：dwdz_crew_company_type
 */
@Data
@TableName("dwdz_crew_company_type")
@ApiModel(value = "公司类型字典表")
public class DwdzCrewCompanyType {
    /** 公司类型代码 */
    @TableId
    @ApiModelProperty(value = "公司类型代码")
    private String companyType;

    /** 公司类型名称 */
    @ApiModelProperty(value = "公司类型名称")
    private String companyTypeName;

    /** 公司类型描述 */
    @ApiModelProperty(value = "公司类型描述")
    private String companyTypeDesc;

    /** 排序序号 */
    @ApiModelProperty(value = "排序序号")
    private Integer sortOrder;

    /** 是否启用（t/f） */
    @ApiModelProperty(value = "是否启用")
    private String isEnabled;

    /** 记录创建日期 */
    @ApiModelProperty(value = "记录创建日期")
    private Date recCreateDate;

    /** 记录修改日期 */
    @ApiModelProperty(value = "记录修改日期")
    private Date recModifyDate;
} 