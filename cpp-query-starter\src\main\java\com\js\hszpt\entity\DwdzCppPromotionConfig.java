package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 晋升配置表实体类
 * 用于管理船员职务晋升配置的JSON数据
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_promo_config")
public class DwdzCppPromotionConfig {

    /**
     * 配置唯一标识ID（主键）
     */
    @TableId(value = "config_id", type = IdType.ASSIGN_UUID)
    private String configId;

    /**
     * 路径选择器选项JSON数据
     */
    @TableField("path_selector_options")
    private String pathSelectorOptions;

    /**
     * 职业路径JSON数据
     */
    @TableField("career_paths")
    private String careerPaths;

    /**
     * 管理关系JSON数据
     */
    @TableField("management_relations")
    private String managementRelations;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
} 