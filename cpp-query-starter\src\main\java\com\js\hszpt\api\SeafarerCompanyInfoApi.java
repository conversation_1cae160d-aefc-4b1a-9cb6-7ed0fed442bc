package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.DwdzCrewCompanyInfo;
import com.js.hszpt.service.DwdzCrewCompanyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 船员关联公司基本信息API接口
 */
@RestController
@RequestMapping("/api/seafarer/company-info")
public class SeafarerCompanyInfoApi {

    @Autowired
    private DwdzCrewCompanyInfoService companyInfoService;

    /** 根据ID查详情 */
    @GetMapping("/{companyId}")
    public Result<DwdzCrewCompanyInfo> getById(@PathVariable String companyId) {
        return ResultUtil.data(companyInfoService.getById(companyId));
    }

    /** 查询所有公司信息 */
    @GetMapping("/list")
    public Result<List<DwdzCrewCompanyInfo>> listAll() {
        return ResultUtil.data(companyInfoService.list());
    }

    /** 新增公司信息 */
    @PostMapping
    public Result<Boolean> add(@RequestBody DwdzCrewCompanyInfo info) {
        return ResultUtil.data(companyInfoService.save(info));
    }

    /** 修改公司信息 */
    @PutMapping
    public Result<Boolean> update(@RequestBody DwdzCrewCompanyInfo info) {
        return ResultUtil.data(companyInfoService.updateById(info));
    }

    /** 删除公司信息 */
    @DeleteMapping("/{companyId}")
    public Result<Boolean> delete(@PathVariable String companyId) {
        return ResultUtil.data(companyInfoService.removeById(companyId));
    }
} 