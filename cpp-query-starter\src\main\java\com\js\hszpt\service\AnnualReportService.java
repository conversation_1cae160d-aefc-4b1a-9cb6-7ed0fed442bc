package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.dto.GeneratePdfRequest;
import com.js.hszpt.utils.DataMaskingUtil;
import com.js.hszpt.entity.CrewAnnualReport;
import com.js.hszpt.entity.CrewCurrentCert;
import com.js.hszpt.entity.DwdzCrewCurrentCert;
import com.js.hszpt.enmus.CertificateStatusEnum;
import com.js.hszpt.mapper.CrewAnnualReportDao;
import com.js.hszpt.mapper.CrewCurrentCertDao;
import com.js.hszpt.mapper.DwdzCrewCurrentCertDao;
import com.js.hszpt.vo.AnnualReportDataVO;
import com.js.hszpt.vo.PdfGenerateResponseVO;
import com.js.hszpt.vo.YearOptionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 年度报告服务类
 * 
 * @ClassName: AnnualReportService
 * @Description: 年度报告服务实现
 * @author: System Generation
 */
@Slf4j
@Service
public class AnnualReportService extends ServiceImpl<CrewAnnualReportDao, CrewAnnualReport> {

    private final static int START_YEAR = 2018;

    @Autowired
    private CrewAnnualReportDao crewAnnualReportDao;

    @Autowired
    private CrewCurrentCertDao crewCurrentCertDao;

    @Autowired
    private DwdzCrewCurrentCertDao dwdzCrewCurrentCertDao;

    public List<CrewAnnualReport> getPushMessageList() {
        return crewAnnualReportDao.getPushMessageList();
    }

    /**
     * 获取年份选项列表
     * 
     * @return 年份选项列表
     */
    public List<YearOptionVO> getYearOptions() {
        List<YearOptionVO> yearOptions = new ArrayList<>();
        int current = DateUtil.year(new Date());
        // 默认取到当前年份
        for (int i = current; i >= START_YEAR; i--) {
            YearOptionVO yearOptionVO = new YearOptionVO(i, i + "年");
            yearOptions.add(yearOptionVO);
        }
        return yearOptions;
    }

    /**
     * 获取年度报告数据
     * 
     * @param year       年份
     * @param seafarerId 船员ID或身份证号
     * @return 年度报告数据
     */
    public AnnualReportDataVO getAnnualReportData(Integer year, String seafarerId) {
        // 验证参数不能为空
        if (year == null || StrUtil.isBlank(seafarerId)) {
            log.warn("参数验证失败 - 年份: {}, 船员ID: {}", year, seafarerId);
            return null;
        }
        // 查询基本信息 - 添加异常处理
        Map<String, Object> basicInfoMap = new HashMap<>();
        try {
            basicInfoMap = crewAnnualReportDao.getCrewBasicInfo(seafarerId);
        } catch (Exception e) {
            log.warn("查询基本信息失败:", e);
        }

        // 查询船舶状态信息 - 使用船员ID查询dwdz_crew_current_status表
        Map<String, Object> shipInfoMap = new HashMap<>();
        try {
            shipInfoMap = crewAnnualReportDao.getCrewCurrentStatus(seafarerId);
        } catch (Exception e) {
            log.warn("查询船舶信息失败:", e);
        }

        // 查询证书信息 - 使用船员ID查询dwdz_crew_current_cert表
        List<DwdzCrewCurrentCert> certs;
        try {
            certs = dwdzCrewCurrentCertDao.getByCrewId(seafarerId);
        } catch (Exception e) {
            log.warn("查询证书信息失败，使用空列表: ", e);
            certs = new ArrayList<>();
        }

        // 构建返回数据
        AnnualReportDataVO result = new AnnualReportDataVO();

        // 基本信息
        AnnualReportDataVO.BasicInfoVO basicInfo = new AnnualReportDataVO.BasicInfoVO();
        Optional<Map<String, Object>> basicInfoOptional = Optional.ofNullable(basicInfoMap);
        basicInfo.setName(basicInfoOptional
                .map(map -> (String) map.get("name"))
                .orElse(null));
        // 对身份证号进行脱敏处理
        basicInfo.setIdNumber(basicInfoOptional
                .map(map -> (String) map.get("idNumber"))
                .map(DataMaskingUtil::maskIdNumber)
                .orElse(null));
        basicInfo.setAvatar(basicInfoOptional
                .map(map -> (String) map.get("avatar"))
                .orElse(null));
        result.setBasicInfo(basicInfo);

        // 统计数据 - 修复clearDate的格式化处理
        AnnualReportDataVO.StatisticsVO statistics = new AnnualReportDataVO.StatisticsVO();
        // 查询年度报告主记录
        CrewAnnualReport report = crewAnnualReportDao.getByCrewIdAndYear(seafarerId, year.toString());
        Optional<CrewAnnualReport> reportOptional = Optional.ofNullable(report);

        // 安全获取违法得分总计
        statistics.setViolationScoreTotal(
                reportOptional.map(CrewAnnualReport::getIllegalScoreTotal)
                        .map(BigDecimal::intValue)
                        .orElse(0));

        // 安全获取扣分
        statistics.setScoreReduction(
                reportOptional.map(CrewAnnualReport::getDeductedScore)
                        .map(score -> score.intValue() + "分")
                        .orElse("0分"));

        // 安全处理清分日期字段
        String clearanceDate = reportOptional
                .map(CrewAnnualReport::getClearDate)
                .map(date -> {
                    try {
                        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    } catch (Exception e) {
                        log.warn("清分日期格式化失败: {}", e.getMessage());
                        return null;
                    }
                })
                .orElse(null);
        statistics.setClearanceDate(clearanceDate);

        // 安全获取工作天数相关字段
        statistics.setServiceDaysTotal(
                reportOptional.map(CrewAnnualReport::getWorkingDaysTotal)
                        .orElse(0));
        statistics.setUnlimitedAreaDays(
                reportOptional.map(CrewAnnualReport::getUnlimitedWorkingDays)
                        .orElse(0));
        statistics.setCoastalAreaDays(
                reportOptional.map(CrewAnnualReport::getCoastalWorkingDays)
                        .orElse(0));

        result.setStatistics(statistics);

        // 船舶状态
        AnnualReportDataVO.ShipStatusVO shipStatus = new AnnualReportDataVO.ShipStatusVO();
        Optional<Map<String, Object>> shipInfoOptional = Optional.ofNullable(shipInfoMap);
        shipStatus.setCurrentStatus(shipInfoOptional
                .map(map -> (String) map.get("currentStatus"))
                .orElse(null));
        shipStatus.setShipName(shipInfoOptional
                .map(map -> (String) map.get("shipName"))
                .orElse(null));
        shipStatus.setShipType(shipInfoOptional
                .map(map -> (String) map.get("shipType"))
                .orElse(null));
        shipStatus.setGrossTonnage(shipInfoOptional
                .map(map -> (String) map.get("grossTonnage"))
                .orElse(null));
        shipStatus.setPosition(shipInfoOptional
                .map(map -> (String) map.get("position"))
                .orElse(null));
        shipStatus.setBoardingDate(shipInfoOptional
                .map(map -> (String) map.get("boardingDate"))
                .orElse(null));
        shipStatus.setDeparturePort(shipInfoOptional
                .map(map -> (String) map.get("departurePort"))
                .orElse(null));
        shipStatus.setDestinationPort(shipInfoOptional
                .map(map -> (String) map.get("destinationPort"))
                .orElse(null));
        result.setShipStatus(shipStatus);

        // 证书信息
        AnnualReportDataVO.CertificatesVO certificates = new AnnualReportDataVO.CertificatesVO();
        certificates.setCompetency(convertDwdzCertificates(certs, "COMPETENCE"));
        certificates.setTraining(convertDwdzCertificates(certs, "QUALIFICATION"));
        certificates.setHealth(convertDwdzCertificates(certs, "HEALTH"));
        result.setCertificates(certificates);

        return result;
    }

    /**
     * 生成PDF报告
     * 
     * @param request PDF生成请求
     * @return PDF生成结果
     */
    public PdfGenerateResponseVO generatePdf(GeneratePdfRequest request) {
        // 如果未传船员ID，这里应该从当前登录用户获取，暂时使用示例ID
        String crewId = StrUtil.isNotBlank(request.getSeafarerId()) ? request.getSeafarerId() : "123456";

        // 这里应该实现实际的PDF生成逻辑
        // 暂时返回模拟数据
        PdfGenerateResponseVO response = new PdfGenerateResponseVO();
        response.setFileUrl("https://example.com/reports/annual-report-" + request.getYear() + "-" + crewId + ".pdf");
        response.setFileName("年度报告-" + request.getYear() + ".pdf");
        response.setFileSize("2.5MB");

        // 修复：使用LocalDateTime而不是LocalDate来格式化包含时分秒的时间
        response.setGenerateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return response;
    }

    /**
     * 转换证书列表
     * 
     * @param certs    证书列表
     * @param category 证书类别
     * @return 转换后的证书VO列表
     */
    private List<AnnualReportDataVO.CertificateVO> convertCertificates(List<CrewCurrentCert> certs, String category) {
        // 安全处理空参数
        if (certs == null || category == null) {
            return new ArrayList<>();
        }

        return certs.stream()
                .filter(cert -> cert != null && category.equals(cert.getCertCategory()))
                .map(cert -> {
                    AnnualReportDataVO.CertificateVO certVO = new AnnualReportDataVO.CertificateVO();
                    // 使用Optional安全处理证书名称
                    certVO.setName(Optional.ofNullable(cert.getCertName()).orElse(""));

                    // 安全处理日期格式化
                    try {
                        certVO.setExpiryDate(cert.getExpiryDate() != null
                                ? new java.text.SimpleDateFormat("yyyy-MM-dd").format(cert.getExpiryDate())
                                : null);
                    } catch (Exception e) {
                        log.warn("证书到期日期格式化失败: {}", e.getMessage());
                        certVO.setExpiryDate(null);
                    }

                    // 判断证书状态
                    CertificateStatusEnum statusEnum;
                    if (cert.getExpiryDate() != null) {
                        try {
                            LocalDate now = LocalDate.now();
                            LocalDate expiryDate = cert.getExpiryDate().toInstant()
                                    .atZone(java.time.ZoneId.systemDefault()).toLocalDate();

                            if (expiryDate.isBefore(now)) {
                                statusEnum = CertificateStatusEnum.EXPIRED;
                            } else if (expiryDate.isBefore(now.plusDays(30))) {
                                statusEnum = CertificateStatusEnum.EXPIRING;
                            } else {
                                statusEnum = CertificateStatusEnum.VALID;
                            }
                        } catch (Exception e) {
                            log.warn("证书状态判断失败: {}", e.getMessage());
                            statusEnum = CertificateStatusEnum.VALID;
                        }
                    } else {
                        statusEnum = CertificateStatusEnum.VALID;
                    }

                    // 设置状态信息
                    certVO.setStatus(statusEnum.getCode());
                    certVO.setStatusClass(statusEnum.getStatusClass());

                    return certVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换证书列表（新表dwdz_crew_current_cert）
     * 
     * @param certs    证书列表
     * @param category 证书类别
     * @return 转换后的证书VO列表
     */
    private List<AnnualReportDataVO.CertificateVO> convertDwdzCertificates(List<DwdzCrewCurrentCert> certs,
            String category) {
        // 安全处理空参数
        if (certs == null || category == null) {
            return new ArrayList<>();
        }

        return certs.stream()
                .filter(cert -> cert != null && category.equals(cert.getCertCategory()))
                .map(cert -> {
                    AnnualReportDataVO.CertificateVO certVO = new AnnualReportDataVO.CertificateVO();
                    // 使用Optional安全处理证书名称
                    certVO.setName(Optional.ofNullable(cert.getCertName()).orElse(""));

                    // 安全处理日期格式化
                    try {
                        certVO.setExpiryDate(cert.getExpiryDate() != null
                                ? new java.text.SimpleDateFormat("yyyy-MM-dd").format(cert.getExpiryDate())
                                : null);
                    } catch (Exception e) {
                        log.warn("证书到期日期格式化失败: {}", e.getMessage());
                        certVO.setExpiryDate(null);
                    }

                    // 判断证书状态
                    CertificateStatusEnum statusEnum;
                    if (cert.getExpiryDate() != null) {
                        try {
                            LocalDate now = LocalDate.now();
                            LocalDate expiryDate = cert.getExpiryDate().toInstant()
                                    .atZone(java.time.ZoneId.systemDefault()).toLocalDate();

                            if (expiryDate.isBefore(now)) {
                                statusEnum = CertificateStatusEnum.EXPIRED;
                            } else if (expiryDate.isBefore(now.plusDays(30))) {
                                statusEnum = CertificateStatusEnum.EXPIRING;
                            } else {
                                statusEnum = CertificateStatusEnum.VALID;
                            }
                        } catch (Exception e) {
                            log.warn("证书状态判断失败: {}", e.getMessage());
                            statusEnum = CertificateStatusEnum.VALID;
                        }
                    } else {
                        statusEnum = CertificateStatusEnum.VALID;
                    }

                    // 设置状态信息
                    certVO.setStatus(statusEnum.getCode());
                    certVO.setStatusClass(statusEnum.getStatusClass());

                    return certVO;
                })
                .collect(Collectors.toList());
    }
}