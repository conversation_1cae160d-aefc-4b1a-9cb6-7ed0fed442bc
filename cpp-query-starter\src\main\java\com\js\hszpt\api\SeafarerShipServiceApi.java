package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.DwdzCrewShipService;
import com.js.hszpt.service.DwdzCrewShipServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 船员船舶类型履职统计API
 */
@Api(tags = "船员船舶类型履职统计API")
@RestController
@RequestMapping("/api/seafarer/ship-service")
public class SeafarerShipServiceApi {

    @Autowired
    private DwdzCrewShipServiceService shipService;

    @ApiOperation("根据船员ID查询船舶类型履职统计")
    @GetMapping("/{crewId}")
    public Result<List<DwdzCrewShipService>> getByCrewId(@PathVariable String crewId,String statType) {
        List<DwdzCrewShipService> list = shipService.getByCrewId(crewId,statType);
        // 取整处理
        for (DwdzCrewShipService item : list) {
            if (item.getTotalMonths() != null) {
                item.setTotalMonths(new java.math.BigDecimal(item.getTotalMonths().setScale(0, java.math.RoundingMode.HALF_UP).intValue()));
            }
        }
        return ResultUtil.data(list);
    }
} 