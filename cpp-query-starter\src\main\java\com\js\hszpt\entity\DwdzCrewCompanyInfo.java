package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 船员关联公司基本信息表
 * 对应表：dwdz_crew_company_info
 */
@Data
@TableName("dwdz_crew_company_info")
@ApiModel(value = "船员关联公司基本信息表")
public class DwdzCrewCompanyInfo {
    /** 公司唯一识别ID */
    @TableId
    @ApiModelProperty(value = "公司唯一识别ID")
    private String companyId;

    /** 公司全称 */
    @ApiModelProperty(value = "公司全称")
    private String companyName;

    /** 公司简称 */
    @ApiModelProperty(value = "公司简称")
    private String companyShortName;

    /** 公司注册地址 */
    @ApiModelProperty(value = "公司注册地址")
    private String address;

    /** 公司联系电话 */
    @ApiModelProperty(value = "公司联系电话")
    private String contact;

    /** 记录创建日期 */
    @ApiModelProperty(value = "记录创建日期")
    private Date recCreateDate;

    /** 记录修改日期 */
    @ApiModelProperty(value = "记录修改日期")
    private Date recModifyDate;
} 