package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.CrewBasicInfo;
import com.js.hszpt.entity.DwdzCrewCareerTrack;
import com.js.hszpt.entity.DwdzCrewCurrentCert;
import com.js.hszpt.mapper.DwdzCrewCurrentCertDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

@Service
public class DwdzCrewCurrentCertService extends ServiceImpl<DwdzCrewCurrentCertDao, DwdzCrewCurrentCert> {

    @Autowired
    private CrewBasicInfoService crewBasicInfoService;

    /**
     * 根据 crewId 与类型筛选职务代码集合，查询适任证书，按发证日期倒序
     */
    public List<DwdzCrewCareerTrack> getCareerTrackByCrewId(String crewId, String type) {
        CrewBasicInfo crewBasicInfo = crewBasicInfoService.getById(crewId);
        if (crewBasicInfo == null) {
            return new ArrayList<>();
        }

        String idNumber = crewBasicInfo.getIdNumber();
        if (StrUtil.isEmpty(idNumber)) {
            return new ArrayList<>();
        }

        List<String> positionCodeList = null;
        if (StrUtil.equals("1", type)) {
            positionCodeList = CollUtil.newArrayList("56", "55", "54", "53", "52", "51");
        } else if (StrUtil.equals("2", type)) {
            positionCodeList = CollUtil.newArrayList("66", "65", "64", "63", "62", "61");
        } else if (StrUtil.equals("3", type)) {
            positionCodeList = CollUtil.newArrayList("68", "67");
        }
        if (CollUtil.isEmpty(positionCodeList)) {
            return new ArrayList<>();
        }

        DateTime birthDate = IdcardUtil.getBirthDate(idNumber);
        if (birthDate == null) {
            return new ArrayList<>();
        }

        QueryWrapper<DwdzCrewCurrentCert> queryWrapper = Wrappers.query();
        queryWrapper
                .select("position_level","min(issue_date) as issue_date")
                .lambda()
                .eq(DwdzCrewCurrentCert::getCrewId, crewId)
                .eq(DwdzCrewCurrentCert::getCertCategory, "COMPETENCE")
                .in(DwdzCrewCurrentCert::getCompetencePositionCode, positionCodeList)
                .isNotNull(DwdzCrewCurrentCert::getIssueDate)
                .groupBy(DwdzCrewCurrentCert::getPositionLevel)
                .orderByAsc(DwdzCrewCurrentCert::getIssueDate);
        List<DwdzCrewCurrentCert> dwdzCrewCurrentCertList = this.list(queryWrapper);
        List<DwdzCrewCareerTrack> dwdzCrewCareerTrackList = new ArrayList<>();
        for (int i = 0; i < dwdzCrewCurrentCertList.size(); i++) {
            DwdzCrewCurrentCert dwdzCrewCurrentCert = dwdzCrewCurrentCertList.get(i);
            DwdzCrewCareerTrack dwdzCrewCareerTrack = new DwdzCrewCareerTrack();
            dwdzCrewCareerTrack.setCrewId(crewId);
            dwdzCrewCareerTrack.setTrackOrder(i + 1);
            dwdzCrewCareerTrack.setPositionName(dwdzCrewCurrentCert.getPositionLevel());
            Date issueDate = dwdzCrewCurrentCert.getIssueDate();
            dwdzCrewCareerTrack.setFirstServiceDate(issueDate);
            dwdzCrewCareerTrack.setCertObtainDate(issueDate);
            long birthYear = DateUtil.betweenYear(birthDate, issueDate, true);
            dwdzCrewCareerTrack.setAgeAtObtain((int) birthYear);
            dwdzCrewCareerTrack.setPromotionDuration(new BigDecimal(birthYear));
            if (i != dwdzCrewCurrentCertList.size() - 1) {
                DwdzCrewCurrentCert nextDwdzCrewCurrentCert = dwdzCrewCurrentCertList.get(i + 1);
                Date nextIssueDate = nextDwdzCrewCurrentCert.getIssueDate();
                long betweenYear = DateUtil.betweenYear(issueDate, nextIssueDate, true);
                dwdzCrewCareerTrack.setNextPromotionDuration(new BigDecimal(betweenYear));
            }else {
                DateTime now = DateUtil.date();
                long betweenYear = DateUtil.betweenYear(issueDate, now, true);
                dwdzCrewCareerTrack.setNextPromotionDuration(new BigDecimal(betweenYear));
            }
            dwdzCrewCareerTrackList.add(dwdzCrewCareerTrack);
        }
        return dwdzCrewCareerTrackList;
    }
}
