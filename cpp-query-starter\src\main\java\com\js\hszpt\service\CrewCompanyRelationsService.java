package com.js.hszpt.service;

import com.js.hszpt.vo.CompanyRelationsStatisticsVO;
import com.js.hszpt.vo.CompanyRelationsDetailVO;
import com.js.hszpt.vo.CompanyTypeVO;

import java.util.List;

/**
 * 船员公司关系服务接口
 */
public interface CrewCompanyRelationsService {
    
    /**
     * 获取船员公司关系统计信息
     * @param seafarerId 船员ID
     * @return 统计信息
     */
    CompanyRelationsStatisticsVO getCompanyRelationsStatistics(String seafarerId);
    
    /**
     * 获取船员公司履历详细列表
     * @param seafarerId 船员ID
     * @param companyTypes 公司类型数组
     * @return 公司履历详细列表
     */
    List<CompanyRelationsDetailVO> getCompanyRelationsList(String seafarerId, List<String> companyTypes);
    
    /**
     * 获取公司类型字典
     * @return 公司类型列表
     */
    List<CompanyTypeVO> getCompanyTypes();
} 