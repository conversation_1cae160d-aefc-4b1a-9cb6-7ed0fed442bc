package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.DwdzCrewPositionService;
import com.js.hszpt.service.DwdzCrewPositionServiceService;
import com.js.hszpt.mapper.CrewAnnualReportDao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 船员职务履职统计API
 */
@Api(tags = "船员职务履职统计API")
@RestController
@RequestMapping("/api/seafarer/position-service")
public class SeafarerPositionServiceApi {

    @Autowired
    private DwdzCrewPositionServiceService positionService;
    
    @Autowired
    private CrewAnnualReportDao crewAnnualReportDao;

    //（生涯履历和移动端船员职务导航共用了这个接口，需要注意）
    @ApiOperation("根据船员ID查询职务履职统计")
    @GetMapping("/{crewId}")
    public Result<List<DwdzCrewPositionService>> getByCrewId(@PathVariable String crewId) {
        List<DwdzCrewPositionService> list = positionService.getByCrewId(crewId);
        return ResultUtil.data(list);
    }
    
    @ApiOperation("根据船员ID查询现任职务状态")
    @GetMapping("/current-status/{crewId}")
    public Result<Map<String, Object>> getCurrentStatus(@PathVariable String crewId) {
        Map<String, Object> currentStatus = crewAnnualReportDao.getCrewCurrentStatus(crewId);
        return ResultUtil.data(currentStatus);
    }
    
    @ApiOperation("根据完整职务名称查询晋升关系列表")
    @GetMapping("/promotion-relations")
    public Result<List<Map<String, Object>>> getPromotionRelations(@RequestParam String fullPositionName) {
        fullPositionName = fullPositionName.replaceAll("\\s+", "");
        List<Map<String, Object>> promotionRelations = crewAnnualReportDao.getPromotionRelationsByPosition(fullPositionName);
        return ResultUtil.data(promotionRelations);
    }
} 