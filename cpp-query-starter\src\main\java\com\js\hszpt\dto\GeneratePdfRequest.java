package com.js.hszpt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PDF生成请求DTO
 * @ClassName: GeneratePdfRequest
 * @Description: PDF生成请求DTO类
 * @author: System Generation
 */
@Data
@ApiModel(value = "PDF生成请求DTO")
public class GeneratePdfRequest {

    /**
     * 报告年份
     */
    @ApiModelProperty(value = "报告年份", required = true)
    private Integer year;

    /**
     * 船员ID
     */
    @ApiModelProperty(value = "船员ID(如果不传，则根据当前登录用户生成)")
    private String seafarerId;
} 