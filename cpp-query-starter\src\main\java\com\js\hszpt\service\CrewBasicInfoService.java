package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.hszpt.entity.CrewBasicInfo;
import com.js.hszpt.vo.CrewQueryVO;
import com.js.core.common.vo.Result;

import java.util.Map;
import java.util.List;

public interface CrewBasicInfoService extends IService<CrewBasicInfo> {
    /**
     * 分页查询船员列表
     */
    Result getCrewList(CrewQueryVO queryVO);
    
    /**
     * 获取特征标签列表
     */
    Result getFeatureTags();
    
    /**
     * 获取港口列表
     */
    Result getPorts(String query);
    
    /**
     * 获取船名列表
     */
    Result getShips(String query);
    
    /**
     * 获取当前职务列表
     */
    Result getPositions(String query);
    
    /**
     * 获取船员基本信息
     */
    Map<String, Object> getSeafarerBasicInfo(String seafarerId);

    /**
     * 获取船员身份证号明文
     * @param seafarerId 船员ID
     * @return 身份证号原文，未查询到返回null
     */
    String getSeafarerIdNumberPlain(String seafarerId);
    
    /**
     * 获取船员特征标签数据
     */
    Map<String, List<Map<String, Object>>> getSeafarerFeatureTags(String crewId);
    
    /**
     * 根据身份证号码获取船员照片（base64格式）
     */
    String getSeafarerPhoto(String idcardNo);
    
    /**
     * 插入船员照片到数据库
     * @param idcardNo 身份证号码
     * @param filePath 本地图片文件路径
     * @return 是否插入成功
     */
    boolean insertSeafarerPhoto(String idcardNo, String filePath);

    /**
     * 导出船员列表为xlsx（按查询条件，不分页）
     * @param queryVO 查询条件
     * @return xlsx二进制字节数组
     */
    byte[] exportCrewListXlsx(CrewQueryVO queryVO);
} 