<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CrewPositionCategoryMapper">
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        category_id, position_code, position_name, position_level, total_count, 
        position_description, rec_create_date, rec_modify_date, sort
    </sql>

    <!-- 查询所有职务分类列表（按排序字段排序） -->
    <select id="selectAllOrderBySort" resultType="com.js.hszpt.entity.CrewPositionCategory">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_position_category
        ORDER BY sort ASC, rec_create_date DESC
    </select>

</mapper> 