package com.js.hszpt.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.js.hszpt.mapper.DwdzCppPositionHierarchyDao;
import com.js.hszpt.service.SeafarerIndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 船员首页服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@DS("dzzzdwdz")
public class SeafarerIndexServiceImpl implements SeafarerIndexService {

    @Autowired
    private DwdzCppPositionHierarchyDao positionHierarchyDao;

    @Override
    public Map<String, Object> getSeafarerCount() {
        log.info("开始获取船员总数统计");
        
        try {
            // 从数据库获取船员总数
            Long totalCount = positionHierarchyDao.getSeafarerTotalCount();
            
            // 如果数据库没有数据，返回默认值
            if (totalCount == null) {
                totalCount = 0L;
                log.warn("数据库中没有船员数据，使用默认值：{}", totalCount);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            log.info("获取船员总数统计成功，总数：{}", totalCount);
            return result;
            
        } catch (Exception e) {
            log.error("获取船员总数统计失败：{}", e.getMessage(), e);
            
            // 异常情况下返回默认数据
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", 0l);
            result.put("lastUpdateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.put("error", "数据获取异常，显示默认数据");
            return result;
        }
    }
} 