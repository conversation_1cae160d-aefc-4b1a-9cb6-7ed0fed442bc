<?xml version="1.0" encoding="UTF-8" ?>
<testsuite tests="0" failures="0" name="com.js.hszpt.test.CertificateServiceTest" time="0" errors="0" skipped="0">
  <properties>
    <property name="idea.version" value="2024.3"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="sun.boot.library.path" value="D:\environment\Java\jdk1.8.0_211\jre\bin"/>
    <property name="java.vm.version" value="25.211-b12"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="maven.multiModuleProjectDirectory" value="E:\project\IdeaProjects\cpp_src\cpp-query-starter"/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="path.separator" value=";"/>
    <property name="guice.disable.misplaced.annotation.check" value="true"/>
    <property name="java.vm.name" value="Java HotSpot(TM) Client VM"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="user.script" value=""/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="user.dir" value="E:\project\IdeaProjects\cpp_src\cpp-query-starter"/>
    <property name="java.runtime.version" value="1.8.0_211-b12"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="java.endorsed.dirs" value="D:\environment\Java\jdk1.8.0_211\jre\lib\endorsed"/>
    <property name="os.arch" value="x86"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="line.separator" value="
"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="user.variant" value=""/>
    <property name="os.name" value="Windows 10"/>
    <property name="maven.ext.class.path" value="D:\soft\JetBrains\IntelliJ IDEA 2024.3\plugins\maven\lib\maven-event-listener.jar"/>
    <property name="classworlds.conf" value="D:\environment\apache-maven-3.5.3\bin\m2.conf"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\environment\Java\jdk1.8.0_211\bin;C:\windows\Sun\Java\bin;C:\windows\system32;C:\windows;d:\soft\cursor\resources\app\bin;d:\soft\Cursor\resources\app\bin;d:\soft\Cursor\resources\app\bin;d:\soft\Cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\WINDOWS.X64_193000_db_home\bin;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\dotnet\;D:\environment\Java\jdk-1.8\bin;D:\environment\Java\jdk-1.8\jre_1.8\bin;D:\environment\apache-maven-3.5.3\bin;D:\soft\TortoiseGit\bin;D:\soft\mysql-5.7.24-winx64\bin;D:\soft\Tencent\微信web开发者工具\dll;D:\env;ronment\ffmpeg-master-latest-win64-gpl-shared\bin;C:\Program Files\Docker\Docker\resources\bin;D:\soft\Git\cmd;D:\environment\nodejs;D:\environment\nodejs\node_gobal;D:\environment\nvm;D:\nvm4w\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\soft\JetBrains\IntelliJ IDEA 2024.3\bin;D:\environment\nodejs\node_gobal\node_modules;D:\environment\nvm;D:\nvm4w\nodejs;D:\soft\cursor\resources\app\bi;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\soft\JetBrains\IntelliJ IDEA 2024.3\bin;;node_gobal\node_modules;D:\soft\Microsoft VS Code\bin;D:\environment\nvm;D:\nvm4w\nodejs;D:\soft\cursor\resources\app\bin;."/>
    <property name="jansi.passthrough" value="true"/>
    <property name="maven.conf" value="D:\environment\apache-maven-3.5.3/conf"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.class.version" value="52.0"/>
    <property name="sun.management.compiler" value="HotSpot Client Compiler"/>
    <property name="maven.repo.local" value="D:\environment\repository"/>
    <property name="os.version" value="10.0"/>
    <property name="user.home" value="C:\Users\<USER>\environment\apache-maven-3.5.3\boot\plexus-classworlds-2.5.2.jar;D:\soft\JetBrains\IntelliJ IDEA 2024.3\lib\idea_rt.jar"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="sun.arch.data.model" value="32"/>
    <property name="java.home" value="D:\environment\Java\jdk1.8.0_211\jre"/>
    <property name="sun.java.command" value="org.codehaus.classworlds.Launcher -Didea.version=2024.3 -s D:\environment\apache-maven-3.5.3\conf\settings-jsfw.xml -Dmaven.repo.local=D:\environment\repository package"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="user.language" value="zh"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.version" value="1.8.0_211"/>
    <property name="java.ext.dirs" value="D:\environment\Java\jdk1.8.0_211\jre\lib\ext;C:\windows\Sun\Java\lib\ext"/>
    <property name="sun.boot.class.path" value="D:\environment\Java\jdk1.8.0_211\jre\lib\resources.jar;D:\environment\Java\jdk1.8.0_211\jre\lib\rt.jar;D:\environment\Java\jdk1.8.0_211\jre\lib\sunrsasign.jar;D:\environment\Java\jdk1.8.0_211\jre\lib\jsse.jar;D:\environment\Java\jdk1.8.0_211\jre\lib\jce.jar;D:\environment\Java\jdk1.8.0_211\jre\lib\charsets.jar;D:\environment\Java\jdk1.8.0_211\jre\lib\jfr.jar;D:\environment\Java\jdk1.8.0_211\jre\classes"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="maven.home" value="D:\environment\apache-maven-3.5.3"/>
    <property name="file.separator" value="\"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="sun.desktop" value="windows"/>
    <property name="sun.cpu.isalist" value="pentium_pro+mmx pentium_pro pentium+mmx pentium i486 i386 i86"/>
  </properties>
</testsuite>