package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.CrewEmployment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 船员任职关系信息Mapper接口
 */
@Mapper
public interface CrewEmploymentMapper extends BaseMapper<CrewEmployment> {
    
    /**
     * 根据船员ID查询任职关系
     * @param crewId 船员ID
     * @return 任职关系列表
     */
    List<CrewEmployment> selectByCrewId(@Param("crewId") String crewId);
    
    /**
     * 获取船员公司关系统计信息
     * @param crewId 船员ID
     * @return 统计信息
     */
    Map<String, Object> getCompanyRelationsStatistics(@Param("crewId") String crewId);
    
    /**
     * 根据船员ID和公司类型查询任职关系
     * @param crewId 船员ID
     * @param companyTypes 公司类型数组
     * @return 任职关系列表
     */
    List<CrewEmployment> selectByCrewIdAndCompanyTypes(@Param("crewId") String crewId, 
                                                       @Param("companyTypes") List<String> companyTypes);
} 