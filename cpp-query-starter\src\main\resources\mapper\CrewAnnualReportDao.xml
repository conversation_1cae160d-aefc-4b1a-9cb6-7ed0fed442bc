<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CrewAnnualReportDao">

    <!-- 年度报告结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.CrewAnnualReport">
        <id column="report_id" property="reportId" jdbcType="VARCHAR"/>
        <result column="crew_id" property="crewId" jdbcType="VARCHAR"/>
        <result column="report_year" property="reportYear" jdbcType="CHAR"/>
        <result column="illegal_score_total" property="illegalScoreTotal" jdbcType="DECIMAL"/>
        <result column="deducted_score" property="deductedScore" jdbcType="DECIMAL"/>
        <result column="clear_date" property="clearDateStr" jdbcType="VARCHAR"/>
        <result column="working_days_total" property="workingDaysTotal" jdbcType="INTEGER"/>
        <result column="unlimited_working_days" property="unlimitedWorkingDays" jdbcType="INTEGER"/>
        <result column="coastal_working_days" property="coastalWorkingDays" jdbcType="INTEGER"/>
        <result column="onboard_flag" property="onboardFlag" jdbcType="BOOLEAN"/>
        <result column="ship_name" property="shipName" jdbcType="VARCHAR"/>
        <result column="duty_start_date" property="dutyStartDateStr" jdbcType="VARCHAR"/>
        <result column="duty_location" property="dutyLocation" jdbcType="VARCHAR"/>
        <result column="duty_position" property="dutyPosition" jdbcType="VARCHAR"/>
        <result column="rec_create_date" property="recCreateDateStr" jdbcType="VARCHAR"/>
        <result column="rec_modify_date" property="recModifyDateStr" jdbcType="VARCHAR"/>
        <result column="mobile_push_status" property="mobilePushStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        report_id, crew_id, report_year, illegal_score_total, deducted_score,
        COALESCE(clear_date::text, '') as "clear_date",
        working_days_total, unlimited_working_days, coastal_working_days, onboard_flag,
        ship_name,
        COALESCE(duty_start_date::text, '') as "duty_start_date",
        duty_location, duty_position,
        COALESCE(rec_create_date::text, '') as "rec_create_date",
        COALESCE(rec_modify_date::text, '') as "rec_modify_date"
    </sql>

    <!-- 获取可查询的年份列表 -->
    <select id="getAvailableYears" resultType="String">
        SELECT DISTINCT report_year
        FROM dwdz_crew_ar_annual_report
        WHERE report_year IS NOT NULL
        ORDER BY report_year DESC
        LIMIT 10
    </select>

    <!-- 根据船员ID和年份查询年度报告 -->
    <select id="getByCrewIdAndYear" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_ar_annual_report
        WHERE crew_id = #{crewId}
        AND report_year = #{reportYear}
        LIMIT 1
    </select>

    <!-- 根据船员ID查询基本信息 -->
    <select id="getCrewBasicInfo" resultType="map">
        SELECT
            COALESCE(u.full_name, '船员' || '1') as "name",
            COALESCE(u.id_number, '***') as "idNumber",
            COALESCE(u.photo_url, '') as "avatar"
        FROM dwdz_crew_basic_info u
        WHERE u.crew_id = #{crewId}
        LIMIT 1
    </select>

    <!-- 获取船舶详细信息 -->
    <select id="getShipDetailInfo" resultType="map">
        SELECT
            COALESCE(r.ship_name, '未知') as "shipName",
            '散货船' as "shipType",
            '50000' as "grossTonnage",
            COALESCE(r.duty_position, '未知') as "position",
            COALESCE(r.duty_start_date::text, '未知') as "boardingDate",
            COALESCE(r.duty_location, '未知') as "departurePort",
            '未知' as "destinationPort",
            CASE
                WHEN r.onboard_flag = true THEN '在船'
                ELSE '不在船'
            END as "currentStatus"
        FROM dwdz_crew_ar_annual_report r
        WHERE r.report_id = #{reportId}
        LIMIT 1
    </select>

    <!-- 根据身份证号查询船员ID -->
    <select id="getCrewIdByIdNumber" resultType="String">
        SELECT crew_id
        FROM dwdz_crew_basic_info
        WHERE id_number = #{idNumber}
        LIMIT 1
    </select>

    <!-- 根据船员ID查询当前状态信息 -->
    <select id="getCrewCurrentStatus" resultType="map">
        SELECT
            COALESCE(s.onboard_status, '未知') as "currentStatus",
            COALESCE(s.ship_name, '未知') as "shipName",
            COALESCE(s.ship_type, '未知') as "shipType",
            '50000' as "grossTonnage",
            COALESCE(s.position, '未知') as "position",
            COALESCE(s.onboard_date::text, '未知') as "boardingDate",
            COALESCE(s.home_port, s.current_location, '未知') as "departurePort",
            '未知' as "destinationPort",
            CASE s.navi_area WHEN 'unlimited' THEN '无限航区' WHEN 'coastal' THEN '沿海航区' ELSE '' END AS "naviArea",
            COALESCE(s.comp_level_name, ' ') as "compLevelName"
        FROM dwdz_crew_current_status s
        WHERE s.crew_id = #{crewId}
        LIMIT 1
    </select>
    
    <!-- 根据完整职务名称查询晋升关系列表 -->
    <select id="getPromotionRelationsByPosition" resultType="map">
        SELECT 
            rel.promotion_relation_id as "promotionRelationId",
            rel.relation_type as "relationType",
            rel.sqxszsmc as "sqxszsmc",
            rel.person_count as "personCount",
            rel.sort_order as "sortOrder",
            rel.dq_subcategory_id as "dqSubcategoryId",
            rel.mb_subcategory_id as "mbSubcategoryId",
            mb_sub.flxxmc as "mbSubcategoryName",
            mb_sub.subcategory_id  as "relatedSubcategoryId"
        FROM dwdz_crew_position_promo_rel rel
        LEFT JOIN dwdz_crew_position_subcategory mb_sub ON rel.mb_subcategory_id = mb_sub.subcategory_id and mb_sub.node_type = '0'
        WHERE (rel.dq_subcategory_id = (select subcategory_id from dwdz_crew_position_subcategory p where p.flxxmc = #{fullPositionName}) )
        and exists(select 1 from dwdz_crew_position_subcategory sub1 where sub1.subcategory_id = rel.mb_subcategory_id and sub1.node_type = '0' )
        ORDER BY rel.sort_order ASC, rel.relation_type, rel.person_count DESC
    </select>
    <select id="getPushMessageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_ar_annual_report
        WHERE mobile_push_status = '0'
        OR (mobile_push_status = '2' AND rec_modify_date &gt; NOW() - INTERVAL '10 minutes')
        ORDER BY rec_create_date DESC
        LIMIT 1000
    </select>

</mapper> 