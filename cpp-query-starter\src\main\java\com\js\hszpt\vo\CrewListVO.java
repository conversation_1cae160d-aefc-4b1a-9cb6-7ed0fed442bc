package com.js.hszpt.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员列表返回数据VO
 */
@Data
public class CrewListVO {
    // 基本信息
    private String crewId; // 船员ID
    private String fullName; // 姓名
    private String idNumber; // 身份证号
    private String phone; // 手机号码
    private String gender; // 性别
    private Date birthDate; // 出生日期
    private String nativePlace; // 籍贯
    private String education; // 学历
    private String photoUrl; // 头像URL

    // 当前状态信息
    private String currentStatus; // 当前身份
    private String onboardStatus; // 在船状态
    private String shipName; // 当前服务船名
    private String shipNameEn; // 英文船名
    private String shipImo; // 船舶IMO
    private String shipMmsi; // 船舶MMSI
    private String shipType; // 船舶类型
    private String homePort; // 船籍港
    private String position; // 当前职务
    private String positionLevel; // 当前职务等级
    private String naviArea; // 当前职务航区
    private String currentLocation; // 当前位置
    private Date onboardDate; // 上船日期
    private Date positionStartDate; // 任职开始日期

    // 公司信息
    private String dispatchCompanyName; // 外派公司名称
    private String shipManagementCompany; // 船舶管理公司
    private String shipOwnerCompany; // 船东公司
    private String shipOperatorCompany; // 船舶经营公司

    // 辖区信息
    private String registryPortAuthority; // 船籍港所属海事辖区
    private String dispatchCompanyRegion; // 外派公司所属辖区
    private String shipOwnerRegion; // 船东公司所属辖区
    private String managementCompanyRegion; // 船舶管理公司所属辖区
    private String operatorRegion; // 船舶经营公司所属辖区
    private String dispatchAgentRegion; // 所属派遣代办公司的所在辖区

    // 证书信息
    private String certificateType; // 证书类型
    private String certificateNumber; // 证书号码

    // 其他信息
    private Boolean isDispatched; // 是否外派船员
    private Date updateTime; // 更新时间
    private String featureTags; // 聚合后的现场监管标签名称列表（逗号分隔）
    private Integer siteTagCount; // 现场监管标签数量（由SQL子查询site_tag_count映射）
    private Integer positiveTagCount; // 正向标签数量（由SQL子查询positive_tag_count映射）
    private Integer hasCompetencyCert; // 是否持海船船员适任证书（1-是，0-否），用于控制生涯履历和年度报告按钮展示
    private Long totalCount; // 查询结果总数（由窗口函数COUNT(*) OVER()映射）

    // 新增：导出用的标签字符串字段
    private String siteTagsStr; // 现场监管标签字符串（逗号分隔）
    private String identityTagsStr; // 身份标签字符串（逗号分隔）
    private String statusTagsStr; // 状态标签字符串（逗号分隔）
}