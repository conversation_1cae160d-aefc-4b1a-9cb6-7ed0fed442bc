package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 职务分类表实体类
 * 用于管理船员职务分类信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_position_category")
public class DwdzCppPositionCategory {

    /**
     * 分类唯一标识ID（主键）
     */
    @TableId(value = "category_id", type = IdType.ASSIGN_UUID)
    private String categoryId;

    /**
     * 标准化职位编码
     */
    @TableField("position_code")
    private String positionCode;

    /**
     * 职务名称
     */
    @TableField("position_name")
    private String positionName;

    /**
     * 职务级别（management/operational/support）
     */
    @TableField("position_level")
    private String positionLevel;

    /**
     * 该职务总人数
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 职务描述
     */
    @TableField("position_description")
    private String positionDescription;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    /**
     * 子分类列表（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private List<DwdzCppPositionSubcategory> subcategories;
} 