package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员职业生涯汇总信息表
 * <AUTHOR> Generation
 */
@Data
@TableName("dwdz_crew_summary")
@ApiModel(value = "船员职业生涯汇总信息表")
public class DwdzCrewSummary {

    private static final long serialVersionUID = 1L;

    /**
     * 职业生涯汇总记录唯一标识
     */
    @TableId(value = "summary_id")
    @ApiModelProperty(value = "职业生涯汇总记录唯一标识")
    private String summaryId;

    /**
     * 关联船员基本信息表的船员ID
     */
    @ApiModelProperty(value = "关联船员基本信息表的船员ID")
    private String crewId;

    /**
     * 从业起始年份（从2018年起计算）
     */
    @ApiModelProperty(value = "从业起始年份（从2018年起计算）")
    private Integer careerStartYear;

    /**
     * 从业年数（从2018年起计算）
     */
    @ApiModelProperty(value = "从业年数（从2018年起计算）")
    private Integer careerYears;

    /**
     * 是否展示从业年数（true:展示2018年后的从业年数, false:不展示）
     */
    @ApiModelProperty(value = "是否展示从业年数")
    private Boolean showCareerFlag;

    /**
     * 首次取得职务证书的日期
     */
    @ApiModelProperty(value = "首次取得职务证书的日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date firstOfficerDate;

    /**
     * 晋升平均时长（单位：年，保留一位小数）
     */
    @ApiModelProperty(value = "晋升平均时长（单位：年，保留一位小数）")
    private BigDecimal promotionDuration;

    /**
     * 超过同龄人百分比（如0.82表示超过82%的同龄船员）
     */
    @ApiModelProperty(value = "超过同龄人百分比")
    private BigDecimal peerPercentile;

    /**
     * 船员历史合作过的所有公司数量总和（整数计数）
     */
    @ApiModelProperty(value = "船员历史合作过的所有公司数量总和")
    private Integer totalCooperationCompanies;

    /**
     * 船员当前仍在合作的活跃公司数量（整数计数）
     */
    @ApiModelProperty(value = "船员当前仍在合作的活跃公司数量")
    private Integer currentCooperationCompanies;

    /**
     * 记录创建日期
     */
    @ApiModelProperty(value = "记录创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @ApiModelProperty(value = "记录修改日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recModifyDate;
} 