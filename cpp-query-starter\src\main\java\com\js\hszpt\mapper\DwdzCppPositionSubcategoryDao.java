package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCppPositionSubcategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 职务子类表DAO接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCppPositionSubcategoryDao extends BaseMapper<DwdzCppPositionSubcategory> {

    /**
     * 根据分类ID获取子分类列表
     */
    List<DwdzCppPositionSubcategory> getSubcategoriesByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 根据子分类名称获取子分类信息
     */
    DwdzCppPositionSubcategory getSubcategoryByName(@Param("subcategoryName") String subcategoryName);
} 