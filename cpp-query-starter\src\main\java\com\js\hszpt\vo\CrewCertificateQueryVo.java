package com.js.hszpt.vo;

import lombok.Data;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class CrewCertificateQueryVo {
    private String serialNo; // 序号
    private String dataId; // 证照数据主键
    private String certificateId; // 电子证照业务标识码
    private String certificateType; // 证照类型名称
    private String certificateTypeCode; // 证照类型代码
    private String certificateDefineAuthorityName; // 证照定义机构
    private String certificateDefineAuthorityCode; // 证照定义机构代码
    private String relatedItemName; // 关联事项名称
    private String relatedItemCode; // 关联事项编码
    private String certificateHolderCategory; // 持证主体类别
    private String nationality; // 国籍/持证主体类别名称
    private String validityRange; // 有效期限范围
    private String certificateIdentifier; // 电子证照唯一标识码
    private String certificateName; // 证照名称
    private String certificateNumber; // 证书编号
    private String issueType; // 签发机关/证照颁发机构
    private String certificateIssuingAuthorityCode; // 证照颁发机构代码
    private String issueDate; // 签发日期
    private String holderName; // 持证人姓名
    private String holderCode; // 身份证号/持证主体代码
    private String certificateHolderTypeName; // 持证主体代码类型
    private String effectiveDate; // 生效日期
    private String expiryDate; // 截止日期
    private String trainingOrg; // 培训机构/issue_dept_code2
    private String foreignOrg; // 外派机构/issue_dept_code3
    private String certificateAreaCode; // 证照所属地区编码
    private String certificateStatus; // 证照状态
    private String creatorId; // 登记人员ID
    private String createTime; // 创建时间
    private String operatorId; // 最后操作人
    private String updateTime; // 修改时间
    private String filePath; // 文件保存位置
    private String syncStatus; // 同步状态
    private String remarks; // 备注
    private String deptId; // 登记部门
    private String applyNum; // 申请编号
    private String affairType; // 事项类型
    private String serveBusiness; // 服务对象
    private String affairId; // 事项ID
    private String affairNum; // 事项编号
    private String sortName; // 归档编号
    private String sourceCode; // 源系统代码
    private Date recCreateDate; // 记录创建日期
    private Date recModifyDate; // 记录修改日期
    private String msaOrgCode; // 数据归属机构代码
    private String birthDate; // 出生年月日
    private String nameEn; // 持证人姓名-英文
    private String countryCn; // 国籍-中文
    private String countryEn; // 国籍-英文
    private String signDeptEn; // 发证机关(英文)
    private String applivationsCn; // 适用航线-中文
    private String crewType; // 职务资格
    private String certificateNameEn; // 证照名称(英文)
    private String certificateStatusEn; // 证照状态(英文)
    private String certificateCnEn; // 证照中英文类型
    private String condition; // 查询条件

    /**
     * 培训项目名称（多个项目以##分隔）
     */
    private String projectNames;

    /**
     * 培训签发日期
     */
    private String projectIssuedDate;

    /**
     * 培训有效期至
     */
    private String projectExpiringDate;

    /**
     * 展示字段列表
     */
    private List<Map<String, Object>> displayFields;

    /**
     * 持证人姓名
     */
    private String certificateHolderName; // 持证人姓名

    /**
     * 出生日期
     */
    private String birth; // 出生日期

    /**
     * 证书签发日期
     */
    private String certificateIssuedDate; // 证书签发日期

    /**
     * 证书到期日期
     */
    private String certificateExpiringDate; // 证书到期日期

    /**
     * 证书颁发机构名称
     */
    private String certificateIssuingAuthorityName; // 证书颁发机构名称

    /**
     * 证照类型名称
     */
    private String certificateTypeName;

    /**
     * 证书印刷号
     */
    private String certPrintNo;

    private String certificateEffectiveDateEn; // 证照有效期开始日期（英文）
    private String certificateExpiringDateEn; // 证照有效期截止日期（英文）
    private String certificateIssuedDateEn; // 证照颁发日期（英文）
    private String crewTypeEn; // 职务(英文)
    private String applivationsEn; // 适用航线-英文
    private String authAuthorityCn; // 授权机关（中文）
    private String authAuthorityEn; // 授权机关（英文）
    private String evaOrgCn; // 审核机构（中文）
    private String evaOrgEn; // 审核机构（英文）
    private String trainManagerNameCn; // 培训主管姓名（中文）
    private String trainManagerNameEn; // 培训主管姓名（英文）
    private String representativeCn; // 法定代表人姓名（中文）
    private String representativeEn; // 法定代表人姓名（英文）
    private String trainingName; // 培训项目（中文）
    private String trainingNameEn; // 培训项目（英文）
    private String trainingDateOfIssue; // 培训项目签发日期（中文）
    private String trainingDateOfIssueEn; // 培训项目签发日期（英文）
    private String trainingEffectiveDate; // 培训项目有效期至（中文）
    private String trainingEffectiveDateEn; // 培训项目有效期至（英文）
    
    /**
     * 出生日期英文
     */
    private String birthEn; // 出生日期        
    
    private String trainingInstitutionCode; // 机构代码
    
    private String trainingLocation; // 培训地点
}
