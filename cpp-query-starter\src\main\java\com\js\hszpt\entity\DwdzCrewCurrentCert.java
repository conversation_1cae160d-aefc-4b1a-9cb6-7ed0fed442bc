package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 船员当前持证情况表
 * 
 * @ClassName: DwdzCrewCurrentCert
 * @Description: 船员当前持证情况表实体类
 * @author: System Generation
 */
@Data
@TableName("dwdz_crew_current_cert")
public class DwdzCrewCurrentCert {

    private static final long serialVersionUID = 1L;

    /**
     * 证书ID
     */
    @TableId("cert_id")
    private String certId;

    /**
     * 船员ID
     */
    @TableField("crew_id")
    private String crewId;

    /**
     * 证书类别(COMPETENCE:适任证书/QUALIFICATION:培训合格证/HEALTH:健康证)
     */
    @TableField("cert_category")
    private String certCategory;

    /**
     * 证书名称
     */
    @TableField("cert_name")
    private String certName;

    /**
     * 证书编号
     */
    @TableField("cert_number")
    private String certNumber;

    /**
     * 发证日期
     */
    @TableField("issue_date")
    private Date issueDate;

    /**
     * 有效期至
     */
    @TableField("expiry_date")
    private Date expiryDate;

    /**
     * 证书状态(VALID:有效/INVALID:无效)
     */
    @TableField("cert_status")
    private String certStatus;

    /**
     * 航区 unlimited-无限航区 coastal-沿海航区
     */
    @TableField("navi_area")
    private String naviArea;

    /**
     * 是否即将失效(三个月内)
     */
    @TableField("expiring_flag")
    private Boolean expiringFlag;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    /**
     * 当前职务等级 FIRST_CLASS(一等), SECOND_CLASS(二等), THIRD_CLASS(三等)
     */
    @TableField("position_level")
    private String positionLevel;

    /**
     * 适任职务代码
     */
    @TableField("competence_position_code")
    private String competencePositionCode;

    /**
     * 适任职务名称
     */
    @TableField("competence_position_name")
    private String competencePositionName;
}