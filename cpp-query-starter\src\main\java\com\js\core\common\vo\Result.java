package com.js.core.common.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
 

/**
 * 
 * @ClassName:  Result   
 * @Description:TODO(前后端交互数据标准)   
 * @author:  liny 
 * @date:   May 8, 2019 8:05:19 PM   
 * @param <T>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 成功标志
     */
    private boolean success;

    /**
     * 失败消息
     */
    private String message;

    /**
     * 返回代码
     */
    private Integer code;

    /**
     * 时间戳
     */
    private long timestamp = System.currentTimeMillis();

    /**
     * 结果对象
     */
    private T result;
    

 
    public Result<T> setData(T t){
    	Result<T> result = new Result<>();
        result.setResult(t);
        result.setSuccess(true);
        result.setCode(200);
        return result;
    }

    public Result<T> setSuccessMsg(String msg){
    	Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setMessage(msg);
        result.setCode(200);
        result.setResult(null);
        return result;
    }

    public Result<T> setData(T t, String msg){
    	Result<T> result = new Result<>();
    	result.setSuccess(true);
        result.setCode(200);
        result.setResult(t);
        result.setMessage(msg);
        return result;
    }

    public Result<T> setErrorMsg(String msg){
    	Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setMessage(msg);
        result.setCode(500);
        return result;
    }

    public Result<T> setErrorMsg(Integer code, String msg){
    	Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setMessage(msg);
        result.setCode(code);
        return result;
    }
    
    public static <T> Result<T> success(T t){
        return new Result<T>().setData(t);
    }

    public static <T> Result<T> success(T t, String msg){
        return new Result<T>().setData(t, msg);
    }
    
    public static <T> Result<T> success(String msg){
        return new Result<T>().setSuccessMsg(msg);
    }

    public static <T> Result<T> failed(String msg){
        return new Result<T>().setErrorMsg(msg);
    }

    public static <T> Result<T> failed(Integer code, String msg){
        return new Result<T>().setErrorMsg(code, msg);
    }
}
