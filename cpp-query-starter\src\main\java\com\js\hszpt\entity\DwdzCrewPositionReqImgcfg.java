package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 晋升要求总览图片配置表实体类
 * 用于管理晋升要求总览中的图片配置信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_position_req_imgcfg")
public class DwdzCrewPositionReqImgcfg {

    /**
     * 节点唯一标识ID（主键）
     */
    @TableId(value = "cfg_id", type = IdType.ASSIGN_UUID)
    private String cfgId;

    /**
     * 文件实际访问名称
     */
    @TableField("visit_file_name")
    private String visitFileName;

    /**
     * 法规系统原始文件id
     */
    @TableField("source_file_id")
    private String sourceFileId;

    /**
     * 外网访问路径
     */
    @TableField("outside_url")
    private String outsideUrl;

    /**
     * 内网访问路径
     */
    @TableField("intranet_url")
    private String intranetUrl;

    /**
     * 配置说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
}
