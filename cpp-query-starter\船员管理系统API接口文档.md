# 船员管理系统API接口文档

## 基础信息

- **服务地址**: `http://localhost:8285`
- **API前缀**: `/api/v1/seafarer`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **接口总数**: 15个

## 通用响应格式

所有接口都遵循统一的响应格式：

```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965003230,
  "result": {
    // 具体业务数据
  }
}
```

**响应字段说明:**
- `success`: 请求是否成功 (boolean)
- `message`: 响应消息 (string)
- `code`: 状态码 (number)
- `timestamp`: 时间戳 (number)
- `result`: 业务数据 (object)

---

## 1. 首页相关接口

### 1.1 获取船员总数

**接口地址**: `GET /api/v1/seafarer/index/seafarer-count`

**功能说明**: 获取船员总数统计信息

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965003230,
  "result": {
    "totalCount": 2080000,
    "lastUpdateTime": "2025-06-15 13:23:23"
  }
}
```

**响应字段说明**:
- `totalCount`: 船员总数 (number)
- `lastUpdateTime`: 最后更新时间 (string)

---

## 2. 船员职业结构相关接口

### 2.1 获取职业路径数据

**接口地址**: `GET /api/v1/seafarer/career/paths`

**功能说明**: 获取所有职业晋升路径配置数据

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965003516,
  "result": {
    "deck1": {
      "path": ["able-seaman", "senior-seaman", "third-officer", "second-officer", "chief-officer", "captain"],
      "color": "#10b981",
      "name": "甲板部通道1 (值班水手直升)",
      "description": "promotion"
    },
    "deck2": {
      "path": ["able-seaman", "third-officer", "second-officer", "chief-officer", "captain"],
      "color": "#16a085",
      "name": "甲板部通道2 (高级水手)",
      "description": "promotion"
    },
    "engine1": {
      "path": ["able-engineer", "senior-engineer", "third-engineer", "second-engineer", "chief-engineer-officer", "chief-engineer"],
      "color": "#8b5cf6",
      "name": "轮机部通道1 (值班机工直升)",
      "description": "promotion"
    },
    "engine2": {
      "path": ["able-engineer", "third-engineer", "second-engineer", "chief-engineer-officer", "chief-engineer"],
      "color": "#7c3aed",
      "name": "轮机部通道2 (高级机工)",
      "description": "promotion"
    },
    "electrical": {
      "path": ["electrical-technician", "electrical-officer"],
      "color": "#f59e0b",
      "name": "电子电气通道",
      "description": "promotion"
    },
    "radio": {
      "path": ["limited-operator", "general-operator", "radio-operator-2", "radio-operator-1"],
      "color": "#06b6d4",
      "name": "无线电操作通道",
      "description": "promotion"
    },
    "management": {
      "path": ["position1", "position2"],
      "color": "#ffd700",
      "name": "船舶管理关系",
      "description": "management"
    }
  }
}
```

**响应字段说明**:
- `path`: 晋升路径职位序列 (array)
- `color`: 通道显示颜色 (string)
- `name`: 通道名称 (string)
- `description`: 通道类型描述 (string)

### 2.2 获取管理关系数据

**接口地址**: `GET /api/v1/seafarer/career/management-relations`

**功能说明**: 获取职位间的管理关系配置

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965003887,
  "result": {
    "deck_management": {
      "color": "#ffd700",
      "name": "甲板部管理关系",
      "connections": [
        {
          "from": "captain",
          "to": "chief-officer"
        },
        {
          "from": "chief-officer",
          "to": "second-officer"
        },
        {
          "from": "second-officer",
          "to": "third-officer"
        }
      ]
    },
    "engine_management": {
      "color": "#ff6b6b",
      "name": "轮机部管理关系",
      "connections": [
        {
          "from": "chief-engineer",
          "to": "chief-engineer-officer"
        },
        {
          "from": "chief-engineer-officer",
          "to": "second-engineer"
        },
        {
          "from": "second-engineer",
          "to": "third-engineer"
        }
      ]
    }
  }
}
```

**响应字段说明**:
- `color`: 管理关系显示颜色 (string)
- `name`: 管理关系名称 (string)
- `connections`: 管理连接关系 (array)
  - `from`: 上级职位 (string)
  - `to`: 下级职位 (string)

### 2.3 获取职位详情

**接口地址**: `GET /api/v1/seafarer/career/position/{positionId}`

**功能说明**: 根据职位ID获取详细信息和子分类

**请求参数**:
- `positionId` (path): 职位ID，支持值：`captain`、`chief-engineer`、`chief-officer`、`unknown`

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965003887,
  "result": {
    "id": "captain",
    "name": "船长",
    "level": "management",
    "description": "船舶最高指挥官，负责船舶的安全航行和全面管理",
    "totalCount": 21600,
    "subcategories": [
      {
        "name": "无限航区船长",
        "count": 8500,
        "description": "可在全球任何海域担任船长职务"
      },
      {
        "name": "沿海航区船长",
        "count": 6200,
        "description": "限制在沿海航区担任船长职务"
      },
      {
        "name": "近洋航区船长",
        "count": 4800,
        "description": "限制在近洋航区担任船长职务"
      },
      {
        "name": "客船船长",
        "count": 2100,
        "description": "专门担任客船船长职务"
      }
    ]
  }
}
```

**响应字段说明**:
- `id`: 职位ID (string)
- `name`: 职位名称 (string)
- `level`: 职位级别 (string): `management`/`operational`/`support`/`unknown`
- `description`: 职位描述 (string)
- `totalCount`: 总人数 (number)
- `subcategories`: 子分类列表 (array)
  - `name`: 子分类名称 (string)
  - `count`: 子分类人数 (number)
  - `description`: 子分类描述 (string)

### 2.4 获取晋升关系数据

**接口地址**: `GET /api/v1/seafarer/career/promotion-relations/{subcategoryName}`

**功能说明**: 根据子分类名称获取晋升来源和目标关系

**请求参数**:
- `subcategoryName` (path): 子分类名称，如：`无限航区船长`、`沿海航区大副`、`轮机长`

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965005229,
  "result": {
    "upper": [
      {
        "name": "高级船长",
        "description": "基于丰富的海上服务资历",
        "type": "资历晋升"
      },
      {
        "name": "船队船长",
        "description": "晋升至管理岗位",
        "type": "管理晋升"
      }
    ],
    "lower": [
      {
        "name": "无限航区大副",
        "description": "通过正常晋升渠道",
        "type": "正常晋升"
      },
      {
        "name": "沿海航区大副",
        "description": "需要额外培训和考试",
        "type": "跨航区晋升"
      }
    ]
  }
}
```

**响应字段说明**:
- `upper`: 可晋升到的更高职位 (array)
- `lower`: 可从哪些职位晋升而来 (array)
  - `name`: 职位名称 (string)
  - `description`: 晋升描述 (string)
  - `type`: 晋升类型 (string)

### 2.5 获取晋升要求详情

**接口地址**: `GET /api/v1/seafarer/career/promotion-requirements/{targetPosition}`

**功能说明**: 根据目标职位获取详细晋升要求

**请求参数**:
- `targetPosition` (path): 目标职位名称，如：`captain`、`chief-engineer`、`chief-officer`

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965006135,
  "result": {
    "overview": [
      "具备相应的适任证书",
      "满足海上服务资历要求",
      "通过海事局适任考试",
      "身体健康，符合船员健康标准"
    ],
    "age": [
      "年满18周岁",
      "不超过65周岁",
      "初次申请年龄不超过60周岁"
    ],
    "health": [
      "持有有效的海员健康证明",
      "视力矫正后达到0.8以上",
      "听力正常，无色盲色弱",
      "无影响安全履职的疾病"
    ],
    "experience": [
      "担任大副职务满18个月",
      "其中至少12个月在相应航区",
      "累计海上服务资历不少于36个月",
      "近5年内海上服务资历不少于12个月"
    ],
    "training": [
      "完成船长岗位适任培训",
      "完成船舶保安员培训",
      "完成高级消防培训",
      "完成精通救生艇筏和救助艇培训"
    ],
    "certificates": [
      "船舶操纵模拟器培训合格证",
      "雷达观测与标绘培训合格证",
      "自动雷达标绘仪培训合格证",
      "全球海上遇险与安全系统培训合格证"
    ],
    "notes": [
      "需通过海事局理论和实操考试",
      "考试合格后颁发适任证书",
      "证书有效期为5年",
      "到期前需完成知识更新培训"
    ]
  }
}
```

**响应字段说明**:
- `overview`: 总览要求 (array)
- `age`: 年龄要求 (array)
- `health`: 健康要求 (array)
- `experience`: 资历要求 (array)
- `training`: 培训要求 (array)
- `certificates`: 证书要求 (array)
- `notes`: 注意事项 (array)

### 2.6 获取职务人数统计

**接口地址**: `GET /api/v1/seafarer/career/position-counts`

**功能说明**: 获取所有职务的人数统计映射

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965677301,
  "result": {
    "MGT-D-CAPT": 150,
    "MGT-E-ENG": 180,
    "MGT-D-CHFOFF": 320,
    "MGT-E-CHENG": 280,
    "OPT-D-2OFF": 450,
    "OPT-E-2ENG": 380,
    "OPT-D-3OFF": 560,
    "OPT-E-3ENG": 520,
    "OPT-EET-EO": 230,
    "OPT-R-1RO": 120,
    "OPT-R-2RO": 180,
    "OPT-R-GO": 340,
    "OPT-R-LO": 420,
    "SUP-D-AB": 1200,
    "SUP-D-SRAB": 800,
    "SUP-E-AB": 980,
    "SUP-E-SRAB": 650,
    "SUP-EET-ET": 450
  }
}
```

**响应字段说明**:
- 键：职务编码 (string)
- 值：人数统计 (number)

### 2.7 获取单个职务人数

**接口地址**: `GET /api/v1/seafarer/career/position-count/{positionId}`

**功能说明**: 根据职位ID获取具体人数

**请求参数**:
- `positionId` (path): 职位ID，如：`captain`、`chief-engineer`、`chief-officer`、`unknown`

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965677787,
  "result": {
    "positionId": "captain",
    "count": 150,
    "status": "default"
  }
}
```

**响应字段说明**:
- `positionId`: 职位ID (string)
- `count`: 人数 (number)
- `status`: 数据状态 (string): `success`/`default`/`error`

### 2.8 获取路径选择器选项

**接口地址**: `GET /api/v1/seafarer/career/path-selector-options`

**功能说明**: 获取路径选择器的所有可选项配置

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965678591,
  "result": {
    "departments": [
      {
        "value": "deck",
        "label": "甲板部"
      },
      {
        "value": "engine",
        "label": "轮机部"
      },
      {
        "value": "electrical",
        "label": "电子电气"
      },
      {
        "value": "radio",
        "label": "无线电"
      }
    ],
    "levels": [
      {
        "value": "management",
        "label": "管理级"
      },
      {
        "value": "operational",
        "label": "操作级"
      },
      {
        "value": "support",
        "label": "支持级"
      }
    ],
    "navigationAreas": [
      {
        "value": "unlimited",
        "label": "无限航区"
      },
      {
        "value": "coastal",
        "label": "沿海航区"
      },
      {
        "value": "near-ocean",
        "label": "近洋航区"
      },
      {
        "value": "inland",
        "label": "内河航区"
      }
    ],
    "shipTypes": [
      {
        "value": "cargo",
        "label": "货船"
      },
      {
        "value": "passenger",
        "label": "客船"
      },
      {
        "value": "tanker",
        "label": "油轮"
      },
      {
        "value": "container",
        "label": "集装箱船"
      },
      {
        "value": "bulk",
        "label": "散货船"
      }
    ],
    "certificateTypes": [
      {
        "value": "competency",
        "label": "适任证书"
      },
      {
        "value": "special",
        "label": "特殊培训"
      },
      {
        "value": "endorsement",
        "label": "海员证"
      },
      {
        "value": "health",
        "label": "健康证明"
      }
    ]
  }
}
```

**响应字段说明**:
- `departments`: 部门选项 (array)
- `levels`: 级别选项 (array)
- `navigationAreas`: 航区选项 (array)
- `shipTypes`: 船舶类型选项 (array)
- `certificateTypes`: 证书类型选项 (array)

每个选项包含：
- `value`: 选项值 (string)
- `label`: 显示标签 (string)

---

## 3. 证书路径相关接口

### 3.1 获取证书路径数据

**接口地址**: `GET /api/v1/seafarer/certificate/path/{certificateType}`

**功能说明**: 根据证书类型获取证书路径数据

**请求参数**:
- `certificateType` (path): 证书类型，支持值：`competency`、`special`、`endorsement`、`unknown`

**响应示例**:

**适任证书 (competency)**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965679154,
  "result": {
    "type": "适任证书",
    "description": "船员适任证书晋升路径",
    "paths": [
      {
        "department": "甲板部",
        "color": "#4CAF50",
        "certificates": ["三副", "二副", "大副", "船长"]
      },
      {
        "department": "轮机部",
        "color": "#FF9800",
        "certificates": ["三管轮", "二管轮", "大管轮", "轮机长"]
      }
    ]
  }
}
```

**特殊培训 (special)**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965679408,
  "result": {
    "type": "特殊培训",
    "description": "船员特殊培训证书路径",
    "trainings": [
      {
        "name": "基本安全培训",
        "color": "#2196F3",
        "requirement": "所有船员必须"
      },
      {
        "name": "精通救生艇筏",
        "color": "#9C27B0",
        "requirement": "高级船员必须"
      },
      {
        "name": "高级消防",
        "color": "#F44336",
        "requirement": "高级船员必须"
      },
      {
        "name": "医护培训",
        "color": "#00BCD4",
        "requirement": "船长大副必须"
      }
    ]
  }
}
```

**海员证 (endorsement)**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965679652,
  "result": {
    "type": "海员证",
    "description": "海员证申请和更新路径",
    "steps": [
      "提交申请材料",
      "体检合格",
      "背景调查",
      "颁发海员证",
      "定期更新"
    ]
  }
}
```

### 3.2 搜索证书节点

**接口地址**: `GET /api/v1/seafarer/certificate/search`

**功能说明**: 根据关键词搜索证书节点

**请求参数**:
- `keyword` (query): 搜索关键词 (必填)
- `limit` (query): 限制返回数量，默认20 (可选)

**请求示例**:
```
GET /api/v1/seafarer/certificate/search?keyword=船长&limit=10
```

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965679945,
  "result": {
    "keyword": "船长",
    "total": 1,
    "nodes": [
      {
        "id": "captain-cert",
        "name": "船长适任证书",
        "type": "适任证书",
        "description": "船长职务适任证书"
      }
    ]
  }
}
```

**响应字段说明**:
- `keyword`: 搜索关键词 (string)
- `total`: 搜索结果总数 (number)
- `nodes`: 搜索结果列表 (array)
  - `id`: 节点ID (string)
  - `name`: 节点名称 (string)
  - `type`: 节点类型 (string)
  - `description`: 节点描述 (string)

### 3.3 获取证书晋升要求详情

**接口地址**: `POST /api/v1/seafarer/certificate/promotion-requirements`

**功能说明**: 获取证书晋升要求详情

**请求参数**:
```json
{
  "fromCertificate": "三副适任证书",
  "toCertificate": "二副适任证书",
  "department": "甲板部",
  "navigationArea": "无限航区"
}
```

**请求字段说明**:
- `fromCertificate`: 源证书 (string)
- `toCertificate`: 目标证书 (string)
- `department`: 部门 (string)
- `navigationArea`: 航区 (string)

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965680323,
  "result": {
    "basicRequirements": [
      "持有有效的低一级适任证书",
      "满足相应的海上服务资历要求",
      "身体健康，符合船员健康标准",
      "年龄符合相关规定要求"
    ],
    "experienceRequirements": [
      "担任相应职务满规定时间",
      "累计海上服务资历符合要求",
      "近期海上服务资历符合要求",
      "无重大安全责任事故记录"
    ],
    "trainingRequirements": [
      "完成相应等级的适任培训",
      "通过海事局理论考试",
      "通过海事局实操评估",
      "完成所需的特殊培训项目"
    ],
    "documentRequirements": [
      "海员证",
      "健康证明",
      "基本安全培训合格证",
      "专业培训合格证书"
    ]
  }
}
```

**响应字段说明**:
- `basicRequirements`: 基本要求 (array)
- `experienceRequirements`: 经验要求 (array)
- `trainingRequirements`: 培训要求 (array)
- `documentRequirements`: 文档要求 (array)

---

## 4. 船员群体分类相关接口

### 4.1 获取船员数据

**接口地址**: `GET /api/v1/seafarer/seafarer/data`

**功能说明**: 获取船员群体分类的完整树形结构数据

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965681067,
  "result": {
    "name": "船员",
    "value": 2080000,
    "children": [
      {
        "name": "预备船员",
        "value": 325000,
        "children": [
          {
            "name": "学生",
            "value": 325000,
            "children": [
              {
                "name": "航海类本科教育学生",
                "value": 88500
              },
              {
                "name": "船员类专科教育学生",
                "value": 108200
              },
              {
                "name": "非航海类工科一年制学生",
                "value": 59000
              },
              {
                "name": "其他",
                "value": 69300
              }
            ]
          }
        ]
      },
      {
        "name": "现役船员",
        "value": 1720000,
        "children": [
          {
            "name": "非自航船舶船员",
            "value": 207000
          },
          {
            "name": "公务船船员",
            "value": 88500
          },
          {
            "name": "小海船船员",
            "value": 320000
          },
          {
            "name": "参照船员管理人员",
            "value": 133000
          },
          {
            "name": "特定航线江海直达船舶船员",
            "value": 138000
          },
          {
            "name": "持海船船员内河航行行驶资格证明船员",
            "value": 93500
          },
          {
            "name": "持海船适任证书船员",
            "value": 777000,
            "certifiedCount": 777000,
            "onboardCount": 542000,
            "demandCount": 685000,
            "children": [
              {
                "name": "持海船高级船员适任证书",
                "value": 438000,
                "certifiedCount": 438000,
                "onboardCount": 312000,
                "demandCount": 385000,
                "children": [
                  {
                    "name": "持油船和化学品船货物操作基本培训合格证船员",
                    "value": 39300
                  }
                ]
              },
              {
                "name": "持海船普通船员适任证书",
                "value": 305000,
                "certifiedCount": 305000,
                "onboardCount": 198000,
                "demandCount": 268000
              },
              {
                "name": "持不参加航行和轮机值班海船船员适任证书",
                "value": 34000,
                "certifiedCount": 34000,
                "onboardCount": 32000,
                "demandCount": 32000
              }
            ]
          }
        ]
      },
      {
        "name": "退役船员",
        "value": 35000
      }
    ]
  }
}
```

**响应字段说明**:
- `name`: 分类名称 (string)
- `value`: 人数 (number)
- `certifiedCount`: 持证人数 (number, 可选)
- `onboardCount`: 在船人数 (number, 可选)
- `demandCount`: 需求人数 (number, 可选)
- `children`: 子分类列表 (array, 可选)

### 4.2 获取颜色配置

**接口地址**: `GET /api/v1/seafarer/seafarer/colors`

**功能说明**: 获取船员群体和区块的颜色配置

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965681215,
  "result": {
    "groupColors": {
      "预备船员": "#f39c12",
      "现役船员": "#1E5f8B",
      "退役船员": "#8e44ad"
    },
    "blockColors": {
      "持海船高级船员适任证书": "#2E86AB",
      "持海船普通船员适任证书": "#A23B72",
      "持不参加航行和轮机值班海船船员适任证书": "#F18F01"
    }
  }
}
```

**响应字段说明**:
- `groupColors`: 群体颜色配置 (object)
- `blockColors`: 区块颜色配置 (object)

### 4.3 获取图表配置

**接口地址**: `GET /api/v1/seafarer/seafarer/chart-config`

**功能说明**: 获取图表组件所需的完整配置

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1749965681306,
  "result": {
    "canvas": {
      "defaultRadius": 140,
      "minRadiusRatio": 0.35,
      "defaultInnerRadius": 60,
      "innerRadiusRatio": 0.15
    },
    "animation": {
      "rotationDuration": 800,
      "minimumDragDistance": 0.5235987755982988,
      "particleCount": 30
    },
    "responsive": {
      "mobile": 768,
      "tablet": 1024
    },
    "wheelSizes": {
      "mobile": {
        "width": 180,
        "height": 180
      },
      "tablet": {
        "width": 200,
        "height": 200
      },
      "desktop": {
        "width": 280,
        "height": 280
      }
    },
    "defaultGroupIndex": 1
  }
}
```

**响应字段说明**:
- `canvas`: 画布配置 (object)
  - `defaultRadius`: 默认半径 (number)
  - `minRadiusRatio`: 最小半径比例 (number)
  - `defaultInnerRadius`: 默认内半径 (number)
  - `innerRadiusRatio`: 内半径比例 (number)
- `animation`: 动画配置 (object)
  - `rotationDuration`: 旋转持续时间 (number)
  - `minimumDragDistance`: 最小拖拽距离 (number)
  - `particleCount`: 粒子数量 (number)
- `responsive`: 响应式配置 (object)
  - `mobile`: 移动端断点 (number)
  - `tablet`: 平板断点 (number)
- `wheelSizes`: 轮盘尺寸配置 (object)
  - `mobile/tablet/desktop`: 不同设备的宽高配置 (object)
- `defaultGroupIndex`: 默认群体索引 (number)

---

## 5. 错误处理

### 5.1 错误响应格式

当接口出现错误时，响应格式如下：

```json
{
  "success": false,
  "message": "具体错误信息",
  "code": 500,
  "timestamp": 1749965681306,
  "result": null
}
```

### 5.2 常见错误码

- `200`: 请求成功
- `400`: 参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 5.3 错误处理建议

1. **网络异常**: 检查网络连接，进行重试
2. **参数错误**: 检查请求参数格式和值
3. **服务器错误**: 显示友好提示，记录错误日志
4. **超时处理**: 设置合理的请求超时时间

---

## 6. 接口测试

### 6.1 测试工具推荐

- **Postman**: 可导入本文档生成的接口集合
- **curl**: 命令行测试工具
- **浏览器开发者工具**: 直接在浏览器中测试

### 6.2 测试用例示例

```bash
# 测试获取船员总数
curl -X GET "http://localhost:8285/api/v1/seafarer/index/seafarer-count"

# 测试获取职位详情
curl -X GET "http://localhost:8285/api/v1/seafarer/career/position/captain"

# 测试搜索证书节点
curl -X GET "http://localhost:8285/api/v1/seafarer/certificate/search?keyword=船长&limit=10"
```

---

## 7. 接口总览

| 序号 | 接口名称 | HTTP方法 | 接口地址 | 说明 |
|------|----------|----------|----------|------|
| 1 | 获取船员总数 | GET | `/index/seafarer-count` | 首页船员总数统计 |
| 2 | 获取职业路径数据 | GET | `/career/paths` | 职业晋升路径配置 |
| 3 | 获取管理关系数据 | GET | `/career/management-relations` | 职位管理关系配置 |
| 4 | 获取职位详情 | GET | `/career/position/{positionId}` | 职位详细信息 |
| 5 | 获取晋升关系数据 | GET | `/career/promotion-relations/{subcategoryName}` | 晋升来源和目标关系 |
| 6 | 获取晋升要求详情 | GET | `/career/promotion-requirements/{targetPosition}` | 详细晋升要求 |
| 7 | 获取职务人数统计 | GET | `/career/position-counts` | 所有职务人数统计 |
| 8 | 获取单个职务人数 | GET | `/career/position-count/{positionId}` | 单个职务人数 |
| 9 | 获取路径选择器选项 | GET | `/career/path-selector-options` | 路径选择器配置 |
| 10 | 获取证书路径数据 | GET | `/certificate/path/{certificateType}` | 证书路径数据 |
| 11 | 搜索证书节点 | GET | `/certificate/search` | 证书节点搜索 |
| 12 | 获取证书晋升要求详情 | POST | `/certificate/promotion-requirements` | 证书晋升要求 |
| 13 | 获取船员数据 | GET | `/seafarer/data` | 船员群体树形数据 |
| 14 | 获取颜色配置 | GET | `/seafarer/colors` | 群体和区块颜色配置 |
| 15 | 获取图表配置 | GET | `/seafarer/chart-config` | 图表组件配置 |

---

## 8. 更新日志

- **v1.0.0** (2025-06-15): 初始版本，包含15个核心接口
- 后续更新将在此记录接口变更信息

---

## 9. 技术支持

如有接口使用问题，请联系后端开发团队或查看项目文档。

**注意**: 本文档基于当前系统测试结果生成，所有接口都已验证可正常使用。 