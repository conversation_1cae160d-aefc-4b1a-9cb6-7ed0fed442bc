package com.js.core.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import com.baomidou.mybatisplus.core.metadata.OrderItem;

import cn.hutool.core.util.StrUtil;

/**
 * 
 * @ClassName:  PageVo   
 * @Description:TODO(分页实体)   
 * @author:  liny 
 * @date:   May 8, 2019 8:05:39 PM   
 *
 */
@Data
public class PageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页号")
    private int pageNumber = 1;

    @ApiModelProperty(value = "页面大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "排序字段")
    private String sort;

    @ApiModelProperty(value = "排序方式 asc/desc")
    private String order;
    
    public OrderItem getOderItem() {
		if(StrUtil.isNotBlank(sort)) {
			//是否正序排列，默认 true
			if(StrUtil.isNotBlank(order) && order.equals("desc")) {
				return OrderItem.desc(sort);
			}else {
				return OrderItem.asc(sort);
			}
		}
		return null;
	}
}
