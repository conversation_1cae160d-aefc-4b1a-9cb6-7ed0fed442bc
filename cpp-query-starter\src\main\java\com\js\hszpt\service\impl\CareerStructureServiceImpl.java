package com.js.hszpt.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.js.hszpt.entity.DwdzCppPositionCategory;
import com.js.hszpt.entity.DwdzCppPositionSubcategory;
import com.js.hszpt.entity.DwdzCppPositionPromotionReq;
import com.js.hszpt.entity.DwdzCppPromotionConfig;
import com.js.hszpt.entity.DwdzCrewExamConfig;
import com.js.hszpt.entity.DwdzCrewTrainSubject;
import com.js.hszpt.mapper.DwdzCppPositionCategoryDao;
import com.js.hszpt.mapper.DwdzCppPositionSubcategoryDao;
import com.js.hszpt.mapper.DwdzCppPositionPromotionReqDao;
import com.js.hszpt.mapper.DwdzCppPositionPromotionRelDao;
import com.js.hszpt.mapper.DwdzCppPromotionConfigDao;
import com.js.hszpt.mapper.DwdzCrewExamConfigDao;
import com.js.hszpt.mapper.DwdzCrewTrainSubjectDao;
import com.js.hszpt.service.CareerStructureService;
import com.js.hszpt.service.PromotionImageConfigCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 船员职业结构服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@DS("dzzzdwdz")
public class CareerStructureServiceImpl implements CareerStructureService {

    @Autowired
    private DwdzCppPromotionConfigDao promotionConfigDao;

    @Autowired
    private DwdzCppPositionCategoryDao positionCategoryDao;

    @Autowired
    private DwdzCppPositionSubcategoryDao positionSubcategoryDao;

    @Autowired
    private DwdzCppPositionPromotionReqDao promotionRequirementDao;

    @Autowired
    private DwdzCppPositionPromotionRelDao promotionRelationDao;

    @Autowired
    private DwdzCrewExamConfigDao examConfigDao;

    @Autowired
    private DwdzCrewTrainSubjectDao trainSubjectDao;

    @Autowired
    private PromotionImageConfigCacheService imageConfigCacheService;

    @Override
    public Map<String, Object> getPositionsByLevel(String positionLevel) {
        log.info("开始获取{}级别的职务名称列表", positionLevel);
        
        try {
            // 从数据库获取指定级别的职务列表
            List<DwdzCppPositionCategory> positions = positionCategoryDao.getCategoriesByLevel(positionLevel);
            
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> positionList = new ArrayList<>();
            
            for (DwdzCppPositionCategory position : positions) {
                Map<String, Object> positionData = new HashMap<>();
                positionData.put("category_id", position.getCategoryId());
                positionData.put("position_code", position.getPositionCode());
                positionData.put("position_name", position.getPositionName());
                positionData.put("position_level", position.getPositionLevel());
                positionData.put("total_count", position.getTotalCount());
                positionData.put("position_description", position.getPositionDescription());
                positionList.add(positionData);
            }
            
            result.put("data", positionList);
            log.info("获取{}级别职务列表成功，共{}个职务", positionLevel, positionList.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取{}级别职务列表失败：{}", positionLevel, e.getMessage(), e);
            // 异常情况下返回空数据
            Map<String, Object> result = new HashMap<>();
            result.put("data", new ArrayList<>());
            return result;
        }
    }

    @Override
    public Map<String, Object> getCareerPaths() {
        log.info("开始获取职业路径数据");
        
        try {
            // 从数据库获取最新的晋升配置
            DwdzCppPromotionConfig config = promotionConfigDao.getLatestConfig();
            
            if (config != null && config.getCareerPaths() != null && !config.getCareerPaths().trim().isEmpty()) {
                // 直接返回数据库中的JSON数据
                log.info("从数据库获取职业路径数据成功");
                return parseJsonToMap(config.getCareerPaths());
            } else {
                log.warn("数据库中没有职业路径配置数据，返回空数据");
                return new HashMap<>();
            }
            
        } catch (Exception e) {
            log.error("获取职业路径数据失败：{}", e.getMessage(), e);
            // 异常情况下返回空数据
            return new HashMap<>();
        }
    }





    @Override
    public Map<String, Object> getManagementRelations() {
        log.info("开始获取管理关系数据");
        
        try {
            // 从数据库获取最新的晋升配置
            DwdzCppPromotionConfig config = promotionConfigDao.getLatestConfig();
            
            if (config != null && config.getManagementRelations() != null && !config.getManagementRelations().trim().isEmpty()) {
                // 直接返回数据库中的JSON数据
                log.info("从数据库获取管理关系数据成功");
                return parseJsonToMap(config.getManagementRelations());
            } else {
                log.warn("数据库中没有管理关系配置数据，返回空数据");
                return new HashMap<>();
            }
            
        } catch (Exception e) {
            log.error("获取管理关系数据失败：{}", e.getMessage(), e);
            // 异常情况下返回空数据
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getPositionDetails(String positionId) {
        log.info("开始获取职位详情，职位ID：{}", positionId);
        
        try {
            // 从数据库获取职位分类信息
            DwdzCppPositionCategory category = positionCategoryDao.getCategoryByPositionCode(positionId);
            
            Map<String, Object> result = new HashMap<>();
            
            if (category != null) {
                result.put("id", category.getPositionCode());
                result.put("name", category.getPositionName());
                result.put("level", category.getPositionLevel());
                result.put("description", category.getPositionDescription());
                result.put("totalCount", category.getTotalCount());
                
                // 获取子分类信息
                List<DwdzCppPositionSubcategory> subcategories = 
                    positionSubcategoryDao.getSubcategoriesByCategoryId(category.getCategoryId());
                
                List<Map<String, Object>> subcategoryList = new ArrayList<>();
                for (DwdzCppPositionSubcategory sub : subcategories) {
                    Map<String, Object> subData = new HashMap<>();
                    subData.put("id", sub.getSubcategoryId()); // 返回subcategory_id供前端使用
                    subData.put("name", sub.getFlxxmc()); // 使用新的字段名作为子分类名称
                    subData.put("count", sub.getPersonCount());
                    // 为描述字段提供默认值，可以使用完整职务名称或其他相关字段
                    String description = sub.getWzzwqc() != null ? sub.getWzzwqc() :
                                       (sub.getFlxxmc() != null ? sub.getFlxxmc() : "");
                    subData.put("description", description);

                    // 添加前端思维导图根节点需要的3个字段
                    subData.put("srzwmc", sub.getSrzwmc()); // 适任职务信息名称
                    subData.put("hqmcqc", sub.getHqmcqc()); // 航区等分类信息名称全称
                    subData.put("djmcqc", sub.getDjmcqc()); // 职务等级信息名称全称

                    subcategoryList.add(subData);
                }
                result.put("subcategories", subcategoryList);
                
            } else {
                // 返回空数据
                result.put("id", positionId);
                result.put("name", "");
                result.put("level", "");
                result.put("description", "");
                result.put("totalCount", 0);
                result.put("subcategories", new ArrayList<>());
                log.warn("数据库中没有职位详情数据，职位ID：{}", positionId);
            }
            
            log.info("获取职位详情成功，职位ID：{}", positionId);
            return result;
            
        } catch (Exception e) {
            log.error("获取职位详情失败，职位ID：{}，错误：{}", positionId, e.getMessage(), e);
            // 异常情况下返回空数据
            Map<String, Object> result = new HashMap<>();
            result.put("id", positionId);
            result.put("name", "");
            result.put("level", "");
            result.put("description", "");
            result.put("totalCount", 0);
            result.put("subcategories", new ArrayList<>());
            return result;
        }
    }



    /**
     * 创建子分类数据辅助方法
     */
    private Map<String, Object> createSubcategory(String name, int count, String description) {
        Map<String, Object> sub = new HashMap<>();
        sub.put("name", name);
        sub.put("count", count);
        sub.put("description", description);
        return sub;
    }



    @Override
    public Map<String, Object> getPromotionRelationsBySubcategoryId(String subcategoryId) {
        log.info("开始获取晋升关系数据，子分类ID：{}", subcategoryId);
        
        try {
            // 从数据库获取晋升关系数据，基于新的表结构
            List<Map<String, Object>> promotionRelations = promotionRelationDao.getPromotionRelationsBySubcategoryId(subcategoryId);
            
            if (promotionRelations == null || promotionRelations.isEmpty()) {
                log.warn("数据库中没有找到子分类ID {} 的晋升关系数据，返回空数据", subcategoryId);
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("lower", new ArrayList<>());
                emptyResult.put("upper", new ArrayList<>());
                emptyResult.put("relationTypeEnums", getRelationTypeEnums()); // 返回枚举供前端参考
                return emptyResult;
            }
            
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> lower = new ArrayList<>(); // 晋升来源
            List<Map<String, Object>> upper = new ArrayList<>(); // 晋升目标
            
            // 处理晋升关系数据
            for (Map<String, Object> relation : promotionRelations) {
                String direction = (String) relation.get("direction");
                String relatedSubcategoryName = (String) relation.get("relatedSubcategoryName");
                String relatedSubcategoryId = (String) relation.get("relatedSubcategoryId");
                String relationTypeName = (String) relation.get("sqxszsmc"); // 使用中文名称
                Integer personCount = (Integer) relation.get("personCount");
                BigDecimal sortOrder = (BigDecimal) relation.get("sortOrder");
                String dqSubcategoryId = (String) relation.get("dqSubcategoryId");
                String mbSubcategoryId = (String) relation.get("mbSubcategoryId");
                
                Map<String, Object> relationItem = new HashMap<>();
                relationItem.put("id", relatedSubcategoryId); // 返回ID给前端
                relationItem.put("name", relatedSubcategoryName);
                relationItem.put("type", relationTypeName); // 使用中文名称
                relationItem.put("personCount", personCount != null ? personCount : 0);
                relationItem.put("sortOrder", sortOrder != null ? sortOrder.doubleValue() : 0.0);
                relationItem.put("dqSubcategoryId", dqSubcategoryId); // 当前证书职务分类id
                relationItem.put("mbSubcategoryId", mbSubcategoryId); // 晋升目标证书职务分类id
                
                // 添加新的展示字段
                relationItem.put("srzwmc", relation.get("relatedSrzwmc")); // 适任职务信息名称
                relationItem.put("hqmcqc", relation.get("relatedHqmcqc")); // 航区等分类信息名称全称
                relationItem.put("djmcqc", relation.get("relatedDjmcqc")); // 职务等级信息名称全称
                
                // 根据方向分类到lower或upper
                if ("source".equals(direction)) {
                    // 当前子分类是晋升目标，relation中的是晋升来源
                    lower.add(relationItem);
                } else if ("target".equals(direction)) {
                    // 当前子分类是晋升来源，relation中的是晋升目标
                    upper.add(relationItem);
                }
            }
            
            // 按排序序号排序
            lower.sort((a, b) -> Double.compare((Double) a.get("sortOrder"), (Double) b.get("sortOrder")));
            upper.sort((a, b) -> Double.compare((Double) a.get("sortOrder"), (Double) b.get("sortOrder")));
            
            result.put("lower", lower);
            result.put("upper", upper);
            result.put("relationTypeEnums", getRelationTypeEnums()); // 返回枚举供前端参考
            
            log.info("获取晋升关系数据成功，子分类ID：{}，来源关系：{}个，目标关系：{}个", 
                    subcategoryId, lower.size(), upper.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取晋升关系数据失败，子分类ID：{}，错误：{}", subcategoryId, e.getMessage(), e);
            throw new RuntimeException("获取晋升关系数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取关系类型枚举值
     * @return 关系类型枚举映射
     */
    private Map<String, String> getRelationTypeEnums() {
        Map<String, String> enums = new HashMap<>();
        enums.put("0", "职务晋升");
        enums.put("1", "学生考试发证");
        enums.put("2", "航区扩大");
        enums.put("3", "功率提高");
        enums.put("4", "军转海");
        enums.put("5", "内转海");
        enums.put("6", "渔转海");
        enums.put("7", "再有效");
        enums.put("8", "职务签证");
        enums.put("9", "初次申请(游艇)");
        enums.put("10", "吨位提高");
        enums.put("11", "取消限制");
        enums.put("12", "变更范围(游艇)");
        return enums;
    }

    @Override
    public Map<String, Object> getPromotionRequirements(String targetPosition) {
        log.info("开始获取晋升要求详情，目标职位：{}", targetPosition);
        
        try {
            // 判断targetPosition是ID还是名称，并获取相应的晋升要求列表
            List<DwdzCppPositionPromotionReq> requirements;
            
            // 先尝试作为ID查询
            if (targetPosition != null && targetPosition.length() > 10) {
                // 看起来是ID格式，使用ID查询
                requirements = promotionRequirementDao.getRequirementsBySubcategoryId(targetPosition);
            } else {
                // 保持向后兼容，使用名称查询（废弃方法）
                DwdzCppPositionPromotionReq singleRequirement = promotionRequirementDao.getRequirementBySubcategoryName(targetPosition);
                requirements = singleRequirement != null ? Arrays.asList(singleRequirement) : new ArrayList<>();
            }
            
            Map<String, Object> result = new HashMap<>();
            
            if (requirements != null && !requirements.isEmpty()) {
                // 按照条件类型分组处理
                Map<Integer, List<Map<String, Object>>> typeGroups = new HashMap<>();
                
                for (DwdzCppPositionPromotionReq req : requirements) {
                    Integer zssqtjlx = req.getZssqtjlx(); // 证书申请条件类型
                    if (zssqtjlx == null) continue;
                    
                    Map<String, Object> reqItem = new HashMap<>();
                    reqItem.put("requirementId", req.getRequirementId());
                    reqItem.put("typeName", req.getZssqtjlxmczs()); // 条件类型中文名称
                    reqItem.put("basicContent", req.getSqtjjbnr()); // 申请条件基本内容
                    reqItem.put("supplementContent", req.getSqtjbcnr()); // 申请条件补充内容
                    reqItem.put("attachments", req.getSqtjfj()); // 申请条件附件
                    reqItem.put("notes", req.getBz()); // 备注
                    reqItem.put("dqSubcategoryId", req.getDqSubcategoryId());
                    reqItem.put("mbSubcategoryId", req.getMbSubcategoryId());
                    
                    // 处理特殊的关联查询
                    if (zssqtjlx == 5) {
                        // 考试要求：查询考试科目信息
                        reqItem.put("examSubjects", getExamSubjects(req.getKskmlb()));
                    } else if (zssqtjlx == 7) {
                        // 培训合格证要求：查询培训项目信息
                        reqItem.put("trainSubjects", getTrainSubjects(req.getPxhgzxmxz()));
                    }
                    
                    typeGroups.computeIfAbsent(zssqtjlx, k -> new ArrayList<>()).add(reqItem);
                }
                
                // 构建返回结果
                result.put("overview", typeGroups.getOrDefault(0, new ArrayList<>())); // 要求总览
                result.put("age", typeGroups.getOrDefault(1, new ArrayList<>())); // 年龄要求
                result.put("health", typeGroups.getOrDefault(2, new ArrayList<>())); // 健康要求
                result.put("experience", typeGroups.getOrDefault(3, new ArrayList<>())); // 资历要求
                result.put("training", typeGroups.getOrDefault(4, new ArrayList<>())); // 培训要求
                result.put("exam", typeGroups.getOrDefault(5, new ArrayList<>())); // 考试要求
                result.put("practice", typeGroups.getOrDefault(6, new ArrayList<>())); // 见习要求
                result.put("certificate", typeGroups.getOrDefault(7, new ArrayList<>())); // 培训合格证要求
                result.put("other", typeGroups.getOrDefault(9, new ArrayList<>())); // 其他说明
                
                log.info("从数据库获取晋升要求详情成功，目标职位：{}，共{}条记录", targetPosition, requirements.size());
            } else {
                // 数据库中没有数据，返回空结构
                result.put("overview", new ArrayList<>());
                result.put("age", new ArrayList<>());
                result.put("health", new ArrayList<>());
                result.put("experience", new ArrayList<>());
                result.put("training", new ArrayList<>());
                result.put("exam", new ArrayList<>());
                result.put("practice", new ArrayList<>());
                result.put("certificate", new ArrayList<>());
                result.put("other", new ArrayList<>());
                
                log.warn("数据库中没有找到目标职位 {} 的晋升要求数据，返回空数据", targetPosition);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取晋升要求详情失败，目标职位：{}，错误：{}", targetPosition, e.getMessage(), e);
            // 异常情况下返回空数据
            Map<String, Object> result = new HashMap<>();
            result.put("overview", new ArrayList<>());
            result.put("age", new ArrayList<>());
            result.put("health", new ArrayList<>());
            result.put("experience", new ArrayList<>());
            result.put("training", new ArrayList<>());
            result.put("exam", new ArrayList<>());
            result.put("practice", new ArrayList<>());
            result.put("certificate", new ArrayList<>());
            result.put("other", new ArrayList<>());
            return result;
        }
    }

    @Override
    public Map<String, Object> getPromotionRequirementsByIds(String dqSubcategoryId, String mbSubcategoryId, String source) {
        // 处理请求来源参数，默认为内网
        if (source == null || source.trim().isEmpty()) {
            source = "intranet";
        }
        boolean isIntranet = "intranet".equalsIgnoreCase(source);
        
        log.info("开始获取晋升要求详情，当前职务ID：{}，目标职务ID：{}，请求来源：{}", dqSubcategoryId, mbSubcategoryId, source);
        
        try {
            // 根据两个ID查询晋升要求列表
            List<DwdzCppPositionPromotionReq> requirements = 
                promotionRequirementDao.getRequirementsByIds(dqSubcategoryId, mbSubcategoryId);
            
            Map<String, Object> result = new HashMap<>();
            
            if (requirements != null && !requirements.isEmpty()) {
                // 按照条件类型分组处理
                Map<Integer, List<String>> basicContentGroups = new HashMap<>(); // 用于存储基本内容的文本
                Map<Integer, List<Map<String, Object>>> specialTypeGroups = new HashMap<>(); // 用于存储特殊类型的完整对象
                
                for (DwdzCppPositionPromotionReq req : requirements) {
                    Integer zssqtjlx = req.getZssqtjlx(); // 证书申请条件类型
                    if (zssqtjlx == null) continue;
                    
                    if (zssqtjlx == 5 || zssqtjlx == 7) {
                        // 考试要求和培训合格证要求：返回完整的对象结构
                        Map<String, Object> reqItem = new HashMap<>();
                        reqItem.put("requirementId", req.getRequirementId());
                        reqItem.put("typeName", req.getZssqtjlxmczs()); // 条件类型中文名称
                        reqItem.put("basicContent", req.getSqtjjbnr()); // 申请条件基本内容
                        reqItem.put("supplementContent", req.getSqtjbcnr()); // 申请条件补充内容
                        reqItem.put("attachments", req.getSqtjfj()); // 申请条件附件
                        reqItem.put("notes", req.getBz()); // 备注
                        reqItem.put("dqSubcategoryId", req.getDqSubcategoryId());
                        reqItem.put("mbSubcategoryId", req.getMbSubcategoryId());
                        reqItem.put("dqSubcategoryName", req.getDqSubcategoryName());
                        reqItem.put("mbSubcategoryName", req.getMbSubcategoryName());
                        
                        if (zssqtjlx == 5) {
                            // 考试要求：查询考试科目信息
                            reqItem.put("examSubjects", getExamSubjects(req.getKskmlb()));
                        } else if (zssqtjlx == 7) {
                            // 培训合格证要求：查询培训项目信息
                            reqItem.put("trainSubjects", getTrainSubjects(req.getPxhgzxmxz()));
                        }
                        
                        specialTypeGroups.computeIfAbsent(zssqtjlx, k -> new ArrayList<>()).add(reqItem);
                    } else {
                        String basicContent = null;
                        if (zssqtjlx == 0){
                            // 特殊处理：当zssqtjlx=0时，获取配置的div图片配置
                            basicContent = req.getSqtjbcnr();
                        }else {
                            // 其他类型：直接使用 sqtjjbnr 字段的内容
                            basicContent = req.getSqtjjbnr();
                        }

                        
                        // 特殊处理：当zssqtjlx=0时，进行图片配置处理
                        if (zssqtjlx == 0 && basicContent != null && !basicContent.trim().isEmpty()) {
                            // 检查是否包含source_file_id
                            String matchingSourceFileId = imageConfigCacheService.findMatchingSourceFileId(basicContent);
                            if (matchingSourceFileId != null) {
                                // 构建图片访问URL
                                String imageUrl = imageConfigCacheService.buildImageUrl(matchingSourceFileId, isIntranet);
                                if (imageUrl != null) {
                                    // 用图片URL替换原始内容
                                    basicContent = imageUrl;
                                    log.debug("晋升要求总览图片处理：source_file_id={}, imageUrl={}", matchingSourceFileId, imageUrl);
                                }
                            }
                        }
                        
                        if (basicContent != null && !basicContent.trim().isEmpty()) {
                            // 解析基本内容为字符串列表
                            List<String> contentList = parseRequirementText(basicContent);
                            if (!contentList.isEmpty()) {
                                basicContentGroups.computeIfAbsent(zssqtjlx, k -> new ArrayList<>()).addAll(contentList);
                            }
                        }
                        
                        // 如果基本内容为空，尝试使用补充内容
                        if ((basicContent == null || basicContent.trim().isEmpty()) && req.getSqtjbcnr() != null) {
                            List<String> contentList = parseRequirementText(req.getSqtjbcnr());
                            if (!contentList.isEmpty()) {
                                basicContentGroups.computeIfAbsent(zssqtjlx, k -> new ArrayList<>()).addAll(contentList);
                            }
                        }
                    }
                }
                
                // 构建返回结果
                result.put("overview", basicContentGroups.getOrDefault(0, new ArrayList<>())); // 要求总览
                result.put("age", basicContentGroups.getOrDefault(1, new ArrayList<>())); // 年龄要求
                result.put("health", basicContentGroups.getOrDefault(2, new ArrayList<>())); // 健康要求
                result.put("experience", basicContentGroups.getOrDefault(3, new ArrayList<>())); // 资历要求
                result.put("training", basicContentGroups.getOrDefault(4, new ArrayList<>())); // 培训要求
                result.put("exam", specialTypeGroups.getOrDefault(5, new ArrayList<>())); // 考试要求
                result.put("practice", basicContentGroups.getOrDefault(6, new ArrayList<>())); // 见习要求
                result.put("certificate", specialTypeGroups.getOrDefault(7, new ArrayList<>())); // 培训合格证要求
                result.put("other", basicContentGroups.getOrDefault(9, new ArrayList<>())); // 其他说明
                
                log.info("从数据库获取晋升要求详情成功，当前职务ID：{}，目标职务ID：{}，共{}条记录", 
                        dqSubcategoryId, mbSubcategoryId, requirements.size());
            } else {
                // 数据库中没有数据，返回空结构
                result.put("overview", new ArrayList<>());
                result.put("age", new ArrayList<>());
                result.put("health", new ArrayList<>());
                result.put("experience", new ArrayList<>());
                result.put("training", new ArrayList<>());
                result.put("exam", new ArrayList<>());
                result.put("practice", new ArrayList<>());
                result.put("certificate", new ArrayList<>());
                result.put("other", new ArrayList<>());
                
                log.warn("数据库中没有找到当前职务ID {} 到目标职务ID {} 的晋升要求数据，返回空数据", 
                        dqSubcategoryId, mbSubcategoryId);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取晋升要求详情失败，当前职务ID：{}，目标职务ID：{}，错误：{}", 
                     dqSubcategoryId, mbSubcategoryId, e.getMessage(), e);
            // 异常情况下返回空数据
            Map<String, Object> result = new HashMap<>();
            result.put("overview", new ArrayList<>());
            result.put("age", new ArrayList<>());
            result.put("health", new ArrayList<>());
            result.put("experience", new ArrayList<>());
            result.put("training", new ArrayList<>());
            result.put("exam", new ArrayList<>());
            result.put("practice", new ArrayList<>());
            result.put("certificate", new ArrayList<>());
            result.put("other", new ArrayList<>());
            return result;
        }
    }
    
    /**
     * 获取考试科目信息
     * @param kskmlb 考试科目ID列表，多条数据用逗号隔开
     * @return 考试科目信息列表
     */
    private List<Map<String, Object>> getExamSubjects(String kskmlb) {
        if (kskmlb == null || kskmlb.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            // 解析ID列表
            String[] examIds = kskmlb.split(",");
            List<String> examIdList = new ArrayList<>();
            for (String id : examIds) {
                id = id.trim();
                if (!id.isEmpty()) {
                    examIdList.add(id);
                }
            }
            
            if (examIdList.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 查询考试科目信息
            List<DwdzCrewExamConfig> examConfigs = examConfigDao.selectByExamIds(examIdList);
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (DwdzCrewExamConfig config : examConfigs) {
                Map<String, Object> examItem = new HashMap<>();
                examItem.put("examId", config.getExamId());
                examItem.put("subjectName", config.getSubjectName());
                examItem.put("examDuration", config.getExamDuration());
                examItem.put("paperCode", config.getPaperCode());
                examItem.put("targetObject", config.getTargetObject());
                examItem.put("passingScore", config.getPassingScore());
                examItem.put("sectionPassScore", config.getSectionPassScore());
                examItem.put("examType", config.getExamType());
                examItem.put("certType", config.getCertType());
                result.add(examItem);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取考试科目信息失败，考试科目ID列表：{}，错误：{}", kskmlb, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取培训项目科目信息
     * @param pxhgzxmxz 培训合格证项目选择ID列表，多条数据用逗号隔开
     * @return 培训项目科目信息列表
     */
    private List<Map<String, Object>> getTrainSubjects(String pxhgzxmxz) {
        if (pxhgzxmxz == null || pxhgzxmxz.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            // 解析ID列表
            String[] subjectIds = pxhgzxmxz.split(",");
            List<String> subjectIdList = new ArrayList<>();
            for (String id : subjectIds) {
                id = id.trim();
                if (!id.isEmpty()) {
                    subjectIdList.add(id);
                }
            }
            
            if (subjectIdList.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 查询培训项目科目信息
            List<DwdzCrewTrainSubject> trainSubjects = trainSubjectDao.selectBySubjectIds(subjectIdList);
            
            List<Map<String, Object>> result = new ArrayList<>();
            for (DwdzCrewTrainSubject subject : trainSubjects) {
                Map<String, Object> trainItem = new HashMap<>();
                trainItem.put("subjectId", subject.getSubjectId());
                trainItem.put("projectName", subject.getProjectName());
                trainItem.put("projectCode", subject.getProjectCode());
                trainItem.put("subjectName", subject.getSubjectName());
                trainItem.put("subjectCode", subject.getSubjectCode());
                trainItem.put("subjectType", subject.getSubjectType());
                trainItem.put("certType", subject.getCertType());
                trainItem.put("certName", subject.getCertName());
                result.add(trainItem);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("获取培训项目科目信息失败，培训项目ID列表：{}，错误：{}", pxhgzxmxz, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 解析要求JSON字符串为列表
     * 优先解析JSON数组格式，如果失败则回退到文本分割
     */
    private List<String> parseRequirementJson(String jsonText) {
        if (jsonText == null || jsonText.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            // 尝试解析为JSON数组
            ObjectMapper mapper = new ObjectMapper();
            List<String> result = mapper.readValue(jsonText, new TypeReference<List<String>>() {});
            
            if (result != null) {
                log.debug("成功解析JSON数组格式的要求数据，包含{}个项目", result.size());
                return result;
            }
            
        } catch (Exception e) {
            log.debug("JSON解析失败，回退到文本分割模式：{}", e.getMessage());
        }
        
        // JSON解析失败，回退到原来的文本分割方式
        return parseRequirementText(jsonText);
    }
    
    /**
     * 解析要求文本为列表（备用方法）
     * 支持多种分隔符：换行符、分号、逗号等
     */
    private List<String> parseRequirementText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        
        // 首先按换行符分割
        String[] lines = text.split("\\r?\\n");
        
        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty()) {
                // 如果行中包含分号或中文分号，进一步分割
                if (line.contains(";") || line.contains("；")) {
                    String[] parts = line.split("[;；]");
                    for (String part : parts) {
                        part = part.trim();
                        if (!part.isEmpty()) {
                            result.add(part);
                        }
                    }
                } else {
                    result.add(line);
                }
            }
        }
        
        return result;
    }

    @Override
    public Map<String, Integer> getPositionCounts() {
        log.info("开始获取职务人数统计");
        
        try {
            // 从数据库获取职务人数统计
            List<Map<String, Object>> dbResults = positionCategoryDao.getPositionCountsMap();
            
            Map<String, Integer> result = new HashMap<>();
            
            // 将List<Map>转换为Map<String, Integer>
            if (dbResults != null && !dbResults.isEmpty()) {
                for (Map<String, Object> row : dbResults) {
                    String positionCode = (String) row.get("position_code");
                    Object totalCountObj = row.get("total_count");
                    
                    if (positionCode != null && totalCountObj != null) {
                        Integer totalCount = null;
                        if (totalCountObj instanceof Integer) {
                            totalCount = (Integer) totalCountObj;
                        } else if (totalCountObj instanceof Number) {
                            totalCount = ((Number) totalCountObj).intValue();
                        }
                        
                        if (totalCount != null) {
                            result.put(positionCode, totalCount);
                        }
                    }
                }
            }
            
            // 如果数据库没有数据，返回空数据
            if (result.isEmpty()) {
                log.warn("数据库中没有职务人数数据，返回空数据");
            }
            
            log.info("获取职务人数统计成功，返回{}个职务", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取职务人数统计失败：{}", e.getMessage(), e);
            // 异常情况下返回空数据
            return new HashMap<>();
        }
    }



    @Override
    public Map<String, Object> getPositionCount(String positionId) {
        log.info("开始获取单个职务人数，职位ID：{}", positionId);
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 从数据库获取职务人数
            Integer count = positionCategoryDao.getPositionCountByCode(positionId);
            
            if (count != null) {
                result.put("positionId", positionId);
                result.put("count", count);
                result.put("status", "success");
            } else {
                // 返回空数据
                result.put("positionId", positionId);
                result.put("count", 0);
                result.put("status", "no_data");
            }
            
            log.info("获取单个职务人数成功，职位ID：{}，人数：{}", positionId, result.get("count"));
            return result;
            
        } catch (Exception e) {
            log.error("获取单个职务人数失败，职位ID：{}，错误：{}", positionId, e.getMessage(), e);
            // 异常情况下返回空数据
            Map<String, Object> result = new HashMap<>();
            result.put("positionId", positionId);
            result.put("count", 0);
            result.put("status", "error");
            return result;
        }
    }
    


    @Override
    public Object getPathSelectorOptions() {
        log.info("开始获取路径选择器选项");
        
        try {
            // 从数据库获取最新的晋升配置
            DwdzCppPromotionConfig config = promotionConfigDao.getLatestConfig();
            
            if (config != null && config.getPathSelectorOptions() != null && !config.getPathSelectorOptions().trim().isEmpty()) {
                // 直接返回数据库中的JSON数据
                log.info("从数据库获取路径选择器选项成功");
                return parseJsonToObject(config.getPathSelectorOptions());
            } else {
                log.warn("数据库中没有路径选择器配置数据，返回空数据");
                return new ArrayList<>();
            }
            
        } catch (Exception e) {
            log.error("获取路径选择器选项失败：{}", e.getMessage(), e);
            // 异常情况下返回空数据
            return new ArrayList<>();
        }
    }
    
    /**
     * 创建选项对象辅助方法
     */
    private Map<String, String> createOption(String value, String label) {
        Map<String, String> option = new HashMap<>();
        option.put("value", value);
        option.put("label", label);
        return option;
    }

    /**
     * 解析JSON字符串为Map对象
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseJsonToMap(String jsonString) {
        try {
            if (jsonString == null || jsonString.trim().isEmpty()) {
                return new HashMap<>();
            }
            
            // 使用Jackson进行JSON解析
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});
            
        } catch (Exception e) {
            log.error("解析JSON失败：{}，JSON内容：{}", e.getMessage(), jsonString);
            return new HashMap<>();
        }
    }

    /**
     * 解析JSON字符串为任意对象（Map或List）
     */
    private Object parseJsonToObject(String jsonString) {
        try {
            if (jsonString == null || jsonString.trim().isEmpty()) {
                return new HashMap<>();
            }
            
            // 使用Jackson进行JSON解析
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(jsonString, Object.class);
            
        } catch (Exception e) {
            log.error("解析JSON失败：{}，JSON内容：{}", e.getMessage(), jsonString);
            return new HashMap<>();
        }
    }


} 