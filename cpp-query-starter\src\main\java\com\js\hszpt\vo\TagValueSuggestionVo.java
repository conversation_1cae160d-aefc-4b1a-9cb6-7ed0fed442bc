package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * @ClassName: TagValueSuggestionVo   
 * @Description: 标签值建议响应对象
 * @author: System Generation 
 */
@Data
@ApiModel(value = "标签值建议响应对象")
public class TagValueSuggestionVo {

    /**
     * 特征值
     */
    @ApiModelProperty(value = "特征值", required = true)
    private String featureValue;

    /**
     * 描述信息（可选）
     */
    @ApiModelProperty(value = "描述信息")
    private String description;

    /**
     * 特征类别编码
     */
    @ApiModelProperty(value = "特征类别编码")
    private String triggerCategoryCode;

    /**
     * 构造函数
     */
    public TagValueSuggestionVo() {
    }

    /**
     * 构造函数
     * @param featureValue 特征值
     * @param description 描述
     */
    public TagValueSuggestionVo(String featureValue, String description) {
        this.featureValue = featureValue;
        this.description = description;
    }

    /**
     * 构造函数
     * @param featureValue 特征值
     * @param description 描述
     * @param triggerCategoryCode 特征类别编码
     */
    public TagValueSuggestionVo(String featureValue, String description, String triggerCategoryCode) {
        this.featureValue = featureValue;
        this.description = description;
        this.triggerCategoryCode = triggerCategoryCode;
    }
}
