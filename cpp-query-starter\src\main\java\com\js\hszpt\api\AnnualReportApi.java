package com.js.hszpt.api;

import com.js.annotation.SystemLog;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.enums.LogType;
import com.js.hszpt.dto.GeneratePdfRequest;
import com.js.hszpt.service.AnnualReportService;
import com.js.hszpt.vo.AnnualReportDataVO;
import com.js.hszpt.vo.PdfGenerateResponseVO;
import com.js.hszpt.vo.YearOptionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 船员年度报告API控制器
 * @ClassName: AnnualReportApi
 * @Description: 船员年度报告接口
 * @author: System Generation
 */
@Slf4j
@RestController
@Api(description = "船员年度报告接口")
@RequestMapping("/api/annual-report")
public class AnnualReportApi {

    @Autowired
    private AnnualReportService annualReportService;

    /**
     * 获取年份选项列表
     */
    @SystemLog(description = "年度报告-获取年份选项", type = LogType.OPERATION)
    @GetMapping("/year-options")
    @ApiOperation(value = "获取年份选项列表")
    public Result<List<YearOptionVO>> getYearOptions() {
        try {
            List<YearOptionVO> yearOptions = annualReportService.getYearOptions();
            return ResultUtil.data(yearOptions);
        } catch (Exception e) {
            log.error("获取年份选项失败", e);
            return ResultUtil.error("获取年份选项失败: " + e.getMessage());
        }
    }

    /**
     * 获取年度报告数据
     */
    @SystemLog(description = "年度报告-获取报告数据", type = LogType.OPERATION)
    @GetMapping("/data")
    @ApiOperation(value = "获取年度报告数据")
    public Result<AnnualReportDataVO> getAnnualReportData(
            @ApiParam(value = "查询年份", required = true) @RequestParam Integer year,
            @ApiParam(value = "船员ID或身份证号", required = false) @RequestParam(required = false) String seafarerId) {
        try {
            // 参数验证
            if (year == null) {
                return ResultUtil.error("查询年份不能为空");
            }
            if (seafarerId == null || seafarerId.trim().isEmpty()) {
                return ResultUtil.error("船员ID不能为空");
            }
            
            AnnualReportDataVO reportData = annualReportService.getAnnualReportData(year, seafarerId.trim());
            return reportData != null ? ResultUtil.data(reportData)
                    : ResultUtil.data(new AnnualReportDataVO(),"未找到该船员年度报告数据");
        } catch (Exception e) {
            log.error("获取年度报告数据失败", e);
            return ResultUtil.error("获取年度报告数据失败: " + e.getMessage());
        }
    }

    /**
     * 生成年度报告PDF
     */
    @SystemLog(description = "年度报告-生成PDF", type = LogType.OPERATION)
    @PostMapping("/generate-pdf")
    @ApiOperation(value = "生成年度报告PDF")
    public Result<PdfGenerateResponseVO> generatePdf(@RequestBody GeneratePdfRequest request) {
        try {
            if (request.getYear() == null) {
                return ResultUtil.error("年份参数不能为空");
            }
            
            PdfGenerateResponseVO response = annualReportService.generatePdf(request);
            return ResultUtil.data(response);
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            return ResultUtil.error("生成PDF失败: " + e.getMessage());
        }
    }
} 