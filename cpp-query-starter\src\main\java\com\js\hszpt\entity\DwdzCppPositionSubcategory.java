package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员证书职务分类表实体类
 * 用于管理船员证书职务的具体分类信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_position_subcategory")
public class DwdzCppPositionSubcategory {

    /**
     * 子类唯一标识ID（主键）
     */
    @TableId(value = "subcategory_id", type = IdType.ASSIGN_UUID)
    private String subcategoryId;

    /**
     * 关联的职务名称分类ID（外键）
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 分类信息名称（前端展示用的子分类名称）
     */
    @TableField("flxxmc")
    private String flxxmc;

    /**
     * 完整职务等分类信息编码
     */
    @TableField("wzzwbm")
    private String wzzwbm;

    /**
     * 完整职务等分类信息简称
     */
    @TableField("wzzwjc")
    private String wzzwjc;

    /**
     * 完整职务等分类信息全称
     */
    @TableField("wzzwqc")
    private String wzzwqc;

    /**
     * 适任职务等分类信息名称
     */
    @TableField("srzwmc")
    private String srzwmc;

    /**
     * 适任职务等分类信息名称编码
     */
    @TableField("srzwmcbm")
    private String srzwmcbm;

    /**
     * 等级等分类信息编码
     */
    @TableField("djbm")
    private String djbm;

    /**
     * 等级等分类信息名称简称
     */
    @TableField("djmcjc")
    private String djmcjc;

    /**
     * 等级等分类信息名称全称
     */
    @TableField("djmcqc")
    private String djmcqc;

    /**
     * 等级信息限制
     */
    @TableField("djxxxz")
    private String djxxxz;

    /**
     * 航区等分类信息编码
     */
    @TableField("hqbm")
    private String hqbm;

    /**
     * 航区等分类信息名称简称
     */
    @TableField("hqmcjc")
    private String hqmcjc;

    /**
     * 航区等分类信息名称全称
     */
    @TableField("hqmcqc")
    private String hqmcqc;

    /**
     * 证书类别 0(海船船员适任证书) 2(海上游艇操作人员) 5(培训合格证)
     */
    @TableField("cert_type")
    private Integer certType;

    /**
     * 证书职务分类人数统计
     */
    @TableField("person_count")
    private Integer personCount;

    /**
     * 排序序号（用于同一职务分类下的子类展示顺序）
     */
    @TableField("sort_order")
    private BigDecimal sortOrder;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    /**
     * 子分类描述（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private String description;
} 