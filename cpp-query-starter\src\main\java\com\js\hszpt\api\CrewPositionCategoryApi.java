package com.js.hszpt.api;

import com.js.core.common.vo.Result;
import com.js.hszpt.service.CrewPositionCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 船员职务名称分类API接口
 */
@RestController
@RequestMapping("/api/position-category")
public class CrewPositionCategoryApi {

    @Autowired
    private CrewPositionCategoryService crewPositionCategoryService;

    /**
     * 获取所有职务分类列表
     */
    @GetMapping("/list")
    public Result getAllPositionCategories() {
        return crewPositionCategoryService.getAllPositionCategories();
    }
}