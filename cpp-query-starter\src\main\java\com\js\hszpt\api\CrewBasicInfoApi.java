package com.js.hszpt.api;

import com.js.core.common.vo.Result;
import com.js.hszpt.service.CrewBasicInfoService;
import com.js.hszpt.vo.CrewQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import com.js.hszpt.mapper.CrewBasicInfoMapper;
import com.js.hszpt.vo.CrewListVO;

/**
 * 船员基本信息API接口
 */
@RestController
@RequestMapping("/api/seafarer")
public class CrewBasicInfoApi {

    @Autowired
    private CrewBasicInfoService crewBasicInfoService;

    @Autowired
    private CrewBasicInfoMapper crewBasicInfoMapper;

    /**
     * 船员列表查询
     */
    @GetMapping("/list")
    public Result getCrewList(CrewQueryVO queryVO) {
        return crewBasicInfoService.getCrewList(queryVO);
    }

    /**
     * 船员列表查询 - POST请求（支持复杂查询条件）
     */
    @PostMapping("/list")
    public Result getCrewListPost(@RequestBody CrewQueryVO queryVO) {
        return crewBasicInfoService.getCrewList(queryVO);
    }


    /**
     * 获取特征标签列表
     */
    @GetMapping("/feature-tags")
    public Result getFeatureTags() {
        return crewBasicInfoService.getFeatureTags();
    }

    /**
     * 获取港口列表
     */
    @GetMapping("/ports")
    public Result getPorts(@RequestParam String query) {
        return crewBasicInfoService.getPorts(query);
    }



    /**
     * 获取船名列表
     */
    @GetMapping("/ships")
    public Result getShips(@RequestParam String query) {
        return crewBasicInfoService.getShips(query);
    }

    /**
     * 获取当前职务列表
     */
    @GetMapping("/positions")
    public Result getPositions(@RequestParam String query) {
        return crewBasicInfoService.getPositions(query);
    }

    /**
     * 船员列表导出（按查询条件导出全部，不分页）- POST方式，支持复杂的标签组查询条件
     */
    @PostMapping("/list/export")
    public void exportCrewList(@RequestBody CrewQueryVO queryVO, HttpServletResponse response) {
        try {
            byte[] bytes = crewBasicInfoService.exportCrewListXlsx(queryVO);
            String ts = new java.text.SimpleDateFormat("yyyyMMddHHmmss").format(new java.util.Date());
            String fileName = URLEncoder.encode("船员列表导出_" + ts + ".xlsx", StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.getOutputStream().write(bytes);
            response.flushBuffer();
        } catch (Exception e) {
            // 导出接口直接写流，无统一Result返回
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }
} 