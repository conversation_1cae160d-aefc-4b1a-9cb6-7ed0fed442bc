# 船员年度报告API接口文档

## 概述

本文档描述了船员年度报告系统的API接口规范，包含年份选项查询、年度报告数据获取和PDF生成功能。

**基础信息**
- 服务地址：`http://localhost:8285`
- API前缀：`/api/annual-report`
- 数据格式：JSON
- 字符编码：UTF-8

## 通用响应格式

所有API接口都采用统一的响应格式：

```json
{
  "success": true,           // 请求是否成功
  "message": "success",      // 响应消息
  "code": 200,              // 响应状态码
  "timestamp": 1750671455297, // 时间戳
  "result": {}              // 业务数据
}
```

**响应状态码说明**
- `200`: 请求成功
- `500`: 服务器内部错误

---

## 1. 获取年份选项列表

### 接口信息
- **接口地址**: `GET /api/annual-report/year-options`
- **接口描述**: 获取可查询的年度报告年份选项列表
- **请求方式**: GET

### 请求参数
无

### 响应参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| success | boolean | 是 | 请求是否成功 |
| message | string | 是 | 响应消息 |
| code | integer | 是 | 响应状态码 |
| timestamp | long | 是 | 时间戳 |
| result | array | 是 | 年份选项数组 |

**result数组元素结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| value | integer | 是 | 年份值 |
| label | string | 是 | 年份标签 |

### 请求示例

```bash
GET /api/annual-report/year-options
```

### 响应示例

**成功响应**
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1750671294067,
  "result": [
    {
      "value": 2024,
      "label": "2024年"
    },
    {
      "value": 2023,
      "label": "2023年"
    }
  ]
}
```

**失败响应**
```json
{
  "success": false,
  "message": "获取年份选项失败: 数据库连接异常",
  "code": 500,
  "timestamp": 1750671294067,
  "result": null
}
```

---

## 2. 获取年度报告数据

### 接口信息
- **接口地址**: `GET /api/annual-report/data`
- **接口描述**: 根据年份和船员ID获取年度报告详细数据
- **请求方式**: GET

### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| year | integer | 是 | 查询年份 | 2024 |
| seafarerId | string | 否 | 船员ID，不传则根据当前登录用户获取 | "123456" |

### 响应参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| success | boolean | 是 | 请求是否成功 |
| message | string | 是 | 响应消息 |
| code | integer | 是 | 响应状态码 |
| timestamp | long | 是 | 时间戳 |
| result | object | 是 | 年度报告数据对象 |

**result对象结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| basicInfo | object | 是 | 基本信息 |
| statistics | object | 是 | 统计数据 |
| shipStatus | object | 是 | 船舶状态 |
| certificates | object | 是 | 证书信息 |

**basicInfo对象结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | string | 是 | 船员姓名 |
| idNumber | string | 是 | 身份证号 |
| avatar | string | 是 | 头像URL |

**statistics对象结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| violationScoreTotal | integer | 是 | 违法违规扣分总计 |
| scoreReduction | string | 是 | 减分情况 |
| clearanceDate | string | 是 | 清零日期 |
| serviceDaysTotal | integer | 是 | 服务天数总计 |
| unlimitedAreaDays | integer | 是 | 无限航区服务天数 |
| coastalAreaDays | integer | 是 | 沿海航区服务天数 |

**shipStatus对象结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| currentStatus | string | 是 | 当前状态（在船/休假） |
| shipName | string | 是 | 船舶名称 |
| shipType | string | 是 | 船舶类型 |
| grossTonnage | string | 是 | 总吨位 |
| position | string | 是 | 职务 |
| boardingDate | string | 是 | 上船日期 |
| departurePort | string | 是 | 出发港口 |
| destinationPort | string | 是 | 目的港口 |

**certificates对象结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| competency | array | 是 | 适任证书列表 |
| training | array | 是 | 培训证书列表 |
| health | array | 是 | 健康证书列表 |

### 请求示例

```bash
# 带船员ID查询
GET /api/annual-report/data?year=2024&seafarerId=123456

# 不带船员ID查询（使用当前登录用户）
GET /api/annual-report/data?year=2024
```

### 响应示例

**成功响应**
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1750671455888,
  "result": {
    "basicInfo": {
      "name": "船员123456",
      "idNumber": "***",
      "avatar": ""
    },
    "statistics": {
      "violationScoreTotal": 3,
      "scoreReduction": "0分",
      "clearanceDate": "2025-06-15",
      "serviceDaysTotal": 285,
      "unlimitedAreaDays": 180,
      "coastalAreaDays": 105
    },
    "shipStatus": {
      "currentStatus": "在船",
      "shipName": "远洋货轮001",
      "shipType": "散货船",
      "grossTonnage": "50000",
      "position": "船长",
      "boardingDate": "2024-03-15 00:00:00",
      "departurePort": "上海港",
      "destinationPort": "未知"
    },
    "certificates": {
      "competency": [],
      "training": [],
      "health": []
    }
  }
}
```

**失败响应**
```json
{
  "success": false,
  "message": "获取年度报告数据失败: 未找到指定年份的年度报告数据",
  "code": 500,
  "timestamp": 1750671294686,
  "result": null
}
```

---

## 3. 生成年度报告PDF

### 接口信息
- **接口地址**: `POST /api/annual-report/generate-pdf`
- **接口描述**: 根据年份和船员ID生成年度报告PDF文件
- **请求方式**: POST
- **Content-Type**: application/json

### 请求参数

**请求体结构**

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| year | integer | 是 | 报告年份 | 2024 |
| seafarerId | string | 否 | 船员ID，不传则使用当前登录用户 | "123456" |

### 响应参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| success | boolean | 是 | 请求是否成功 |
| message | string | 是 | 响应消息 |
| code | integer | 是 | 响应状态码 |
| timestamp | long | 是 | 时间戳 |
| result | object | 是 | PDF生成结果对象 |

**result对象结构**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| fileUrl | string | 是 | PDF文件下载地址 |
| fileName | string | 是 | PDF文件名称 |
| fileSize | string | 是 | 文件大小 |
| generateTime | string | 是 | 生成时间 |

### 请求示例

```bash
POST /api/annual-report/generate-pdf
Content-Type: application/json

{
  "year": 2024,
  "seafarerId": "123456"
}
```

### 响应示例

**成功响应**
```json
{
  "success": true,
  "message": "success",
  "code": 200,
  "timestamp": 1750671457073,
  "result": {
    "fileUrl": "https://example.com/reports/annual-report-2024-123456.pdf",
    "fileName": "年度报告-2024.pdf",
    "fileSize": "2.5MB",
    "generateTime": "2025-06-23 17:37:37"
  }
}
```

**失败响应 - 参数错误**
```json
{
  "success": false,
  "message": "年份参数不能为空",
  "code": 500,
  "timestamp": 1750671457174,
  "result": null
}
```

**失败响应 - 系统错误**
```json
{
  "success": false,
  "message": "生成PDF失败: 模板文件不存在",
  "code": 500,
  "timestamp": 1750671457174,
  "result": null
}
```

---

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 500 | 服务器内部错误 | 检查请求参数格式，联系后端开发人员 |

## 常见错误信息

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "年份参数不能为空" | year参数未传递或为null | 确保传递有效的年份参数 |
| "未找到指定年份的年度报告数据" | 指定年份没有数据 | 检查年份是否正确，或联系管理员 |
| "获取年份选项失败" | 数据库连接异常 | 联系后端开发人员检查数据库连接 |
| "生成PDF失败" | PDF生成过程出错 | 检查模板文件和数据完整性 |

## 接口测试

推荐使用以下工具进行接口测试：
- Postman
- curl命令
- 浏览器开发者工具

**curl测试示例**

```bash
# 1. 获取年份选项
curl -X GET "http://localhost:8285/api/annual-report/year-options"

# 2. 获取年度报告数据
curl -X GET "http://localhost:8285/api/annual-report/data?year=2024&seafarerId=123456"

# 3. 生成PDF
curl -X POST "http://localhost:8285/api/annual-report/generate-pdf" \
  -H "Content-Type: application/json" \
  -d '{"year": 2024, "seafarerId": "123456"}'
```

## 注意事项

1. 所有时间字段格式为：`yyyy-MM-dd HH:mm:ss`
2. 船员ID为可选参数，不传递时使用当前登录用户信息
3. PDF生成为异步操作，建议前端添加loading状态
4. 文件下载地址有效期为24小时
5. 建议对接口调用频率进行限制，避免过于频繁的请求

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2025-06-23 | 初始版本，包含基础功能接口 | 