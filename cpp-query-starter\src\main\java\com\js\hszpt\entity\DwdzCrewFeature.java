package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 船员特征表实体类
 * 对应表：dwdz_crew_feature
 */
@Data
@TableName("dwdz_crew_feature")
public class DwdzCrewFeature {

    /**
     * 特征ID（主键）
     */
    @TableId("feature_id")
    private String featureId;

    /**
     * 船员ID
     */
    @TableField("crew_id")
    private String crewId;

    /**
     * 触发类别ID
     */
    @TableField("trigger_category_id")
    private String triggerCategoryId;

    /**
     * 触发类别名称
     */
    @TableField("trigger_category_name")
    private String triggerCategoryName;

    /**
     * 触发类别编码
     */
    @TableField("trigger_category_code")
    private String triggerCategoryCode;

    /**
     * 特征类型
     */
    @TableField("feature_type")
    private String featureType;

    /**
     * 特征值
     */
    @TableField("feature_value")
    private String featureValue;

    /**
     * 规则计算描述
     */
    @TableField("rule_calculation_desc")
    private String ruleCalculationDesc;

    /**
     * 计算时间
     */
    @TableField("calc_time")
    private Date calcTime;

    /**
     * 预警级别
     */
    @TableField("warning_level")
    private String warningLevel;

    /**
     * 创建时间
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 修改时间
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

}