package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 船员考试信息配置表实体类
 * 用于管理船员考试科目配置信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_exam_config")
public class DwdzCrewExamConfig {

    /**
     * 节点唯一标识ID（主键）
     */
    @TableId(value = "exam_id", type = IdType.ASSIGN_UUID)
    private String examId;

    /**
     * 科目名称
     */
    @TableField("subject_name")
    private String subjectName;

    /**
     * 考试时长(分钟)
     */
    @TableField("exam_duration")
    private Integer examDuration;

    /**
     * 试卷代码
     */
    @TableField("paper_code")
    private String paperCode;

    /**
     * 适用对象
     */
    @TableField("target_object")
    private String targetObject;

    /**
     * 及格分数
     */
    @TableField("passing_score")
    private String passingScore;

    /**
     * 及格分数第二部分
     */
    @TableField("section_pass_score")
    private String sectionPassScore;

    /**
     * 考试类型 0:适任证理论考试　1:机考评估　2:现场评估　3:合格证理论考试
     */
    @TableField("exam_type")
    private Integer examType;

    /**
     * 证书类别 0(海船船员适任证书) 2(海上游艇操作人员) 5(培训合格证)
     */
    @TableField("cert_type")
    private Integer certType;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private LocalDateTime recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private LocalDateTime recModifyDate;
} 