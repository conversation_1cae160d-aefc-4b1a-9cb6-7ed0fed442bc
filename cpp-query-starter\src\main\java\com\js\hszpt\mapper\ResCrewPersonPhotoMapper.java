package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.ResCrewPersonPhoto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 船员照片表Mapper接口
 */
@DS("dzzzdwdb")
public interface ResCrewPersonPhotoMapper extends BaseMapper<ResCrewPersonPhoto> {
    
    /**
     * 根据身份证号码查询船员照片
     * @param idcardNo 身份证号码
     * @return 船员照片信息
     */
    ResCrewPersonPhoto selectByIdcardNo(@Param("idcardNo") String idcardNo);
} 