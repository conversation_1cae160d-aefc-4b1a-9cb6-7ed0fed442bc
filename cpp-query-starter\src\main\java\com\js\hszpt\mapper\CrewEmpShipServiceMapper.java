package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.CrewEmpShipService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员任职期内船舶服务信息Mapper接口
 */
@Mapper
public interface CrewEmpShipServiceMapper extends BaseMapper<CrewEmpShipService> {
    
    /**
     * 根据任职关系ID查询船舶服务信息
     * @param employmentId 任职关系ID
     * @return 船舶服务信息列表
     */
    List<CrewEmpShipService> selectByEmploymentId(@Param("employmentId") String employmentId);
    
    /**
     * 根据船员ID查询所有船舶服务信息
     * @param crewId 船员ID
     * @return 船舶服务信息列表
     */
    List<CrewEmpShipService> selectByCrewId(@Param("crewId") String crewId);
    
    /**
     * 根据任职关系ID列表查询船舶服务信息
     * @param employmentIds 任职关系ID列表
     * @return 船舶服务信息列表
     */
    List<CrewEmpShipService> selectByEmploymentIds(@Param("employmentIds") List<String> employmentIds);
} 