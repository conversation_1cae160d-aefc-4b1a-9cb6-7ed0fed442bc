package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 船员培训项目科目表实体类
 * 用于管理船员培训项目和科目信息
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_train_subject")
public class DwdzCrewTrainSubject {

    /**
     * 节点唯一标识ID（主键）
     */
    @TableId(value = "subject_id", type = IdType.ASSIGN_UUID)
    private String subjectId;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目代码
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * 科目名称
     */
    @TableField("subject_name")
    private String subjectName;

    /**
     * 科目代码
     */
    @TableField("subject_code")
    private String subjectCode;

    /**
     * 科目类型
     */
    @TableField("subject_type")
    private String subjectType;

    /**
     * 证书类别 
     * 0:海船船员适任证书 1:海船船员培训合格证书 2:海上设施工作人员培训合格证明
     * 6:海港引航员适任证书 7:非自航船船员证书 8:特定航线江海直达船舶船员行驶资格证明
     */
    @TableField("cert_type")
    private Integer certType;

    /**
     * 证书名称
     */
    @TableField("cert_name")
    private String certName;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private LocalDateTime recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private LocalDateTime recModifyDate;
} 