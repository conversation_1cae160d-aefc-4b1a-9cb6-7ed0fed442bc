package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 船员群体层级关系表实体类
 * 用于管理船员群体的树形层级结构数据
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName("dwdz_crew_position_hierarchy")
public class DwdzCppPositionHierarchy {

    /**
     * 唯一标识
     */
    @TableId(value = "hierarchy_id", type = IdType.ASSIGN_UUID)
    private String hierarchyId;

    /**
     * 父级ID
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 船员类别名称
     */
    @TableField("name")
    private String name;

    /**
     * 船员数量
     */
    @TableField("value")
    private Long value;

    /**
     * 持证数量
     */
    @TableField("certified_count")
    private Long certifiedCount;

    /**
     * 需求数量
     */
    @TableField("demand_count")
    private Long demandCount;

    /**
     * 在船数量
     */
    @TableField("onboard_count")
    private Long onboardCount;

    /**
     * 排序序号（用于前端展示顺序控制）
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 节点展示方向（left:左侧 right:右侧）
     */
    @TableField("display_direction")
    private String displayDirection;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

    /**
     * 群体介绍
     */
    @TableField("group_introduction")
    private String groupIntroduction;

    /**
     * 群体人数统计说明
     */
    @TableField("static_introduction")
    private String staticIntroduction;

    /**
     * 描述信息（非数据库字段，用于API返回）
     */
    @TableField(exist = false)
    private String description;

    /**
     * 子节点列表（非数据库字段，用于树形结构）
     */
    @TableField(exist = false)
    private List<DwdzCppPositionHierarchy> children;
} 