<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewSvWarningInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewSvWarningInfo">
        <id column="warning_id" property="warningId" jdbcType="VARCHAR"/>
        <result column="crew_id" property="crewId" jdbcType="VARCHAR"/>
        <result column="warning_title" property="warningTitle" jdbcType="VARCHAR"/>
        <result column="warning_description" property="warningDescription" jdbcType="VARCHAR"/>
        <result column="warning_date" property="warningDate" jdbcType="DATE"/>
        <result column="warning_level" property="warningLevel" jdbcType="VARCHAR"/>
        <result column="is_read" property="isRead" jdbcType="BOOLEAN"/>
        <result column="trigger_rule_id" property="triggerRuleId" jdbcType="VARCHAR"/>
        <result column="trigger_rule_name" property="triggerRuleName" jdbcType="VARCHAR"/>
        <result column="handle_instruction" property="handleInstruction" jdbcType="VARCHAR"/>
        <result column="rule_calculation_desc" property="ruleCalculationDesc" jdbcType="VARCHAR"/>
        <result column="trigger_category_id" property="triggerCategoryId" jdbcType="VARCHAR"/>
        <result column="trigger_category_name" property="triggerCategoryName" jdbcType="VARCHAR"/>
        <result column="rec_create_date" property="recCreateDate" jdbcType="TIMESTAMP"/>
        <result column="rec_modify_date" property="recModifyDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        warning_id, crew_id, warning_title, warning_description, warning_date, 
        warning_level, is_read, trigger_rule_id, trigger_rule_name, 
        handle_instruction, rule_calculation_desc, trigger_category_id, 
        trigger_category_name, rec_create_date, rec_modify_date
    </sql>

    <!-- 根据船员ID查询预警信息 -->
    <select id="selectByCrewId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_sv_warning_info
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        ORDER BY warning_date DESC, rec_create_date DESC
    </select>

    <!-- 根据船员ID和预警级别查询预警信息 -->
    <select id="selectByCrewIdAndLevel" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_sv_warning_info
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
        <if test="warningLevel != null and warningLevel != ''">
            AND warning_level = #{warningLevel,jdbcType=VARCHAR}
        </if>
        ORDER BY warning_date DESC, rec_create_date DESC
    </select>

    <!-- 更新预警信息为已读状态 -->
    <update id="updateAsRead" parameterType="java.lang.String">
        UPDATE dwdz_crew_sv_warning_info
        SET is_read = true,
            rec_modify_date = CURRENT_TIMESTAMP
        WHERE warning_id = #{warningId,jdbcType=VARCHAR}
    </update>

</mapper> 