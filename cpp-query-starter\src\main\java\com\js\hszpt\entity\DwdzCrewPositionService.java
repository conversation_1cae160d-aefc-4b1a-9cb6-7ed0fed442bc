package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 船员在不同职务上的履职统计表
 */
@Data
@TableName("dwdz_crew_position_service")
@ApiModel(value = "船员职务履职统计表")
public class DwdzCrewPositionService {
    @TableId
    @ApiModelProperty(value = "职务履职记录唯一标识")
    private String positionId;

    @ApiModelProperty(value = "关联船员基本信息表的船员ID")
    private String crewId;

    @ApiModelProperty(value = "职务名称（如：船长、大副、轮机长等）")
    private String positionName;

    @ApiModelProperty(value = "履职船舶类型（如：集装箱船、散货船等）")
    private String shipType;

    @ApiModelProperty(value = "履职总月数（含折算月数，保留一位小数）")
    private BigDecimal totalMonths;

    @ApiModelProperty(value = "是否首次任职该职务（true:是, false:否）")
    private Boolean isFirstPosition;

    @ApiModelProperty(value = "晋升自哪个职务（如：从大副晋升为船长）")
    private String promotionFrom;

    @ApiModelProperty(value = "晋升时长（单位：年，保留一位小数）")
    private BigDecimal promotionDuration;

    @ApiModelProperty(value = "记录创建日期")
    private Date recCreateDate;

    @ApiModelProperty(value = "记录修改日期")
    private Date recModifyDate;
} 