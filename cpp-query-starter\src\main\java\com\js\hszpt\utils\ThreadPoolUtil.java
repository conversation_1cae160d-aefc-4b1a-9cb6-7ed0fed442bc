package com.js.hszpt.utils;

import lombok.Getter;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置
 */
public class ThreadPoolUtil {
    private static final BlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(100);
    private static final int SIZE_CORE_POOL = 5;
    private static final int SIZE_MAX_POOL = 10;
    private static final long ALIVE_TIME = 2000L;
    @Getter
    private static final ThreadPoolExecutor pool;

    static {
        pool = new ThreadPoolExecutor(5, 10, 2000L, TimeUnit.MILLISECONDS, queue, new ThreadPoolExecutor.CallerRunsPolicy());
        pool.prestartAllCoreThreads();
    }


}
