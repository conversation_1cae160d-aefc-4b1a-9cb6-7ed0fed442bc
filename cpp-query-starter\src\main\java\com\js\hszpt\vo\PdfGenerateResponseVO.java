package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PDF生成响应VO
 * @ClassName: PdfGenerateResponseVO
 * @Description: PDF生成响应VO类
 * @author: System Generation
 */
@Data
@ApiModel(value = "PDF生成响应VO")
public class PdfGenerateResponseVO {

    /**
     * PDF文件下载链接
     */
    @ApiModelProperty(value = "PDF文件下载链接")
    private String fileUrl;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    /**
     * 生成时间
     */
    @ApiModelProperty(value = "生成时间")
    private String generateTime;
} 