package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.service.DwdzCrewDefectRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 船员缺陷记录API接口
 * 
 * <AUTHOR> Generation
 */
@Slf4j
@RestController
@RequestMapping("/api/crew/defect")
@Api(tags = "船员缺陷记录管理")
public class DwdzCrewDefectRecordApi {

    @Autowired
    private DwdzCrewDefectRecordService defectRecordService;

    /**
     * 根据检查编号查询缺陷记录
     * 
     * @param inspectNo 检查编号
     * @return 缺陷记录列表
     */
    @GetMapping("/byInspectNo")
    @ApiOperation(value = "根据检查编号查询缺陷记录", notes = "根据检查编号查询缺陷记录列表")
    public Result<List<Map<String, Object>>> getDefectRecordsByInspectNo(
            @ApiParam(value = "检查编号", required = true) @RequestParam String inspectNo) {
        try {
            log.info("根据检查编号查询缺陷记录，检查编号：{}", inspectNo);
            List<Map<String, Object>> defectRecords = defectRecordService.getDefectRecordsForDisplay(inspectNo);
            return ResultUtil.data(defectRecords);
        } catch (Exception e) {
            log.error("根据检查编号查询缺陷记录失败，检查编号：{}", inspectNo, e);
            return ResultUtil.error("查询缺陷记录失败：" + e.getMessage());
        }
    }

    /**
     * 根据检查编号列表批量查询缺陷记录
     * 
     * @param inspectNoList 检查编号列表
     * @return 缺陷记录Map（key为检查编号，value为缺陷记录列表）
     */
    @PostMapping("/batchByInspectNo")
    @ApiOperation(value = "根据检查编号列表批量查询缺陷记录", notes = "根据检查编号列表批量查询缺陷记录")
    public Result<Map<String, List<Map<String, Object>>>> getDefectRecordsBatchByInspectNo(
            @ApiParam(value = "检查编号列表", required = true) @RequestBody List<String> inspectNoList) {
        try {
            log.info("根据检查编号列表批量查询缺陷记录，检查编号数量：{}", inspectNoList.size());
            Map<String, List<Map<String, Object>>> defectRecords = defectRecordService.getDefectRecordsForDisplayBatch(inspectNoList);
            return ResultUtil.data(defectRecords);
        } catch (Exception e) {
            log.error("根据检查编号列表批量查询缺陷记录失败", e);
            return ResultUtil.error("批量查询缺陷记录失败：" + e.getMessage());
        }
    }

    /**
     * 根据来源类型和检查编号查询缺陷记录
     * 
     * @param sourceType 来源类型
     * @param inspectNo 检查编号
     * @return 缺陷记录列表
     */
    @GetMapping("/bySourceTypeAndInspectNo")
    @ApiOperation(value = "根据来源类型和检查编号查询缺陷记录", notes = "根据来源类型和检查编号查询缺陷记录列表")
    public Result<List<Map<String, Object>>> getDefectRecordsBySourceTypeAndInspectNo(
            @ApiParam(value = "来源类型（SITE:现场检查/FSC:船旗国/PSC:港口国）", required = true) @RequestParam String sourceType,
            @ApiParam(value = "检查编号", required = true) @RequestParam String inspectNo) {
        try {
            log.info("根据来源类型和检查编号查询缺陷记录，来源类型：{}，检查编号：{}", sourceType, inspectNo);
            List<Map<String, Object>> defectRecords = defectRecordService.getDefectRecordsForDisplay(inspectNo);
            return ResultUtil.data(defectRecords);
        } catch (Exception e) {
            log.error("根据来源类型和检查编号查询缺陷记录失败，来源类型：{}，检查编号：{}", sourceType, inspectNo, e);
            return ResultUtil.error("查询缺陷记录失败：" + e.getMessage());
        }
    }
} 