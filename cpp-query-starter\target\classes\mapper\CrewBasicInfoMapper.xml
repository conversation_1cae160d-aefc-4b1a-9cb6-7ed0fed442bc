<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CrewBasicInfoMapper">
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        crew_id, full_name, used_name, id_number, gender, birth_date, native_place, 
        nationality, nation, phone, address, education, degree, graduate_school, 
        major, graduation_date, diploma_number, collection_place, register_org, 
        photo_url, emergency_contact, emergency_phone, create_by, create_time, 
        update_by, update_time, rec_create_date, rec_modify_date
    </sql>

    <!-- 关联查询船员列表（包含当前状态信息）- 优化版本，一次查询返回数据和总数 -->
    <select id="selectCrewListWithStatusOptimized" resultType="com.js.hszpt.vo.CrewListVO">
        SELECT
            b.crew_id,
            b.full_name,
            b.id_number,
            b.phone,
            b.gender,
            b.birth_date,
            b.native_place,
            b.education,
            b.photo_url,
            b.update_time,
            s.current_status,
            s.onboard_status,
            s.ship_name,
            s.ship_name_en,
            s.ship_imo,
            s.ship_mmsi,
            s.ship_type,
            s.home_port,
            s.registry_port_authority,
            s.position,
            s.position_level,
            s.navi_area,
            s.current_location,
            s.onboard_date,
            s.position_start_date,
            s.dispatch_company_name,
            s.dispatch_company_region,
            s.ship_management_company,
            s.ship_owner_company,
            s.ship_owner_region,
            s.management_company_region,
            s.ship_operator_company,
            s.operator_region,
            s.dispatch_agent_region,
            s.is_dispatched,
            <!-- 使用窗口函数获取总数 -->
            COUNT(*) OVER() AS total_count
        FROM dwdz_crew_basic_info b
        LEFT JOIN dwdz_crew_current_status s ON b.crew_id = s.crew_id
        <where>
            <!-- 关键词模糊查询-->
            <if test="queryVO.keyword != null and queryVO.keyword != ''">
                AND (
                b.full_name like '%' || #{queryVO.keyword} || '%'
                OR b.id_number like '%' || #{queryVO.keyword} || '%'
                )
            </if>
            <!-- 单独船员姓名查询 -->
            <if test="queryVO.seafarerName != null and queryVO.seafarerName != ''">
                AND b.full_name like '%' || #{queryVO.seafarerName} || '%'
            </if>
            <!-- 单独身份证查询 -->
            <if test="queryVO.idCard != null and queryVO.idCard != ''">
                AND b.id_number like '%' || #{queryVO.idCard} || '%'
            </if>
            <!-- 证书类型查询 -->
            <if test="queryVO.certificateType != null and queryVO.certificateType != ''">
                AND EXISTS (SELECT 1 FROM dwdz_crew_current_cert cc WHERE cc.crew_id = b.crew_id AND cc.cert_category = #{queryVO.certificateType})
            </if>
            <!-- 证书号码查询 -->
            <if test="queryVO.certificateNumber != null and queryVO.certificateNumber != ''">
                AND EXISTS (SELECT 1 FROM dwdz_crew_current_cert cc WHERE cc.crew_id = b.crew_id AND cc.cert_number like '%' || #{queryVO.certificateNumber} || '%')
            </if>
            <!-- 状态查询（无需修改） -->
            <if test="queryVO.status != null and queryVO.status != '' and queryVO.status != 'all'">
                AND s.onboard_status = #{queryVO.status}
            </if>
            <!-- 港口模糊查询 -->
            <if test="queryVO.port != null and queryVO.port != ''">
                AND (
                s.home_port like '%' || #{queryVO.port} || '%'
                OR s.current_location like '%' || #{queryVO.port} || '%'
                )
            </if>
            <!-- 机构过滤：当前位置所属机构编码 -->
            <if test="queryVO.orgCode != null and queryVO.orgCode != ''">
                AND s.current_location_org like concat(#{queryVO.orgCode}, '%')
            </if>
            <!-- 船舶中文、英文名称模糊查询 -->
            <if test="queryVO.shipName != null and queryVO.shipName != ''">
                AND exists(select 1 from dwdz_crew_current_status where crew_id = b.crew_id
                AND (s.ship_name like '%' || #{queryVO.shipName} || '%'
                    OR s.ship_name_en like '%' || #{queryVO.shipName} || '%'
                    )
                )
            </if>
            <!-- 船舶识别号、imo、mmsi模糊查询 -->
            <if test="queryVO.shipId != null and queryVO.shipId != ''">
                AND exists(select 1 from dwdz_crew_current_status where crew_id = b.crew_id
                AND (s.ship_id like '%' || #{queryVO.shipId} || '%'
                    OR s.ship_imo like '%' || #{queryVO.shipId} || '%'
                    OR s.ship_mmsi like '%' || #{queryVO.shipId} || '%'
                    )
                )
            </if>
            <!-- 职位模糊查询 -->
            <if test="queryVO.position != null and queryVO.position != ''">
                AND s.position like '%' || #{queryVO.position} || '%'
            </if>
            <!-- 特征标签查询 -->
            <if test="queryVO.featureTags != null and queryVO.featureTags != ''">
                AND exists(select 1 from dwdz_crew_feature where crew_id = b.crew_id
                <!-- 仅匹配“现场监管标签”类别 -->
                AND trigger_category_code like 'SITE_%'
                AND trigger_category_id in
                <foreach collection="queryVO.featureTags.split(',')" item="tag" open="(" close=")" separator=",">
                    #{tag}
                </foreach>)
            </if>
            <!-- 标签组查询条件 - 聚合优化版本 -->
            <if test="queryVO.tagGroupConditions != null and queryVO.tagGroupConditions.size() > 0">
                <bind name="conditionCount" value="queryVO.tagGroupConditions.size()" />
                AND b.crew_id IN (
                    SELECT cf.crew_id
                    FROM dwdz_crew_feature cf
                    WHERE (
                        <foreach collection="queryVO.tagGroupConditions" item="condition" separator=" OR ">
                            <choose>
                                <when test="condition.tagValueType == 'NONE'">
                                    <!-- 无值类型，只需匹配标签编码 -->
                                    cf.trigger_category_code = #{condition.tagCode}
                                </when>
                                <when test="condition.tagValueType == 'NUMBER'">
                                    <!-- 数字类型条件 -->
                                    (cf.trigger_category_code = #{condition.tagCode}
                                    <choose>
                                        <when test="condition.filterCondition != null and condition.filterCondition.startsWith('=')">
                                            <bind name="equalValue" value="condition.filterCondition.substring(1)" />
                                            AND CAST(cf.feature_value AS DECIMAL) = CAST(#{equalValue} AS DECIMAL))
                                        </when>
                                        <when test="condition.filterCondition != null and condition.filterCondition.startsWith('>')">
                                            <choose>
                                                <when test="condition.filterCondition.contains('并且') and condition.filterCondition.contains('__LT__')">
                                                    <bind name="minValue" value="condition.filterCondition.substring(1, condition.filterCondition.indexOf('并且'))" />
                                                    <bind name="maxValue" value="condition.filterCondition.substring(condition.filterCondition.indexOf('__LT__') + 6)" />
                                                    AND CAST(cf.feature_value AS DECIMAL) > CAST(#{minValue} AS DECIMAL)
                                                    AND CAST(cf.feature_value AS DECIMAL) <![CDATA[ < ]]> CAST(#{maxValue} AS DECIMAL))
                                                </when>
                                                <otherwise>
                                                    <bind name="compareValue" value="condition.filterCondition.substring(1)" />
                                                    AND CAST(cf.feature_value AS DECIMAL) > CAST(#{compareValue} AS DECIMAL))
                                                </otherwise>
                                            </choose>
                                        </when>
                                        <when test="condition.filterCondition != null and condition.filterCondition.startsWith('__LT__')">
                                            <bind name="lessThanValue" value="condition.filterCondition.substring(6)" />
                                            AND CAST(cf.feature_value AS DECIMAL) <![CDATA[ < ]]> CAST(#{lessThanValue} AS DECIMAL))
                                        </when>
                                        <otherwise>
                                            )
                                        </otherwise>
                                    </choose>
                                </when>
                                <when test="condition.tagValueType == 'STRING'">
                                    <!-- 字符串类型，支持单值匹配和多值IN查询 -->
                                    (cf.trigger_category_code = #{condition.tagCode}
                                    <choose>
                                        <when test="condition.multiValue == true">
                                            <!-- 多值，使用IN查询 -->
                                            AND cf.feature_value IN
                                            <foreach collection="condition.filterConditionValues" item="value" open="(" close=")" separator=",">
                                                #{value}
                                            </foreach>
                                        </when>
                                        <otherwise>
                                            <!-- 单值，使用精确匹配 -->
                                            AND cf.feature_value like ('%' || #{condition.filterCondition} || '%')
                                        </otherwise>
                                    </choose>)
                                </when>
                                <when test="condition.tagValueType == 'DATE'">
                                    <!-- 日期类型条件 - 安全处理年份和日期 -->
                                    (cf.trigger_category_code = #{condition.tagCode}
                                    <choose>
                                        <when test="condition.filterCondition != null and condition.filterCondition.startsWith('=')">
                                            <bind name="equalDateValue" value="condition.filterCondition.substring(1)" />
                                            AND (
                                                (LENGTH(cf.feature_value) = 4 AND cf.feature_value = #{equalDateValue}) OR
                                                (LENGTH(cf.feature_value) > 4 AND LENGTH(#{equalDateValue}) = 4 AND EXTRACT(YEAR FROM cf.feature_value::date) = CAST(#{equalDateValue} AS INTEGER)) OR
                                                (LENGTH(cf.feature_value) > 4 AND LENGTH(#{equalDateValue}) > 4 AND cf.feature_value::date = #{equalDateValue}::date)
                                            ))
                                        </when>
                                        <when test="condition.filterCondition != null and condition.filterCondition.startsWith('>')">
                                            <choose>
                                                <when test="condition.filterCondition.contains('并且') and condition.filterCondition.contains('__LT__')">
                                                    <bind name="minDateValue" value="condition.filterCondition.substring(1, condition.filterCondition.indexOf('并且'))" />
                                                    <bind name="maxDateValue" value="condition.filterCondition.substring(condition.filterCondition.indexOf('__LT__') + 6)" />
                                                    AND (
                                                        (LENGTH(cf.feature_value) = 4 AND CAST(cf.feature_value AS INTEGER) > CAST(SUBSTRING(#{minDateValue}, 1, 4) AS INTEGER) AND CAST(cf.feature_value AS INTEGER) <![CDATA[ < ]]> CAST(SUBSTRING(#{maxDateValue}, 1, 4) AS INTEGER)) OR
                                                        (LENGTH(cf.feature_value) > 4 AND LENGTH(#{minDateValue}) = 4 AND LENGTH(#{maxDateValue}) = 4 AND EXTRACT(YEAR FROM cf.feature_value::date) > CAST(#{minDateValue} AS INTEGER) AND EXTRACT(YEAR FROM cf.feature_value::date) <![CDATA[ < ]]> CAST(#{maxDateValue} AS INTEGER)) OR
                                                        (LENGTH(cf.feature_value) > 4 AND (LENGTH(#{minDateValue}) > 4 OR LENGTH(#{maxDateValue}) > 4) AND cf.feature_value::date > #{minDateValue}::date AND cf.feature_value::date <![CDATA[ < ]]> #{maxDateValue}::date)
                                                    ))
                                                </when>
                                                <otherwise>
                                                    <bind name="greaterDateValue" value="condition.filterCondition.substring(1)" />
                                                    AND (
                                                        (LENGTH(cf.feature_value) = 4 AND CAST(cf.feature_value AS INTEGER) > CAST(SUBSTRING(#{greaterDateValue}, 1, 4) AS INTEGER)) OR
                                                        (LENGTH(cf.feature_value) > 4 AND LENGTH(#{greaterDateValue}) = 4 AND EXTRACT(YEAR FROM cf.feature_value::date) > CAST(#{greaterDateValue} AS INTEGER)) OR
                                                        (LENGTH(cf.feature_value) > 4 AND LENGTH(#{greaterDateValue}) > 4 AND cf.feature_value::date > #{greaterDateValue}::date)
                                                    ))
                                                </otherwise>
                                            </choose>
                                        </when>
                                        <when test="condition.filterCondition != null and condition.filterCondition.startsWith('__LT__')">
                                            <bind name="lessThanDateValue" value="condition.filterCondition.substring(6)" />
                                            AND (
                                                (LENGTH(cf.feature_value) = 4 AND CAST(cf.feature_value AS INTEGER) <![CDATA[ < ]]> CAST(SUBSTRING(#{lessThanDateValue}, 1, 4) AS INTEGER)) OR
                                                (LENGTH(cf.feature_value) > 4 AND LENGTH(#{lessThanDateValue}) = 4 AND EXTRACT(YEAR FROM cf.feature_value::date) <![CDATA[ < ]]> CAST(#{lessThanDateValue} AS INTEGER)) OR
                                                (LENGTH(cf.feature_value) > 4 AND LENGTH(#{lessThanDateValue}) > 4 AND cf.feature_value::date <![CDATA[ < ]]> #{lessThanDateValue}::date)
                                            ))
                                        </when>
                                        <otherwise>
                                            )
                                        </otherwise>
                                    </choose>
                                </when>
                            </choose>
                        </foreach>
                    )
                    GROUP BY cf.crew_id
                    HAVING COUNT(DISTINCT cf.trigger_category_code) = #{conditionCount}
                )
            </if>
        </where>
        <choose>
            <when test="queryVO.sort != null and queryVO.sort != ''">
                <choose>
                    <when test="queryVO.sort == 'name'">
                        ORDER BY b.full_name 
                        <if test="queryVO.order == 'asc'">ASC</if>
                        <if test="queryVO.order == 'desc'">DESC</if>
                    </when>
                    <when test="queryVO.sort == 'idCard'">
                        ORDER BY b.id_number 
                        <if test="queryVO.order == 'asc'">ASC</if>
                        <if test="queryVO.order == 'desc'">DESC</if>
                    </when>
                    <when test="queryVO.sort == 'position'">
                        ORDER BY s.position 
                        <if test="queryVO.order == 'asc'">ASC</if>
                        <if test="queryVO.order == 'desc'">DESC</if>
                    </when>
                    <when test="queryVO.sort == 'shipName'">
                        ORDER BY s.ship_name 
                        <if test="queryVO.order == 'asc'">ASC</if>
                        <if test="queryVO.order == 'desc'">DESC</if>
                    </when>
                    <otherwise>
                        ORDER BY b.update_time DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <!-- 默认序 -->
                ORDER BY tag_score desc,red_tag_count desc,yellow_tag_count desc,supervision_tag_count desc,crew_id
            </otherwise>
        </choose>
        <!-- 分页控制：当pageSize > 0时分页，否则导出所有数据（限制最大10000条） -->
        <if test="queryVO.pageSize > 0">
            LIMIT #{queryVO.pageSize} OFFSET #{queryVO.offset}
        </if>
        <if test="queryVO.pageSize == null or queryVO.pageSize &lt;= 0">
            LIMIT 10000
        </if>
    </select>


    <!-- 分页查询船员列表 -->
    <select id="selectCrewList" resultType="com.js.hszpt.entity.CrewBasicInfo">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_basic_info
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                    full_name LIKE '%' || #{keyword} || '%'
                    OR id_number LIKE '%' || #{keyword} || '%'
                )
            </if>
            <if test="status != null and status != '' and status != 'all'">
                AND status = #{status}
            </if>
        </where>
        ORDER BY rec_create_date DESC
    </select>

    <!-- 查询特征标签 -->
    <select id="selectFeatureTags" resultType="java.util.Map">
        SELECT DISTINCT feature_type as "name", feature_type as "value"
        FROM dwdz_cpp_crew_feature
        ORDER BY feature_type
    </select>

    <!-- 查询港口列表 -->
    <select id="selectPorts" resultType="java.lang.String">
        SELECT DISTINCT home_port
        FROM dwdz_crew_current_status
        WHERE home_port IS NOT NULL AND home_port != ''
        <if test="query != null and query != ''">
            AND home_port LIKE CONCAT('%', #{query}, '%')
        </if>
        ORDER BY home_port
        LIMIT 10
    </select>

    <!-- 查询船名列表 -->
    <select id="selectShips" resultType="java.lang.String">
        SELECT DISTINCT ship_name
        FROM dwdz_crew_current_status
        WHERE ship_name IS NOT NULL AND ship_name != ''
        <if test="query != null and query != ''">
            AND (ship_name LIKE CONCAT('%', #{query}, '%') OR ship_name_en LIKE CONCAT('%', #{query}, '%'))
        </if>
        ORDER BY ship_name
        LIMIT 10
    </select>

    <!-- 查询当前职务列表 -->
    <select id="selectPositions" resultType="java.lang.String">
        SELECT DISTINCT position
        FROM dwdz_crew_current_status
        WHERE position IS NOT NULL AND position != ''
        <if test="query != null and query != ''">
            AND position LIKE CONCAT('%', #{query}, '%')
        </if>
        ORDER BY position
        LIMIT 10
    </select>

    <!-- 查询船舶识别号列表（包含IMO、MMSI） -->
    <select id="selectShipIdentifiers" resultType="java.lang.String">
        SELECT DISTINCT ship_id
        FROM dwdz_crew_current_status
        WHERE ship_id IS NOT NULL AND ship_id != ''
        <if test="query != null and query != ''">
            AND (ship_id LIKE CONCAT('%', #{query}, '%') 
                OR ship_imo LIKE CONCAT('%', #{query}, '%') 
                OR ship_mmsi LIKE CONCAT('%', #{query}, '%'))
        </if>
        ORDER BY ship_id
        LIMIT 10
    </select>
</mapper> 