package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.CrewAnnualReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员年度报告数据处理层
 * 
 * @ClassName: CrewAnnualReportDao
 * @Description: 船员年度报告数据处理层
 * @author: System Generation
 */
public interface CrewAnnualReportDao extends BaseMapper<CrewAnnualReport> {

    /**
     * 获取推送消息列表
     *
     * @return 推送消息列表
     */
    List<CrewAnnualReport> getPushMessageList();

    /**
     * 获取可查询的年份列表
     * 
     * @return 年份列表
     */
    List<String> getAvailableYears();

    /**
     * 根据船员ID和年份查询年度报告
     * 
     * @param crewId     船员ID
     * @param reportYear 报告年份
     * @return 年度报告
     */
    CrewAnnualReport getByCrewIdAndYear(@Param("crewId") String crewId, @Param("reportYear") String reportYear);

    /**
     * 根据船员ID查询基本信息
     * 
     * @param crewId 船员ID
     * @return 基本信息Map
     */
    java.util.Map<String, Object> getCrewBasicInfo(@Param("crewId") String crewId);

    /**
     * 获取船舶详细信息
     * 
     * @param reportId 报告ID
     * @return 船舶信息Map
     */
    java.util.Map<String, Object> getShipDetailInfo(@Param("reportId") String reportId);

    /**
     * 根据身份证号查询船员ID
     * 
     * @param idNumber 身份证号
     * @return 船员ID
     */
    String getCrewIdByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据船员ID查询当前状态信息
     * 
     * @param crewId 船员ID
     * @return 船员当前状态信息Map
     */
    java.util.Map<String, Object> getCrewCurrentStatus(@Param("crewId") String crewId);
    
    /**
     * 根据完整职务名称查询晋升关系列表
     * 
     * @param fullPositionName 完整职务名称
     * @return 晋升关系列表
     */
    List<java.util.Map<String, Object>> getPromotionRelationsByPosition(@Param("fullPositionName") String fullPositionName);
}