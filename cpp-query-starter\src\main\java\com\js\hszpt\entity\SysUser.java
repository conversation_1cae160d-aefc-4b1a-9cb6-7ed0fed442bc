package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;


/**
 * 
 * @ClassName:  SysUser   
 * @Description:TODO(用户表)   
 * @author:   System Generation 
 */
@Data
@Builder
@TableName("sys_user")
@ApiModel(value = "用户表")
public class SysUser extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 主键ID
    */
    @ApiModelProperty(value = "主键ID")
    private Long userId;


    /**
    * 用户名
    */
    @ApiModelProperty(value = "用户名")
    private String username;


    /**
    * 密码
    */
    @ApiModelProperty(value = "密码")
    private String password;


    /**
    * 随机盐
    */
    @ApiModelProperty(value = "随机盐")
    private String salt;


    /**
    * 电话号码
    */
    @ApiModelProperty(value = "电话号码")
    private String phone;


    /**
    * 头像
    */
    @ApiModelProperty(value = "头像")
    private String avatar;


    /**
    * 部门ID
    */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;


    /**
    * 0-正常，9-锁定
    */
    @ApiModelProperty(value = "0-正常，9-锁定")
    private String lockFlag;


    /**
    * 0-正常，1-删除
    */
    @ApiModelProperty(value = "0-正常，1-删除")
    private String delFlag;


    /**
    * 微信openid
    */
    @ApiModelProperty(value = "微信openid")
    private String wxOpenid;


    /**
    * QQ openid
    */
    @ApiModelProperty(value = "QQ openid")
    private String qqOpenid;


    /**
    * 身份识别号
    */
    @ApiModelProperty(value = "身份识别号")
    private String certNo;


    /**
    * 身份识别号散列码
    */
    @ApiModelProperty(value = "身份识别号散列码")
    private String certKey;


    /**
    * 身份类型 NA 自然人  CP 法人  MA 管理员
    */
    @ApiModelProperty(value = "身份类型 NA 自然人  CP 法人  MA 管理员")
    private String subjectType;


    /**
    * 用户称谓
    */
    @ApiModelProperty(value = "用户称谓")
    private String name;


    /**
    * 法人类型
    */
    @ApiModelProperty(value = "法人类型")
    private String corpType;


    /**
    * 代理人
    */
    @ApiModelProperty(value = "代理人")
    private String legalName;


    /**
    * 用户令牌
    */
    @ApiModelProperty(value = "用户令牌")
    private String tokenSno;


    /**
    * 主账户（管理员）
    */
    @ApiModelProperty(value = "主账户（管理员）")
    private String mainAcctId;


    /**
    * 邮箱
    */
    @ApiModelProperty(value = "邮箱")
    private String email;


    /**
    * 生效时间
    */
    @ApiModelProperty(value = "生效时间")
    private String effectDate;


    /**
    * 失效时间
    */
    @ApiModelProperty(value = "失效时间")
    private String expireDate;


    /**
    * 账号描述
    */
    @ApiModelProperty(value = "账号描述")
    private String acctDesc;


    /**
    * 4A侧用户ID
    */
    @ApiModelProperty(value = "4A侧用户ID")
    private String mainUserId;


    /**
    * 性别
    */
    @ApiModelProperty(value = "性别")
    private String gender;


    /**
    * 地址（自然人住址/法人公司地址）
    */
    @ApiModelProperty(value = "地址（自然人住址/法人公司地址）")
    private String address;


    /**
    * 身份证类型
    */
    @ApiModelProperty(value = "身份证类型")
    private String certType;


    /**
    * 从账户类型
    */
    @ApiModelProperty(value = "从账户类型")
    private String acctType;


    /**
    * 经办人身份证
    */
    @ApiModelProperty(value = "经办人身份证")
    private String legalCertNo;


    /**
    * 从账号ID
    */
    @ApiModelProperty(value = "从账号ID")
    private String subUserId;


    /**
    * 部门ID DEPT_TYPE=2
    */
    @ApiModelProperty(value = "部门ID DEPT_TYPE=2")
    private String department;


    /**
    * 职务ID
    */
    @ApiModelProperty(value = "职务ID")
    private String dutyId;


    /**
    * 部门code
    */
    @ApiModelProperty(value = "部门code")
    private String dutyCode;


    /**
    * 部门名称
    */
    @ApiModelProperty(value = "部门名称")
    private String deptName;


    /**
    * 部门名称
    */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;


}