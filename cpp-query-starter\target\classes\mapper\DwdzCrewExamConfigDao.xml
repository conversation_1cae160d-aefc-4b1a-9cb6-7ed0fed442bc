<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewExamConfigDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewExamConfig">
        <id column="exam_id" property="examId" />
        <result column="subject_name" property="subjectName" />
        <result column="exam_duration" property="examDuration" />
        <result column="paper_code" property="paperCode" />
        <result column="target_object" property="targetObject" />
        <result column="passing_score" property="passingScore" />
        <result column="section_pass_score" property="sectionPassScore" />
        <result column="exam_type" property="examType" />
        <result column="cert_type" property="certType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        exam_id, subject_name, exam_duration, paper_code, target_object, 
        passing_score, section_pass_score, exam_type, cert_type
    </sql>

    <!-- 根据考试ID列表查询考试科目信息 -->
    <select id="selectByExamIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dwdz_crew_exam_config
        WHERE exam_id IN
        <foreach collection="examIds" item="examId" open="(" separator="," close=")">
            #{examId}
        </foreach>
        ORDER BY exam_type ASC, subject_name ASC
    </select>

</mapper> 