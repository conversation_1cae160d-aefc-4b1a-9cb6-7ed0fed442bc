package com.js.hszpt.service;

import com.js.hszpt.entity.DwdzCrewPositionReqImgcfg;
import com.js.hszpt.mapper.DwdzCrewPositionReqImgcfgDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 晋升要求图片配置缓存服务
 * 在应用启动时加载配置数据到内存，提供高效的图片配置查询
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class PromotionImageConfigCacheService implements ApplicationRunner {

    @Autowired
    private DwdzCrewPositionReqImgcfgDao imgConfigDao;

    /**
     * 图片配置映射：source_file_id -> DwdzCrewPositionReqImgcfg
     */
    private final Map<String, DwdzCrewPositionReqImgcfg> imageConfigMap = new ConcurrentHashMap<>();

    /**
     * source_file_id集合，用于快速匹配
     */
    private final Set<String> sourceFileIdSet = ConcurrentHashMap.newKeySet();

    @Override
    public void run(ApplicationArguments args) throws Exception {
        loadImageConfigs();
    }

    /**
     * 加载图片配置数据到内存
     */
    public void loadImageConfigs() {
        try {
            log.info("开始加载晋升要求图片配置数据到内存...");
            
            List<DwdzCrewPositionReqImgcfg> configs = imgConfigDao.selectAllConfigs();
            
            // 清空现有缓存
            imageConfigMap.clear();
            sourceFileIdSet.clear();
            
            // 加载新数据到缓存
            for (DwdzCrewPositionReqImgcfg config : configs) {
                if (config.getSourceFileId() != null && !config.getSourceFileId().trim().isEmpty()) {
                    imageConfigMap.put(config.getSourceFileId(), config);
                    sourceFileIdSet.add(config.getSourceFileId());
                }
            }
            
            log.info("成功加载{}个图片配置到内存缓存", configs.size());
            
        } catch (Exception e) {
            log.error("加载晋升要求图片配置数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("加载图片配置数据失败", e);
        }
    }

    /**
     * 检查字符串内容是否包含任何source_file_id
     * 
     * @param content 要检查的字符串内容
     * @return 匹配的source_file_id，如果没有匹配则返回null
     */
    public String findMatchingSourceFileId(String content) {
        if (content == null || content.trim().isEmpty()) {
            return null;
        }
        
        for (String sourceFileId : sourceFileIdSet) {
            if (content.contains(sourceFileId)) {
                return sourceFileId;
            }
        }
        
        return null;
    }

    /**
     * 根据source_file_id获取图片配置
     * 
     * @param sourceFileId 源文件ID
     * @return 图片配置对象，如果不存在则返回null
     */
    public DwdzCrewPositionReqImgcfg getImageConfig(String sourceFileId) {
        return imageConfigMap.get(sourceFileId);
    }

    /**
     * 构建图片访问URL
     * 
     * @param sourceFileId 源文件ID
     * @param isIntranet 是否为内网访问（true=内网，false=外网）
     * @return 图片访问URL，如果配置不存在则返回null
     */
    public String buildImageUrl(String sourceFileId, boolean isIntranet) {
        DwdzCrewPositionReqImgcfg config = getImageConfig(sourceFileId);
        if (config == null) {
            return null;
        }
        
        String baseUrl = isIntranet ? config.getIntranetUrl() : config.getOutsideUrl();
        String fileName = config.getVisitFileName();
        
        if (baseUrl != null && fileName != null) {
            // 确保baseUrl以斜杠结尾
            if (!baseUrl.endsWith("/")) {
                baseUrl += "/";
            }
            return baseUrl + fileName;
        }
        
        return null;
    }

    /**
     * 刷新缓存（手动触发重新加载）
     */
    public void refreshCache() {
        loadImageConfigs();
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalConfigs", imageConfigMap.size());
        stats.put("sourceFileIds", sourceFileIdSet.size());
        return stats;
    }
}
