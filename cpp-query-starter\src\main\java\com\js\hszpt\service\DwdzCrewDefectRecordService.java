package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.hszpt.entity.DwdzCrewDefectRecord;

import java.util.List;
import java.util.Map;

/**
 * 通用缺陷记录表Service接口
 * 
 * <AUTHOR> Generation
 */
public interface DwdzCrewDefectRecordService extends IService<DwdzCrewDefectRecord> {

    /**
     * 根据检查编号查询缺陷记录
     * 
     * @param inspectNo 检查编号
     * @return 缺陷记录列表
     */
    List<DwdzCrewDefectRecord> getByInspectNo(String inspectNo);

    /**
     * 根据检查编号列表批量查询缺陷记录
     * 
     * @param inspectNoList 检查编号列表
     * @return 缺陷记录列表
     */
    List<DwdzCrewDefectRecord> getByInspectNoList(List<String> inspectNoList);

    /**
     * 根据来源类型和检查编号查询缺陷记录
     * 
     * @param sourceType 来源类型
     * @param inspectNo 检查编号
     * @return 缺陷记录列表
     */
    List<DwdzCrewDefectRecord> getBySourceTypeAndInspectNo(String sourceType, String inspectNo);

    /**
     * 根据检查编号查询缺陷记录并转换为前端展示格式
     * 
     * @param inspectNo 检查编号
     * @return 缺陷记录列表（前端展示格式）
     */
    List<Map<String, Object>> getDefectRecordsForDisplay(String inspectNo);

    /**
     * 根据检查编号列表批量查询缺陷记录并转换为前端展示格式
     * 
     * @param inspectNoList 检查编号列表
     * @return 缺陷记录Map（key为检查编号，value为缺陷记录列表）
     */
    Map<String, List<Map<String, Object>>> getDefectRecordsForDisplayBatch(List<String> inspectNoList);
} 