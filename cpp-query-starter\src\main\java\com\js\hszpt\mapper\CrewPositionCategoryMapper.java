package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.CrewPositionCategory;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 船员职务名称分类表Mapper接口
 */
@Mapper
public interface CrewPositionCategoryMapper extends BaseMapper<CrewPositionCategory> {

    /**
     * 查询所有职务分类列表（按排序字段排序）
     */
    List<CrewPositionCategory> selectAllOrderBySort();
}