package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewPositionService;
import com.js.hszpt.mapper.DwdzCrewPositionServiceMapper;
import com.js.hszpt.service.DwdzCrewPositionServiceService;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 船员职务履职统计 Service实现
 */
@Service
public class DwdzCrewPositionServiceServiceImpl extends ServiceImpl<DwdzCrewPositionServiceMapper, DwdzCrewPositionService>
        implements DwdzCrewPositionServiceService {

    @Override
    public List<DwdzCrewPositionService> getByCrewId(String crewId) {
        return this.baseMapper.selectByCrewId(crewId);
    }
} 