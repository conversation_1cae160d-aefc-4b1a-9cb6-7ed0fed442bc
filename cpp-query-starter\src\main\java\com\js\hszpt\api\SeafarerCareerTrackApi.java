package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.DwdzCrewCareerTrack;
import com.js.hszpt.entity.DwdzCrewCurrentCert;
import com.js.hszpt.service.DwdzCrewCareerTrackService;
import com.js.hszpt.service.DwdzCrewCurrentCertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 船员职业生涯成长轨迹API接口
 * 提供成长轨迹的增删改查及按船员ID查询等功能
 */
@RestController
@RequestMapping("/api/seafarer/career-track")
public class SeafarerCareerTrackApi {

    @Autowired
    private DwdzCrewCareerTrackService careerTrackService;

    @Autowired
    private DwdzCrewCurrentCertService currentCertService;

    /**
     * 根据船员ID查询成长轨迹（按track_order升序）
     * 
     * @param crewId 船员ID
     * @return 成长轨迹列表
     */
    @GetMapping("/{crewId}")
    public Result<List<DwdzCrewCareerTrack>> getCareerTrackByCrewId(@PathVariable String crewId) {
        List<DwdzCrewCareerTrack> list = careerTrackService.getCareerTrackByCrewId(crewId);
        return ResultUtil.data(list);
    }

    /**
     * 根据 crewId 与 type 查询职务类别对应的适任证书（按 issue_date 倒序）
     * 示例：/career-track-posi/110101199001011234?type=1
     */
    @GetMapping("/career-track-posi/{crewId}")
    public Result<List<DwdzCrewCareerTrack>> getCareerTrackPosition(@PathVariable String crewId,
            @RequestParam(required = false, defaultValue = "") String type) {
        List<DwdzCrewCareerTrack> list = currentCertService.getCareerTrackByCrewId(crewId, type);
        return ResultUtil.data(list);
    }

    /**
     * 新增成长轨迹
     */
    @PostMapping
    public Result<Boolean> addCareerTrack(@RequestBody DwdzCrewCareerTrack track) {
        return ResultUtil.data(careerTrackService.save(track));
    }

    /**
     * 修改成长轨迹
     */
    @PutMapping
    public Result<Boolean> updateCareerTrack(@RequestBody DwdzCrewCareerTrack track) {
        return ResultUtil.data(careerTrackService.updateById(track));
    }

    /**
     * 删除成长轨迹
     */
    @DeleteMapping("/{trackId}")
    public Result<Boolean> deleteCareerTrack(@PathVariable String trackId) {
        return ResultUtil.data(careerTrackService.removeById(trackId));
    }

    /**
     * 根据trackId查详情
     */
    @GetMapping("/detail/{trackId}")
    public Result<DwdzCrewCareerTrack> getDetail(@PathVariable String trackId) {
        return ResultUtil.data(careerTrackService.getById(trackId));
    }
}