package  com.js.hszpt;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskExecutor;
// import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * 启动类
 * <AUTHOR>
 *
 */
//启用JPA审计 - 已注释，项目使用MyBatis
//@EnableJpaAuditing
@SpringBootApplication(exclude = {
    org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class,
    org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration.class
})
//启用异步
@EnableAsync
//启用自带定时任务
@EnableScheduling
//启用缓存
@EnableCaching(order=2)
@EnableTransactionManagement(order=1)
@MapperScan({"com.js.**.mapper","com.js.**.dao"})
@ComponentScan(basePackages = { "com.js"})
//SpringBoot项目的注解扫描默认规则是根据Application类所在的包位置从上往下扫描！配置扫描com.js以下
public class CppStarterApplication {

    public static void main(String[] args) {
        SpringApplication.run(CppStarterApplication.class, args);
    }
    
    @Primary
    @Bean
    public TaskExecutor primaryTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        return executor;
    }
}
