package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCppPositionCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 职务分类表DAO接口
 * 提供职务分类数据的数据库访问方法
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCppPositionCategoryDao extends BaseMapper<DwdzCppPositionCategory> {

    /**
     * 根据职位编码获取职务分类信息
     * 
     * @param positionCode 职位编码
     * @return 职务分类信息
     */
    DwdzCppPositionCategory getCategoryByPositionCode(@Param("positionCode") String positionCode);

    /**
     * 获取所有职务的人数统计映射
     * 用于职业结构组件的人数统计显示
     * 
     * @return 职务编码和人数的映射关系
     */
    List<Map<String, Object>> getPositionCountsMap();

    /**
     * 根据职位编码获取单个职务人数
     * 
     * @param positionCode 职位编码
     * @return 职务人数
     */
    Integer getPositionCountByCode(@Param("positionCode") String positionCode);

    /**
     * 获取包含子分类信息的职务分类列表
     * 
     * @return 职务分类列表（包含子分类）
     */
    List<DwdzCppPositionCategory> getCategoriesWithSubcategories();

    /**
     * 根据职务级别获取职务分类列表
     * 
     * @param positionLevel 职务级别（management/operational/support）
     * @return 职务分类列表
     */
    List<DwdzCppPositionCategory> getCategoriesByLevel(@Param("positionLevel") String positionLevel);
} 