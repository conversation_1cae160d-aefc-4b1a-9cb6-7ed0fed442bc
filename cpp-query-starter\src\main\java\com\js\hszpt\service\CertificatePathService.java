package com.js.hszpt.service;

import java.util.Map;

/**
 * 证书路径服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface CertificatePathService {
    
    /**
     * 获取证书路径数据
     * @param certificateType 证书类型
     * @return 证书路径数据
     */
    Map<String, Object> getCertificatePath(String certificateType);
    
    /**
     * 获取证书路径数据（平铺查询+Java构建树）
     * @param certificateType 证书类型名称（如"海船员船员适任证书"）
     * @return 证书路径数据
     */
    Map<String, Object> getCertificatePathFlat(String certificateType);
    
    /**
     * 搜索证书节点
     * @param keyword 搜索关键词
     * @param limit 限制数量
     * @return 搜索结果
     */
    Map<String, Object> searchCertificateNodes(String keyword, Integer limit);
    
    /**
     * 获取证书晋升要求详情
     * @param requestData 请求数据
     * @return 晋升要求详情
     */
    Map<String, Object> getCertificatePromotionRequirements(Map<String, Object> requestData);

    /**
     * 获取证书类型列表
     * @return 证书类型列表
     */
    Map<String, Object> getCertificateTypes();
} 