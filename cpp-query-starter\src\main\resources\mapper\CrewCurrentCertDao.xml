<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CrewCurrentCertDao">

    <!-- 证书结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.CrewCurrentCert">
        <id column="cert_id" property="certId" jdbcType="VARCHAR"/>
        <result column="report_id" property="reportId" jdbcType="VARCHAR"/>
        <result column="cert_category" property="certCategory" jdbcType="VARCHAR"/>
        <result column="cert_name" property="certName" jdbcType="VARCHAR"/>
        <result column="cert_number" property="certNumber" jdbcType="VARCHAR"/>
        <result column="issue_date" property="issueDate"/>
        <result column="expiry_date" property="expiryDate"/>
        <result column="cert_status" property="certStatus" jdbcType="VARCHAR"/>
        <result column="expiring_flag" property="expiringFlag" jdbcType="BOOLEAN"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        cert_id, report_id, cert_category, cert_name, cert_number, issue_date, 
        expiry_date, cert_status, expiring_flag, rec_create_date, rec_modify_date
    </sql>

    <!-- 根据报告ID查询证书列表 -->
    <select id="getByReportId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_ar_current_cert
        WHERE report_id = #{reportId}
        ORDER BY cert_category, expiry_date DESC
    </select>

    <!-- 根据报告ID和证书类别查询证书列表 -->
    <select id="getByReportIdAndCategory" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_ar_current_cert
        WHERE report_id = #{reportId}
        AND cert_category = #{certCategory}
        ORDER BY expiry_date DESC
    </select>

</mapper> 