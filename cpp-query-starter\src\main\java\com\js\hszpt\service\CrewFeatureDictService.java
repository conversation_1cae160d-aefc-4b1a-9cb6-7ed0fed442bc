package com.js.hszpt.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewFeatureDict;
import com.js.hszpt.mapper.DwdzCrewFeatureDictDao;
import com.js.hszpt.vo.TagValueSuggestionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 
 * @ClassName: CrewFeatureDictService    
 * @Description: 船员特征值字典表服务实现
 * @author: System Generation
 */
@Slf4j
@Service
public class CrewFeatureDictService extends ServiceImpl<DwdzCrewFeatureDictDao, DwdzCrewFeatureDict> {

    /**
     * 根据标签编码和查询文本获取标签值建议列表
     * 
     * @param tagCode 标签编码
     * @param queryText 查询文本
     * @param limit 限制返回条数，默认10条
     * @return 标签值建议列表
     */
    public List<TagValueSuggestionVo> getTagValueSuggestions(String tagCode, String queryText, Integer limit) {
        try {
            // 参数校验
            if (StrUtil.isBlank(tagCode)) {
                log.warn("标签编码不能为空");
                return Collections.emptyList();
            }

            if (StrUtil.isBlank(queryText)) {
                log.warn("查询文本不能为空");
                return Collections.emptyList();
            }

            // 设置默认限制条数
            if (limit == null || limit <= 0) {
                limit = 10;
            }

            // 最多返回20条
            if (limit > 20) {
                limit = 20;
            }

            log.info("查询标签值建议，tagCode={}, queryText={}, limit={}", tagCode, queryText, limit);

            // 调用Mapper查询
            List<TagValueSuggestionVo> suggestions = baseMapper.getTagValueSuggestions(tagCode, queryText, limit);

            log.info("查询到{}条标签值建议", suggestions != null ? suggestions.size() : 0);

            return suggestions != null ? suggestions : Collections.emptyList();

        } catch (Exception e) {
            log.error("查询标签值建议失败，tagCode={}, queryText={}, error={}", tagCode, queryText, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
