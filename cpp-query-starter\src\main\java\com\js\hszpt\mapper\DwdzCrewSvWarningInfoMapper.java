package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewSvWarningInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员履职预警信息主表Mapper接口
 * <AUTHOR> Generation
 */
@Mapper
public interface DwdzCrewSvWarningInfoMapper extends BaseMapper<DwdzCrewSvWarningInfo> {

    /**
     * 根据船员ID查询预警信息
     * @param crewId 船员ID
     * @return 预警信息列表
     */
    List<DwdzCrewSvWarningInfo> selectByCrewId(@Param("crewId") String crewId);

    /**
     * 根据船员ID和预警级别查询预警信息
     * @param crewId 船员ID
     * @param warningLevel 预警级别
     * @return 预警信息列表
     */
    List<DwdzCrewSvWarningInfo> selectByCrewIdAndLevel(@Param("crewId") String crewId, @Param("warningLevel") String warningLevel);

    /**
     * 更新预警信息为已读状态
     * @param warningId 预警ID
     * @return 更新结果
     */
    int updateAsRead(@Param("warningId") String warningId);
} 