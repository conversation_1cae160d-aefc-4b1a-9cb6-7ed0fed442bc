package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 
 * @ClassName: DwdzCrewFeatureDict   
 * @Description: 船员特征值字典表
 * @author: System Generation 
 */
@Data
@TableName("dwdz_crew_feature_dict")
@ApiModel(value = "船员特征值字典表")
public class DwdzCrewFeatureDict {

    private static final long serialVersionUID = 1L;

    /**
     * 特征记录字典的唯一标识符
     */
    @TableId
    @ApiModelProperty(value = "特征记录字典的唯一标识符")
    private String featureDictId;

    /**
     * 特征类别编码（关联cpp_fi_rule_category表的category_code）
     */
    @ApiModelProperty(value = "特征类别编码")
    private String triggerCategoryCode;

    /**
     * 特征值，存储数字统计类标签的值
     */
    @ApiModelProperty(value = "特征值")
    private String featureValue;

    /**
     * 记录创建日期
     */
    @ApiModelProperty(value = "记录创建日期")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @ApiModelProperty(value = "记录修改日期")
    private Date recModifyDate;
}
