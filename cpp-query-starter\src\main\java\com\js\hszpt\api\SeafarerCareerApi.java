package com.js.hszpt.api;

import cn.hutool.core.util.StrUtil;
import com.js.annotation.SystemLog;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.enums.LogType;
import com.js.hszpt.service.*;
import com.js.hszpt.vo.CompanyRelationsStatisticsVO;
import com.js.hszpt.vo.CompanyRelationsDetailVO;
import com.js.hszpt.vo.TagValueSuggestionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 船员生涯履历管理API接口
 * <AUTHOR> Generation
 */
@Slf4j
@RestController
@Api(description = "船员生涯履历管理API接口")
@RequestMapping("/api/seafarer")
public class SeafarerCareerApi {

    @Autowired
    private CrewBasicInfoService crewBasicInfoService;

    @Autowired
    private DwdzCrewSummaryService crewSummaryService;

    @Autowired
    private DwdzCrewTimelineService crewTimelineService;

    @Autowired
    private DwdzCrewSvWarningInfoService crewWarningInfoService;

    @Autowired
    private CrewCompanyRelationsService crewCompanyRelationsService;

    @Autowired
    private CrewFeatureDictService crewFeatureDictService;

    /**
     * 获取船员基本信息
     */
    @SystemLog(description = "获取船员基本信息", type = LogType.OPERATION)
    @RequestMapping(value = "/basic-info/{seafarerId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取船员基本信息")
    public Result<Map<String, Object>> getSeafarerBasicInfo(
            @ApiParam(value = "船员ID", required = true) @PathVariable String seafarerId) {
        log.info("获取船员基本信息，船员ID：{}", seafarerId);
        
        try {
            Map<String, Object> result = crewBasicInfoService.getSeafarerBasicInfo(seafarerId);
            if (result == null) {
                return ResultUtil.error("船员信息不存在");
            }
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取船员基本信息失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取船员基本信息失败");
        }
    }

    /**
     * 获取船员身份证号明文（谨慎开放）
     */
    @SystemLog(description = "获取船员身份证号明文", type = LogType.OPERATION)
    @RequestMapping(value = "/basic-info/id-number/{seafarerId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取船员身份证号明文")
    public Result<String> getSeafarerIdNumberPlain(
            @ApiParam(value = "船员ID", required = true) @PathVariable String seafarerId) {
        log.info("获取船员身份证号明文，船员ID：{}", seafarerId);
        try {
            String idNumber = crewBasicInfoService.getSeafarerIdNumberPlain(seafarerId);
            if (StrUtil.isBlank(idNumber)) {
                return ResultUtil.error("未查询到身份证号");
            }
            return ResultUtil.data(idNumber);
        } catch (Exception e) {
            log.error("获取船员身份证号明文失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取船员身份证号失败");
        }
    }

    /**
     * 获取船员履历时间轴数据（按年份分组）
     */
    @SystemLog(description = "获取船员履历时间轴数据", type = LogType.OPERATION)
    @RequestMapping(value = "/timeline", method = RequestMethod.GET)
    @ApiOperation(value = "获取船员履历时间轴数据（按年份分组）")
    public Result<List<Map<String, Object>>> getTimelineDataV1(
            @ApiParam(value = "船员ID") @RequestParam(required = false) String seafarerId,
            @ApiParam(value = "指定年份") @RequestParam(required = false) Integer year) {
        log.info("获取船员履历时间轴数据V1，船员ID：{}，年份：{}", seafarerId, year);
        
        try {
            if (StrUtil.isBlank(seafarerId)) {
                return ResultUtil.data(new ArrayList<>(),"获取船员履历时间轴数据为空");
            }
            List<Map<String, Object>> timelineData = crewTimelineService.getTimelineDataV1(seafarerId, year);
            return ResultUtil.data(timelineData);
        } catch (Exception e) {
            log.error("获取船员履历时间轴数据失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取船员履历时间轴数据失败");
        }
    }

    /**
     * 获取职业发展统计数据
     */
    @SystemLog(description = "获取职业发展统计数据", type = LogType.OPERATION)
    @RequestMapping(value = "/career-analysis/{seafarerId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取职业发展统计数据")
    public Result<Object> getCareerAnalysisData(
            @ApiParam(value = "船员ID", required = true) @PathVariable String seafarerId) {
        log.info("获取职业发展统计数据，船员ID：{}", seafarerId);
        
        try {
            Object careerData = crewSummaryService.getCareerAnalysisData(seafarerId);
            return ResultUtil.data(careerData);
        } catch (Exception e) {
            log.error("获取职业发展统计数据失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取职业发展统计数据失败");
        }
    }

    /**
     * 获取公司关系数据
     */
    @SystemLog(description = "获取公司关系数据", type = LogType.OPERATION)
    @RequestMapping(value = "/company-relations/{seafarerId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取公司关系数据")
    public Result<Map<String, Object>> getCompanyRelationsData(
            @ApiParam(value = "船员ID", required = true) @PathVariable String seafarerId,
            @ApiParam(value = "公司类型筛选") @RequestParam(required = false) List<String> companyTypes) {
        log.info("获取公司关系数据，船员ID：{}，公司类型：{}", seafarerId, companyTypes);
        try {
            Map<String, Object> result = new HashMap<>();
            CompanyRelationsStatisticsVO statistics = crewCompanyRelationsService.getCompanyRelationsStatistics(seafarerId);
            List<CompanyRelationsDetailVO> list = crewCompanyRelationsService.getCompanyRelationsList(seafarerId, companyTypes);
            result.put("statistics", statistics);
            result.put("list", list);
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取公司关系数据失败，船员ID：{}，公司类型：{}", seafarerId, companyTypes, e);
            return ResultUtil.error("获取公司关系数据失败");
        }
    }

    /**
     * 获取履职时间轴数据
     * 
     */
    @SystemLog(description = "获取履职时间轴数据", type = LogType.OPERATION)
    @RequestMapping(value = "/cross-validation/timeline/{seafarerId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取履职时间轴数据")
    public Result<Map<String, Object>> getCrossValidationTimeline(
            @ApiParam(value = "船员ID", required = true) @PathVariable String seafarerId,
            @ApiParam(value = "开始日期") @RequestParam(required = false) String dateStart,
            @ApiParam(value = "结束日期") @RequestParam(required = false) String dateEnd,
            @ApiParam(value = "职务类型") @RequestParam(required = false) String dutyType,
            @ApiParam(value = "船舶类型") @RequestParam(required = false) String shipType,
            @ApiParam(value = "动态类型") @RequestParam(required = false) String dynamicType,
            @ApiParam(value = "业务数据类型") @RequestParam(required = false) String businessData) {
        log.info("获取履职时间轴数据，船员ID：{}", seafarerId);
        
        try {
            Map<String, Object> result = crewTimelineService.getCrossValidationTimeline(seafarerId, dateStart, dateEnd, dutyType, shipType, dynamicType, businessData);
            
            // 【Controller验证日志】确认Controller层接收到的数据状态
//            System.out.println("[Controller日志] ===== Controller层数据验证 =====");
//            System.out.println("[Controller日志] result大小: " + result.size());
//            System.out.println("[Controller日志] result包含sailor: " + result.containsKey("sailor"));
//            System.out.println("[Controller日志] result包含timelineData: " + result.containsKey("timelineData"));
            
//            List<Map<String, Object>> controllerTimelineData = (List<Map<String, Object>>) result.get("timelineData");
//            System.out.println("[Controller日志] Controller层timelineData大小: " + (controllerTimelineData != null ? controllerTimelineData.size() : "null"));
            
//            if (controllerTimelineData != null) {
//                for (int i = 0; i < controllerTimelineData.size(); i++) {
//                    Map<String, Object> period = controllerTimelineData.get(i);
//                    List<Map<String, Object>> periodDynamics = (List<Map<String, Object>>) period.get("dynamics");
//                    System.out.println("[Controller日志] Controller层周期" + i + " - dynamics大小: " + (periodDynamics != null ? periodDynamics.size() : "null"));
//                }
//            }
//            System.out.println("[Controller日志] ===== Controller验证完成 =====");
            
            // 【JSON序列化测试】直接测试序列化过程
//            try {
//                ObjectMapper objectMapper = new ObjectMapper();
//                String jsonString = objectMapper.writeValueAsString(result);
//                System.out.println("[JSON测试] ===== JSON序列化结果 =====");
//                System.out.println("[JSON测试] JSON长度: " + jsonString.length());
//                System.out.println("[JSON测试] 是否包含dynamics: " + jsonString.contains("dynamics"));
//                System.out.println("[JSON测试] 是否包含空数组: " + jsonString.contains("\"dynamics\":[]"));
//                System.out.println("[JSON测试] JSON片段: " + jsonString.substring(0, Math.min(500, jsonString.length())));
//                System.out.println("[JSON测试] ===== JSON测试完成 =====");
//            } catch (Exception e) {
//                System.out.println("[JSON测试] 序列化异常: " + e.getMessage());
//            }
            
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("获取履职时间轴数据失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取履职时间轴数据失败");
        }
    }

    /**
     * 获取预警信息
     */
    @SystemLog(description = "获取预警信息", type = LogType.OPERATION)
    @RequestMapping(value = "/cross-validation/warnings/{seafarerId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取预警信息")
    public Result<List<Map<String, Object>>> getWarnings(
            @ApiParam(value = "船员ID", required = true) @PathVariable String seafarerId) {
        log.info("获取预警信息，船员ID：{}", seafarerId);
        
        try {
            List<Map<String, Object>> warnings = crewWarningInfoService.getWarnings(seafarerId);
            return ResultUtil.data(warnings);
        } catch (Exception e) {
            log.error("获取预警信息失败，船员ID：{}", seafarerId, e);
            return ResultUtil.error("获取预警信息失败");
        }
    }

    /**
     * 获取船员特征标签数据
     */
    @SystemLog(description = "获取船员特征标签数据", type = LogType.OPERATION)
    @RequestMapping(value = "/feature-tags/{crewId}", method = RequestMethod.GET)
    @ApiOperation(value = "获取船员特征标签数据")
    public Result<Map<String, List<Map<String, Object>>>> getSeafarerFeatureTags(
            @ApiParam(value = "船员ID", required = true) @PathVariable String crewId) {
        log.info("获取船员特征标签数据，船员ID：{}", crewId);
        
        try {
            Map<String, List<Map<String, Object>>> featureTags = crewBasicInfoService.getSeafarerFeatureTags(crewId);
            return ResultUtil.data(featureTags);
        } catch (Exception e) {
            log.error("获取船员特征标签数据失败，船员ID：{}", crewId, e);
            return ResultUtil.error("获取船员特征标签数据失败");
        }
    }

    /**
     * 获取船员照片
     */
    @SystemLog(description = "获取船员照片", type = LogType.OPERATION)
    @RequestMapping(value = "/photo/{idcardNo}", method = RequestMethod.GET)
    @ApiOperation(value = "获取船员照片（base64格式）")
    public Result<String> getSeafarerPhoto(
            @ApiParam(value = "身份证号码", required = true) @PathVariable String idcardNo) {
        log.info("获取船员照片，身份证号：{}", idcardNo);
        
        try {
            String base64Photo = crewBasicInfoService.getSeafarerPhoto(idcardNo);
            if (base64Photo == null) {
                return ResultUtil.error("未找到船员照片");
            }
            return ResultUtil.data(base64Photo);
        } catch (Exception e) {
            log.error("获取船员照片失败，身份证号：{}", idcardNo, e);
            return ResultUtil.error("获取船员照片失败");
        }
    }

    /**
     * 插入船员照片
     */
    @SystemLog(description = "插入船员照片", type = LogType.OPERATION)
    @RequestMapping(value = "/photo/insert", method = RequestMethod.POST)
    @ApiOperation(value = "插入船员照片到数据库")
    public Result<Boolean> insertSeafarerPhoto(
            @ApiParam(value = "身份证号码", required = true) @RequestParam String idcardNo,
            @ApiParam(value = "本地图片文件路径", required = true) @RequestParam String filePath) {
        log.info("插入船员照片，身份证号：{}，文件路径：{}", idcardNo, filePath);
        
        try {
            boolean success = crewBasicInfoService.insertSeafarerPhoto(idcardNo, filePath);
            if (success) {
                return ResultUtil.data(true, "船员照片插入成功");
            } else {
                return ResultUtil.error("船员照片插入失败");
            }
        } catch (Exception e) {
            log.error("插入船员照片失败，身份证号：{}，文件路径：{}", idcardNo, filePath, e);
            return ResultUtil.error("插入船员照片失败：" + e.getMessage());
        }
    }

    /**
     * 获取标签值建议列表
     *
     * @param tagCode 标签编码
     * @param queryText 查询文本
     * @param limit 限制返回条数，默认10条
     * @return 标签值建议列表
     */
    @SystemLog(description = "标签值建议-查询建议列表", type = LogType.OPERATION)
    @RequestMapping(value = "/suggestions", method = RequestMethod.GET)
    @ApiOperation(value = "获取标签值建议列表", notes = "根据标签编码和查询文本获取匹配的标签值建议")
    public Result<List<TagValueSuggestionVo>> getSuggestions(
            @ApiParam(value = "标签编码", required = true) @RequestParam String tagCode,
            @ApiParam(value = "查询文本", required = true) @RequestParam String queryText,
            @ApiParam(value = "限制返回条数，默认10条，最多20条") @RequestParam(defaultValue = "10") Integer limit) {

        try {
            log.info("接收到标签值建议查询请求，tagCode={}, queryText={}, limit={}", tagCode, queryText, limit);

            List<TagValueSuggestionVo> suggestions = crewFeatureDictService.getTagValueSuggestions(tagCode, queryText, limit);

            log.info("标签值建议查询成功，返回{}条数据", suggestions.size());

            return ResultUtil.data(suggestions);

        } catch (Exception e) {
            log.error("标签值建议查询失败，tagCode={}, queryText={}, error={}", tagCode, queryText, e.getMessage(), e);
            return ResultUtil.error("查询标签值建议失败：" + e.getMessage());
        }
    }
} 