package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCrewFeature;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 船员特征表 Mapper 接口
 */
public interface DwdzCrewFeatureMapper extends BaseMapper<DwdzCrewFeature> {

    /**
     * 根据船员ID列表和触发类别代码前缀查询特征数据
     * 
     * @param crewIds            船员ID列表
     * @param categoryCodePrefix 触发类别代码前缀
     * @return 特征数据列表
     */
    List<DwdzCrewFeature> selectByCrewIdsAndCategoryCodePrefix(@Param("crewIds") List<String> crewIds,
            @Param("categoryCodePrefix") String categoryCodePrefix);

}