package com.js.hszpt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DwdzCrewCompanyType;
import com.js.hszpt.mapper.DwdzCrewCompanyTypeMapper;
import com.js.hszpt.service.DwdzCrewCompanyTypeService;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 公司类型字典表Service实现
 */
@Service
public class DwdzCrewCompanyTypeServiceImpl extends ServiceImpl<DwdzCrewCompanyTypeMapper, DwdzCrewCompanyType> implements DwdzCrewCompanyTypeService {
    
    /**
     * 获取所有启用的公司类型（按sort_order排序）
     * @return 启用的公司类型列表
     */
    @Override
    public List<DwdzCrewCompanyType> getEnabledTypes() {
        QueryWrapper<DwdzCrewCompanyType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_enabled", "t")
                   .orderByAsc("sort_order");
        return baseMapper.selectList(queryWrapper);
    }
} 