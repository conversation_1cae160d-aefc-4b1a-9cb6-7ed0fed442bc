<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewCompanyTypeMapper">
    <!-- 基础映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewCompanyType">
        <id column="company_type" property="companyType"/>
        <result column="company_type_name" property="companyTypeName"/>
        <result column="company_type_desc" property="companyTypeDesc"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="rec_create_date" property="recCreateDate"/>
        <result column="rec_modify_date" property="recModifyDate"/>
    </resultMap>
</mapper> 