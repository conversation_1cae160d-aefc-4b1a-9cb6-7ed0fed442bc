package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.entity.DwdzCppPromotionConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * 晋升配置表DAO接口
 * 提供晋升配置数据的数据库访问方法
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
@DS("dzzzdwdz")
public interface DwdzCppPromotionConfigDao extends BaseMapper<DwdzCppPromotionConfig> {

    /**
     * 获取最新的晋升配置
     * 
     * @return 晋升配置信息
     */
    DwdzCppPromotionConfig getLatestConfig();
} 