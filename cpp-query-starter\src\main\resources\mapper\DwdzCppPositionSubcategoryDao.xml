<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCppPositionSubcategoryDao">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCppPositionSubcategory">
        <id column="subcategory_id" property="subcategoryId" />
        <result column="category_id" property="categoryId" />
        <result column="flxxmc" property="flxxmc" />
        <result column="wzzwbm" property="wzzwbm" />
        <result column="wzzwjc" property="wzzwjc" />
        <result column="wzzwqc" property="wzzwqc" />
        <result column="srzwmc" property="srzwmc" />
        <result column="srzwmcbm" property="srzwmcbm" />
        <result column="djbm" property="djbm" />
        <result column="djmcjc" property="djmcjc" />
        <result column="djmcqc" property="djmcqc" />
        <result column="djxxxz" property="djxxxz" />
        <result column="hqbm" property="hqbm" />
        <result column="hqmcjc" property="hqmcjc" />
        <result column="hqmcqc" property="hqmcqc" />
        <result column="cert_type" property="certType" />
        <result column="person_count" property="personCount" />
        <result column="sort_order" property="sortOrder" />
        <result column="rec_create_date" property="recCreateDate" />
        <result column="rec_modify_date" property="recModifyDate" />
    </resultMap>

    <!-- 根据分类ID获取子分类列表 -->
    <select id="getSubcategoriesByCategoryId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
            subcategory_id,
            category_id,
            flxxmc,
            wzzwbm,
            wzzwjc,
            wzzwqc,
            srzwmc,
            srzwmcbm,
            djbm,
            djmcjc,
            djmcqc,
            djxxxz,
            hqbm,
            hqmcjc,
            hqmcqc,
            cert_type,
            person_count,
            sort_order,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_subcategory 
        WHERE category_id = #{categoryId}
        ORDER BY hqbm,djbm desc,srzwmcbm
    </select>

    <!-- 根据子分类名称获取子分类信息 -->
    <select id="getSubcategoryByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
            subcategory_id,
            category_id,
            flxxmc,
            wzzwbm,
            wzzwjc,
            wzzwqc,
            srzwmc,
            srzwmcbm,
            djbm,
            djmcjc,
            djmcqc,
            djxxxz,
            hqbm,
            hqmcjc,
            hqmcqc,
            cert_type,
            person_count,
            sort_order,
            rec_create_date,
            rec_modify_date
        FROM dwdz_crew_position_subcategory 
        WHERE flxxmc = #{subcategoryName}
        LIMIT 1
    </select>

    <!-- 根据职务分类查询子分类 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_position_subcategory 
        WHERE category_id = #{categoryId}
        ORDER BY sort_order ASC, flxxmc ASC
    </select>

    <!-- 根据子分类代码查询 -->
    <select id="selectBySubcategoryCode" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_position_subcategory 
        WHERE subcategory_id = #{subcategoryId}
        LIMIT 1
    </select>

    <!-- 获取所有子分类列表 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_position_subcategory 
        ORDER BY category_id ASC, sort_order ASC, flxxmc ASC
    </select>

    <!-- 获取人数分布统计 -->
    <select id="selectCountDistribution" resultType="java.util.Map">
        SELECT 
            CASE 
                WHEN person_count = 0 THEN '无人员'
                WHEN person_count BETWEEN 1 AND 100 THEN '1-100人'
                WHEN person_count BETWEEN 101 AND 500 THEN '101-500人'
                WHEN person_count BETWEEN 501 AND 1000 THEN '501-1000人'
                ELSE '1000人以上'
            END as "countRange",
            COUNT(*) as "subcategoryCount",
            SUM(person_count) as "totalPersons"
        FROM dwdz_crew_position_subcategory 
        GROUP BY 
            CASE 
                WHEN person_count = 0 THEN '无人员'
                WHEN person_count BETWEEN 1 AND 100 THEN '1-100人'
                WHEN person_count BETWEEN 101 AND 500 THEN '101-500人'
                WHEN person_count BETWEEN 501 AND 1000 THEN '501-1000人'
                ELSE '1000人以上'
            END
        ORDER BY MIN(person_count) ASC
    </select>

    <!-- 批量更新人员数量 -->
    <update id="batchUpdatePersonCount">
        UPDATE dwdz_crew_position_subcategory 
        SET person_count = #{personCount}, rec_modify_date = NOW()
        WHERE subcategory_id IN
        <foreach collection="subcategoryIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询职业路径相关的子分类 -->
    <select id="selectByCareerPath" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_position_subcategory 
        WHERE flxxmc IS NOT NULL 
        AND flxxmc != ''
        ORDER BY category_id ASC, sort_order ASC
    </select>

    <!-- 根据证书类别查询子分类 -->
    <select id="selectByCertType" resultMap="BaseResultMap">
        SELECT * FROM dwdz_crew_position_subcategory 
        WHERE cert_type = #{certType}
        ORDER BY category_id ASC, sort_order ASC, flxxmc ASC
    </select>

</mapper> 