package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 公司类型字典表
 */
@Data
@TableName("dwdz_crew_company_type")
public class CrewCompanyType {
    /**
     * 公司类型代码（主键）
     */
    @TableId("type_code")
    private String typeCode;

    /**
     * 类型显示名称
     */
    @TableField("type_name")
    private String typeName;

    /**
     * 界面显示颜色代码
     */
    @TableField("color_code")
    private String colorCode;

    /**
     * 界面显示图标类名
     */
    @TableField("icon_class")
    private String iconClass;

    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;

    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;
} 