package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.hszpt.entity.DwdzCrewCareerTrack;
import java.util.List;

/**
 * 船员职业生涯成长轨迹表Service接口
 * 提供成长轨迹的增删改查及按船员ID查询等功能
 */
public interface DwdzCrewCareerTrackService extends IService<DwdzCrewCareerTrack> {
    /**
     * 根据船员ID查询成长轨迹（按track_order升序）
     * @param crewId 船员ID
     * @return 成长轨迹列表
     */
    List<DwdzCrewCareerTrack> getCareerTrackByCrewId(String crewId);
} 