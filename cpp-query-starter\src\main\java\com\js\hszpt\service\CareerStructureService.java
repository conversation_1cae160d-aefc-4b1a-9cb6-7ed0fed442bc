package com.js.hszpt.service;

import java.util.Map;

/**
 * 船员职业结构服务接口
 * 提供职业路径、管理关系、晋升要求等功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface CareerStructureService {

    /**
     * 根据职务级别获取职务名称列表
     * 从dwdz_crew_position_category表中查询指定级别的职务
     * 
     * @param positionLevel 职务级别（management/operational/support）
     * @return 职务名称列表
     */
    Map<String, Object> getPositionsByLevel(String positionLevel);

    /**
     * 获取职业路径数据
     * 返回所有职业晋升路径配置数据
     * 
     * @return 职业路径数据
     */
    Map<String, Object> getCareerPaths();

    /**
     * 获取管理关系数据
     * 返回职位间的管理关系配置
     * 
     * @return 管理关系数据
     */
    Map<String, Object> getManagementRelations();

    /**
     * 获取职位详情
     * 根据职位ID获取详细信息和子分类
     * 
     * @param positionId 职位ID
     * @return 职位详情数据
     */
    Map<String, Object> getPositionDetails(String positionId);

    /**
     * 根据子分类ID获取晋升关系数据
     * 根据子分类ID获取晋升来源和目标关系
     * 
     * @param subcategoryId 子分类ID
     * @return 晋升关系数据
     */
    Map<String, Object> getPromotionRelationsBySubcategoryId(String subcategoryId);

    /**
     * 获取晋升要求详情
     * 根据目标职位获取详细晋升要求
     * 
     * @param targetPosition 目标职位名称
     * @return 晋升要求详情
     */
    Map<String, Object> getPromotionRequirements(String targetPosition);

    /**
     * 根据当前职务ID和目标职务ID获取晋升要求详情（新版本）
     * 基于新的表结构，使用两个ID精确查询晋升要求
     * 
     * @param dqSubcategoryId 当前证书职务分类id
     * @param mbSubcategoryId 晋升目标证书职务分类id
     * @param source 请求来源（intranet=内网，internet=外网），默认为内网
     * @return 晋升要求详情
     */
    Map<String, Object> getPromotionRequirementsByIds(String dqSubcategoryId, String mbSubcategoryId, String source);

    /**
     * 获取职务人数统计
     * 返回所有职务的人数统计映射
     * 
     * @return 职务人数统计
     */
    Map<String, Integer> getPositionCounts();

    /**
     * 获取单个职务人数
     * 根据职位ID获取具体人数
     * 
     * @param positionId 职位ID
     * @return 职务人数信息
     */
    Map<String, Object> getPositionCount(String positionId);

    /**
     * 获取路径选择器选项
     * 返回职业结构图中路径选择器的所有可选项
     * 
     * @return 路径选择器选项列表
     */
    Object getPathSelectorOptions();
} 