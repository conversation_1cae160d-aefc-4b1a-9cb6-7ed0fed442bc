<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwdzCrewSummaryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.js.hszpt.entity.DwdzCrewSummary">
        <id column="summary_id" property="summaryId" jdbcType="VARCHAR"/>
        <result column="crew_id" property="crewId" jdbcType="VARCHAR"/>
        <result column="career_start_year" property="careerStartYear" jdbcType="INTEGER"/>
        <result column="career_years" property="careerYears" jdbcType="INTEGER"/>
        <result column="show_career_flag" property="showCareerFlag" jdbcType="BOOLEAN"/>
        <result column="first_officer_date" property="firstOfficerDate" jdbcType="DATE"/>
        <result column="promotion_duration" property="promotionDuration" jdbcType="DECIMAL"/>
        <result column="peer_percentile" property="peerPercentile" jdbcType="DECIMAL"/>
        <result column="total_cooperation_companies" property="totalCooperationCompanies" jdbcType="INTEGER"/>
        <result column="current_cooperation_companies" property="currentCooperationCompanies" jdbcType="INTEGER"/>
        <result column="rec_create_date" property="recCreateDate" jdbcType="TIMESTAMP"/>
        <result column="rec_modify_date" property="recModifyDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        summary_id, crew_id, career_start_year, career_years, show_career_flag, 
        first_officer_date, promotion_duration, peer_percentile, 
        total_cooperation_companies, current_cooperation_companies, 
        rec_create_date, rec_modify_date
    </sql>

    <!-- 根据船员ID查询职业生涯汇总信息 -->
    <select id="selectByCrewId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_summary
        WHERE crew_id = #{crewId,jdbcType=VARCHAR}
    </select>

    <!-- 批量查询船员职业生涯汇总信息 -->
    <select id="selectByCrewIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM dwdz_crew_summary
        WHERE crew_id IN
        <foreach collection="list" item="crewId" open="(" separator="," close=")">
            #{crewId,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper> 