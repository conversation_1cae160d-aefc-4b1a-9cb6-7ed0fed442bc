package com.js.hszpt.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MessageRequest {
    private String content;              // 消息内容
    private List<String> cardList;       // 目标用户身份证号列表
    private Date validTime;              // 消息有效期
    private List<String> orgCodeList;    // 目标用户组织代码列表
    private String businessId;           // 业务主键
    private String type;                 // 消息类型
    private String source;               // 消息来源
}
